// import 'package:flutter/material.dart';

// class LoadingWidget extends StatelessWidget {
//   const LoadingWidget({
//     super.key,
//     required this.color,
//   });

//   final Color color;

//   @override
//   Widget build(BuildContext context) {
//     return Scaffold(
//       backgroundColor: color,
//       body: const Center(
//         child: CircularProgressIndicator(),
//       ),
//     );
//   }
// }
import 'package:flutter/material.dart';
import 'package:lottie/lottie.dart';

import '../../config/theme/app_assets.dart';

class LoadingWidget extends StatelessWidget {
  const LoadingWidget({
    super.key,
    required this.color,
  });

  final Color color;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: color,
      body: Center(
        child: Lottie.asset(
          AppAssets.lottieLoader,
          width: 48,
          height: 48,
          fit: BoxFit.contain,
        ),
      ),
    );
  }
}

class Loader extends StatelessWidget {
  const Loader({
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return Center(
      child: Lottie.asset(
        'assets/animations/g4l_loading_animation.json',
        width: 48,
        height: 48,
        fit: BoxFit.contain,
      ),
    );
  }
}
