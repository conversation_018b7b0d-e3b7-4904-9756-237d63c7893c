import 'package:flutter/material.dart';
import 'package:gotcha_mfg_app/core/extensions/app_text_theme_extension.dart';

import '../../config/theme/app_assets.dart';
import '../../config/theme/app_colors.dart';

class InfoMessage extends StatelessWidget {
  final String message;
  final Color color;
  final String asset;
  final bool? showIcon;

  const InfoMessage({
    super.key,
    required this.message,
    required this.color,
    this.asset = AppAssets.infomessage,
    this.showIcon = true,
  });

  @override
  Widget build(BuildContext context) {
    final textTheme = Theme.of(context).textTheme;

    return Container(
      padding: showIcon == false
          ? const EdgeInsets.symmetric(horizontal: 20, vertical: 16)
          : const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      decoration: BoxDecoration(
        color: color ?? AppColors.lightRed, // Light pink background
        borderRadius: BorderRadius.circular(20),
      ),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          if (showIcon == true)
            Image.asset(
              asset,
              scale: 4,
            ),
          // const Icon(
          //   Icons.info_outline,
          //   size: 20,
          //   color: AppColors.navy, // Dark blue/navy color
          // ),
          if (showIcon == true) const SizedBox(width: 12),
          Expanded(
            child: Text(
              message,
              style: textTheme.agLabels,
              maxLines: 16,
            ),
          ),
        ],
      ),
    );
  }
}
