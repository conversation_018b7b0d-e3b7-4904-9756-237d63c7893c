// import 'package:flutter/material.dart';
// import 'package:gotcha_mfg_app/config/theme/app_colors.dart';
// import 'package:gotcha_mfg_app/core/extensions/app_text_theme_extension.dart';

// class PrimaryButton extends StatelessWidget {
//   const PrimaryButton({
//     required this.text,
//     required this.onPressed,
//     this.isEnabled = true,
//     this.showShadowAbove = false,
//     super.key,
//   });

//   final String text;
//   final VoidCallback onPressed;
//   final bool isEnabled;
//   final bool showShadowAbove;

//   @override
//   Widget build(BuildContext context) {
//     return Container(
//       decoration: showShadowAbove
//           ? BoxDecoration(
//               boxShadow: [
//                 BoxShadow(
//                     blurRadius: 32,
//                     offset: const Offset(0, -10),
//                     color: Colors.grey.withOpacity(0.8),
//                     blurStyle: BlurStyle.normal),
//               ],
//             )
//           : null,
//       child: ElevatedButton(
//         onPressed: isEnabled ? onPressed : null,
//         style: ElevatedButton.styleFrom(
//           backgroundColor: isEnabled ? AppColors.coral : AppColors.grey,
//           padding: const EdgeInsets.symmetric(vertical: 16),
//           shape: RoundedRectangleBorder(
//             borderRadius: BorderRadius.circular(30),
//           ),
//         ),
//         child: Text(
//           text,
//           style: const TextStyle(
//             fontSize: 16,
//             color: Colors.white,
//           ),
//         ),
//       ),
//     );
//   }
// }

// class PrimaryButtonVariant extends StatelessWidget {
//   const PrimaryButtonVariant({
//     required this.text,
//     required this.onPressed,
//     this.isEnabled = true,
//     super.key,
//   });

//   final String text;
//   final VoidCallback onPressed;
//   final bool isEnabled;

//   @override
//   Widget build(BuildContext context) {
//     final textTheme = Theme.of(context).textTheme;
//     return ElevatedButton(
//       onPressed: isEnabled ? onPressed : null,
//       style: ElevatedButton.styleFrom(
//         backgroundColor: isEnabled ? AppColors.coral : AppColors.grey,
//         padding: const EdgeInsets.symmetric(vertical: 14),
//         shape: RoundedRectangleBorder(
//           borderRadius: BorderRadius.circular(16),
//         ),
//       ),
//       child: Column(
//         children: [
//           const Icon(
//             Icons.ac_unit,
//             color: Colors.white,
//           ),
//           Text(
//             text,
//             style: textTheme.ralewaySemiBold.copyWith(
//               fontSize: 14,
//               color: Colors.white,
//             ),
//           ),
//         ],
//       ),
//     );
//   }
// }

import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:gotcha_mfg_app/config/theme/app_assets.dart';
import 'package:gotcha_mfg_app/config/theme/app_colors.dart';
import 'package:gotcha_mfg_app/core/extensions/app_text_theme_extension.dart';

class PrimaryButton extends StatefulWidget {
  const PrimaryButton({
    required this.text,
    this.error = false,
    required this.onPressed,
    this.onErrorPressed,
    this.isEnabled = true,
    this.showShadowAbove = false,
    this.buttonColor,
    this.textColor,
    super.key,
    this.borderRadius,
  });

  final String text;
  final VoidCallback onPressed;
  final VoidCallback? onErrorPressed;
  final bool isEnabled;
  final bool error;
  final bool showShadowAbove;
  final Color? buttonColor;
  final Color? textColor;
  final double? borderRadius;

  @override
  State<PrimaryButton> createState() => _PrimaryButtonState();
}

class _PrimaryButtonState extends State<PrimaryButton>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _scaleAnimation;
  bool _isPressed = false;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      duration: const Duration(milliseconds: 150),
      vsync: this,
    );
    _scaleAnimation = Tween<double>(begin: 1.0, end: 0.95).animate(
      CurvedAnimation(parent: _controller, curve: Curves.easeInOut),
    );
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  void _handleTapDown(TapDownDetails details) {
    if (widget.isEnabled) {
      setState(() => _isPressed = true);
      _controller.forward();
    } else if (!widget.isEnabled &&
        widget.error &&
        widget.onErrorPressed != null) {
      // Still handle tap down animation even when disabled and error with callback
      setState(() => _isPressed = true);
      _controller.forward();
    }
  }

  void _handleTapUp(TapUpDetails details) {
    if (widget.isEnabled) {
      setState(() => _isPressed = false);
      _controller.reverse();
      widget.onPressed(); // Call the original onPressed when enabled
    } else if (!widget.isEnabled &&
        widget.error &&
        widget.onErrorPressed != null) {
      setState(() => _isPressed = false);
      _controller.reverse();
      widget
          .onErrorPressed!(); // Call the onErrorPressed when disabled and error
    } else if (!widget.isEnabled) {
      setState(() => _isPressed = false);
      _controller.reverse();
    }
  }

  void _handleTapCancel() {
    if (widget.isEnabled ||
        (!widget.isEnabled && widget.error && widget.onErrorPressed != null)) {
      setState(() => _isPressed = false);
      _controller.reverse();
    }
  }

  @override
  Widget build(BuildContext context) {
    // Define the active button color - use custom color if provided, otherwise default to AppColors.coral
    final Color activeColor = widget.buttonColor ?? AppColors.coral;

    // Define the text color - use custom color if provided, otherwise default to white
    final Color labelColor = widget.textColor ?? Colors.white;

    return GestureDetector(
      onTapDown: _handleTapDown,
      onTapUp: _handleTapUp,
      onTapCancel: _handleTapCancel,
      child: ScaleTransition(
        scale: _scaleAnimation,
        child: Container(
          height: 52,
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(widget.borderRadius ?? 30),
            color: Colors.white,
          ),
          child: ElevatedButton(
            onPressed: widget.isEnabled
                ? widget.onPressed
                : widget.error
                    ? widget.onErrorPressed
                    : null,
            style: ElevatedButton.styleFrom(
              elevation: 0,
              backgroundColor: widget.isEnabled
                  ? (_isPressed ? activeColor.withOpacity(0.64) : activeColor)
                  : const Color(0xFFE6E3E6),
              // padding: const EdgeInsets.symmetric(vertical: 16),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(widget.borderRadius ?? 30),
              ),
            ),
            child: Text(
              widget.text,
              style: Theme.of(context).textTheme.gothamBold.copyWith(
                    fontSize: 16,
                    color: widget.isEnabled ? labelColor : Colors.white,
                  ),
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
              textAlign: TextAlign.center,
            ),
          ),
        ),
      ),
    );
  }
}
// class PrimaryButton extends StatefulWidget {
//   const PrimaryButton({
//     required this.text,
//     this.error=false,
//     required this.onPressed,
//     this.isEnabled = true,
//     this.showShadowAbove = false,
//     super.key,
//   });

//   final String text;
//   final VoidCallback onPressed;
//   final bool isEnabled;
//   final bool error;
//   final bool showShadowAbove;

//   @override
//   State<PrimaryButton> createState() => _PrimaryButtonState();
// }

// class _PrimaryButtonState extends State<PrimaryButton>
//     with SingleTickerProviderStateMixin {
//   late AnimationController _controller;
//   late Animation<double> _scaleAnimation;
//   bool _isPressed = false;

//   @override
//   void initState() {
//     super.initState();
//     _controller = AnimationController(
//       duration: const Duration(milliseconds: 150),
//       vsync: this,
//     );
//     _scaleAnimation = Tween<double>(begin: 1.0, end: 0.95).animate(
//       CurvedAnimation(parent: _controller, curve: Curves.easeInOut),
//     );
//   }

//   @override
//   void dispose() {
//     _controller.dispose();
//     super.dispose();
//   }

//   void _handleTapDown(TapDownDetails details) {
//     if (widget.isEnabled) {
//       setState(() => _isPressed = true);
//       _controller.forward();
//     }
//   }

//   void _handleTapUp(TapUpDetails details) {
//     if (widget.isEnabled) {
//       setState(() => _isPressed = false);
//       _controller.reverse();
//     }
//   }

//   void _handleTapCancel() {
//     if (widget.isEnabled) {
//       setState(() => _isPressed = false);
//       _controller.reverse();
//     }
//   }

//   @override
//   Widget build(BuildContext context) {
//     return GestureDetector(
//       onTapDown: _handleTapDown,
//       onTapUp: _handleTapUp,
//       onTapCancel: _handleTapCancel,
//       child: ScaleTransition(
//         scale: _scaleAnimation,
//         child: Container(
//           decoration: widget.showShadowAbove
//               ? const BoxDecoration(
//                   boxShadow: [
//                     // BoxShadow(
//                     //   blurRadius: 32,
//                     //   offset: const Offset(0, -10),
//                     //   color: Colors.grey.withOpacity(0.8),
//                     //   blurStyle: BlurStyle.normal,
//                     // ),
//                   ],
//                 )
//               : null,
//           child: ElevatedButton(
//             onPressed: widget.isEnabled ? widget.onPressed : widget.error?(){

//             }:null,
//             style: ElevatedButton.styleFrom(
//               elevation: 0,
//               backgroundColor: widget.isEnabled
//                   ? (_isPressed
//                       ? AppColors.coral.withOpacity(0.64)
//                       : AppColors.coral)
//                   : AppColors.grey,
//               padding: const EdgeInsets.symmetric(vertical: 16),
//               shape: RoundedRectangleBorder(
//                 borderRadius: BorderRadius.circular(30),
//               ),
//             ),
//             child: Text(
//               widget.text,
//               style: Theme.of(context).textTheme.gothamBold.copyWith(
//                     fontSize: 16,
//                     color: Colors.white,
//                   ),
//             ),
//           ),
//         ),
//       ),
//     );
//   }
// }

class PrimaryButtonVariant extends StatefulWidget {
  const PrimaryButtonVariant({
    required this.text,
    required this.onPressed,
    this.isEnabled = true,
    super.key,
    required this.isProfessional,
  });

  final String text;
  final VoidCallback onPressed;
  final bool isEnabled;
  final bool isProfessional;

  @override
  State<PrimaryButtonVariant> createState() => _PrimaryButtonVariantState();
}

class _PrimaryButtonVariantState extends State<PrimaryButtonVariant>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _scaleAnimation;
  bool _isPressed = false;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      duration: const Duration(milliseconds: 150),
      vsync: this,
    );
    _scaleAnimation = Tween<double>(begin: 1.0, end: 0.95).animate(
      CurvedAnimation(parent: _controller, curve: Curves.easeInOut),
    );
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  void _handleTapDown(TapDownDetails details) {
    if (widget.isEnabled) {
      setState(() => _isPressed = true);
      _controller.forward();
    }
  }

  void _handleTapUp(TapUpDetails details) {
    if (widget.isEnabled) {
      setState(() => _isPressed = false);
      _controller.reverse();
    }
  }

  void _handleTapCancel() {
    if (widget.isEnabled) {
      setState(() => _isPressed = false);
      _controller.reverse();
    }
  }

  @override
  Widget build(BuildContext context) {
    final textTheme = Theme.of(context).textTheme;
    return GestureDetector(
      onTapDown: _handleTapDown,
      onTapUp: _handleTapUp,
      onTapCancel: _handleTapCancel,
      child: ScaleTransition(
          scale: _scaleAnimation,
          child: SizedBox(
            height: 80,
            child: ElevatedButton(
              onPressed: widget.isEnabled ? widget.onPressed : null,
              style: ElevatedButton.styleFrom(
                elevation: 0,
                backgroundColor: widget.isEnabled
                    ? (_isPressed ? AppColors.coral : Colors.white)
                    : AppColors.grey,
                padding: const EdgeInsets.symmetric(vertical: 18),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
              ),
              child: Center(
                child: Column(
                  // mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    widget.isProfessional
                        ? Image.asset(
                            AppAssets.professionalhelp,
                            color: AppColors.navy,
                            scale: 4,
                          )
                        : Image.asset(
                            AppAssets.morexercises,
                            color: AppColors.navy,
                            scale: 4,
                          ),
                    const Gap(6),
                    // const Icon(
                    //   Icons.help_outline_sharp,
                    //   color: AppColors.navy,
                    // ),
                    Expanded(
                      child: Text(
                        widget.text,
                        style:
                            textTheme.linkText.copyWith(color: AppColors.navy),
                        overflow: TextOverflow.ellipsis,
                        textAlign: TextAlign.center,
                      ),
                    ),
                  ],
                ),
              ),
            ),
          )),
    );
  }
}
