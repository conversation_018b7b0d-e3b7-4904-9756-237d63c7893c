import 'package:flutter/material.dart';

class RoundButton extends StatelessWidget {
  final VoidCallback onToggle;
  final Widget child;

  const RoundButton({
    super.key,
    required this.onToggle,
    required this.child,
  });

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onToggle,
      child: Container(
        decoration: const BoxDecoration(
          shape: BoxShape.circle,
          color: Colors.white30,
        ),
        padding: const EdgeInsets.all(8),
        child: child,
      ),
    );
  }
}
