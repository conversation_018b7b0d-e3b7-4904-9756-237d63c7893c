import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:gotcha_mfg_app/config/theme/app_colors.dart';
import 'package:gotcha_mfg_app/core/extensions/app_text_theme_extension.dart';

import '../../config/theme/app_assets.dart';

class NoData extends StatelessWidget {
  final TextTheme textTheme;

  const NoData({
    super.key,
    required this.textTheme,
  });

  @override
  Widget build(BuildContext context) {
    return Center(
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Image.asset(
            AppAssets.nogymicon,
            color: AppColors.navy,
            scale: 4,
          ),
          const Gap(8),
          Text(
            'No data yet',
            style: textTheme.ralewayRegular.copyWith(
              fontSize: 13,
              color: AppColors.navy,
            ),
          ),
        ],
      ),
    );
  }
}

class NoDataNoIcon extends StatelessWidget {
  final TextTheme textTheme;

  const NoDataNoIcon({
    super.key,
    required this.textTheme,
  });

  @override
  Widget build(BuildContext context) {
    return Center(
      child: Row(
        mainAxisAlignment: MainAxisAlignment.start,
        children: [
          Text(
            'No data yet',
            style: textTheme.ralewayRegular.copyWith(
              fontSize: 13,
              color: AppColors.navy,
            ),
          ),
        ],
      ),
    );
  }
}
