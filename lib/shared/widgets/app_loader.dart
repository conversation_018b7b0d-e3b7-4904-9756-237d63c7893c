// import 'package:flutter/material.dart';
// import 'package:loader_overlay/loader_overlay.dart';

// class AppLoader extends StatelessWidget {
//   const AppLoader({
//     required this.child,
//     super.key,
//   });

//   final Widget child;

//   @override
//   Widget build(BuildContext context) {
//     return LoaderOverlay(
//       overlayColor: Colors.white70,
//       child: child,
//     );
//   }
// }
