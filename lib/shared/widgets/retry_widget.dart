// import 'package:flutter/material.dart';
// import 'package:gap/gap.dart';
// import 'package:gotcha_mfg_app/config/theme/app_colors.dart';
// import 'package:gotcha_mfg_app/core/extensions/app_text_theme_extension.dart';

// typedef RetryCallback = void Function();

// class RetryWidget extends StatelessWidget {
//   const RetryWidget({
//     super.key,
//     required this.onRetry,
//     required this.color,
//   });

//   final RetryCallback onRetry;
//   final Color color;

//   @override
//   Widget build(BuildContext context) {
//     var textTheme = Theme.of(context).textTheme;
//     var size = MediaQuery.of(context).size;
//     return GestureDetector(
//       onTap: onRetry,
//       child: Scaffold(
//         backgroundColor: color,
//         body: ConstrainedBox(
//           constraints: BoxConstraints(maxHeight: size.height),
//           child: Center(
//             child: Column(
//               mainAxisAlignment: MainAxisAlignment.center,
//               children: [
//                 Text(
//                   "We're sorry, but something went wrong.",
//                   style: textTheme.gothamBold.copyWith(
//                     color: AppColors.navy,
//                     fontSize: 18,
//                   ),
//                   textAlign: TextAlign.center,
//                 ),
//                 const Gap(16),
//                 Row(
//                   mainAxisAlignment: MainAxisAlignment.center,
//                   children: [
//                     const Icon(Icons.replay_outlined),
//                     const Gap(8),
//                     Text(
//                       'Tap to retry',
//                       style: textTheme.ralewayMedium
//                           .copyWith(color: AppColors.navy),
//                     )
//                   ],
//                 ),
//                 const Gap(64),
//               ],
//             ),
//           ),
//         ),
//       ),
//     );
//   }
// }

import 'package:flutter/material.dart';
import 'package:gotcha_mfg_app/config/theme/app_assets.dart';
import 'package:gotcha_mfg_app/config/theme/app_colors.dart';
import 'package:gotcha_mfg_app/core/extensions/app_text_theme_extension.dart';
import 'package:gotcha_mfg_app/core/utils/snackbar_service.dart';
import 'package:gotcha_mfg_app/shared/widgets/primary_button.dart';

typedef RetryCallback = void Function();

class RetryWidget extends StatelessWidget {
  const RetryWidget({
    super.key,
    required this.onRetry,
    required this.color,
  });

  final VoidCallback onRetry;
  final Color color;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: color,
      body: SafeArea(
        child: Padding(
          padding: const EdgeInsets.only(left: 8, right: 8, top: 8, bottom: 8),
          child: Container(
            decoration: const BoxDecoration(
              color: AppColors.lightBlue,
              borderRadius: BorderRadius.only(
                topLeft: Radius.circular(30),
                topRight: Radius.circular(30),
                bottomLeft: Radius.circular(30),
                bottomRight: Radius.circular(30),
              ),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Padding(
                  padding: const EdgeInsets.only(left: 24.0, top: 24.0),
                  child: GestureDetector(
                    child: Image.asset(
                      AppAssets.arrowBack,
                      width: 24,
                      color: AppColors.coral,
                    ),
                    onTap: () => Navigator.of(context).canPop()
                        ? Navigator.of(context).pop()
                        : SnackBarService.error(
                            context: context,
                            message: 'Nowhere to go back to',
                          ),
                  ),
                ),
                Expanded(
                  child: Center(
                    child: Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 32.0),
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          const Spacer(flex: 2),
                          Text(
                            "We're sorry, but something went wrong",
                            style: Theme.of(context)
                                .textTheme
                                .sectionHeading
                                .copyWith(),
                          ),
                          const SizedBox(height: 32),
                          SizedBox(
                            width: double.infinity,
                            child: PrimaryButton(
                              onPressed: onRetry,
                              text: 'Tap to retry',
                            ),
                          ),
                          const Spacer(flex: 3),
                        ],
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}

class RetryWidgetMini extends StatelessWidget {
  const RetryWidgetMini({
    super.key,
    required this.onRetry,
    required this.color,
  });

  final VoidCallback onRetry;
  final Color color;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: color,
      body: SafeArea(
        child: Padding(
          padding: const EdgeInsets.only(left: 8, right: 8, top: 24),
          child: Container(
            decoration: const BoxDecoration(
              // color: AppColors.disabledGrey,
              borderRadius: BorderRadius.only(
                topLeft: Radius.circular(30),
                topRight: Radius.circular(30),
              ),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Expanded(
                  child: Center(
                    child: Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 32.0),
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          const Spacer(),
                          Text(
                            "We're sorry, but something went wrong",
                            style: Theme.of(context)
                                .textTheme
                                .sectionHeading
                                .copyWith(),
                            textAlign: TextAlign.center,
                          ),
                          const SizedBox(height: 32),
                          SizedBox(
                            width: double.infinity,
                            child: PrimaryButton(
                              onPressed: onRetry,
                              text: 'Tap to retry',
                            ),
                          ),
                          const Spacer(flex: 4),
                        ],
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
