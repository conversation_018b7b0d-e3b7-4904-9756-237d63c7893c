import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:gotcha_mfg_app/config/theme/app_assets.dart';
import 'package:gotcha_mfg_app/config/theme/app_colors.dart';
import 'package:gotcha_mfg_app/core/extensions/app_text_theme_extension.dart';
import 'package:gotcha_mfg_app/core/utils/platform_utils.dart';

class AppHeader extends StatelessWidget {
  const AppHeader({
    super.key,
    this.onBackTap,
    this.title,
    this.subTitle,
    this.showLogo = false,
    this.totalSteps = 0,
    this.currentStep = 0,
    this.trailing,
    this.subTitleStyle,
  }) : assert(
          !(showLogo && totalSteps > 0),
          'Cannot show both logo and progress bars simultaneously.',
        );

  final VoidCallback? onBackTap;
  final String? title;
  final String? subTitle;
  final bool showLogo;
  final String? trailing;
  final int totalSteps;
  final int currentStep;
  final TextStyle? subTitleStyle;

  static const _padding = EdgeInsets.symmetric(
    horizontal: 24,
    vertical: 16,
  );

  static const _borderRadius = BorderRadius.only(
    topLeft: Radius.circular(30),
    topRight: Radius.circular(30),
  );

  @override
  Widget build(BuildContext context) {
    final textTheme = Theme.of(context).textTheme;
    final size = MediaQuery.of(context).size;

    return Container(
      padding: _padding,
      decoration: const BoxDecoration(
        color: AppColors.navy,
        borderRadius: _borderRadius,
      ),
      child: LayoutBuilder(
        builder: (context, constraints) {
          // Calculate the maximum width for the entire widget
          final maxWidth = constraints.maxWidth;

          // Determine space needed for logo if showing
          final logoWidth = showLogo ? 88.0 + 16.0 /* for gap */ : 0.0;

          // Create the content widgets first (without constraining them)
          final contentColumn = Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisAlignment: MainAxisAlignment.center,
            mainAxisSize: MainAxisSize.min,
            children: [
              if (title != null) ...[
                const Gap(8),
                Row(
                  mainAxisSize: MainAxisSize.min,
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    if (onBackTap != null)
                      SizedBox(
                        child: Material(
                          color: Colors.transparent,
                          child: InkWell(
                            onTap: onBackTap,
                            borderRadius: BorderRadius.circular(4.0),
                            child: Padding(
                              padding: const EdgeInsets.fromLTRB(0, 4, 4, 4),
                              child: Image.asset(
                                AppAssets.arrowBack,
                                color: Colors.white,
                                width: 24,
                              ),
                            ),
                          ),
                        ),
                      ),
                    if (onBackTap != null) const Gap(8),
                    Flexible(
                      child: Text(
                        title!,
                        style: textTheme.sectionHeading.copyWith(
                          color: Colors.white,
                          fontSize: totalSteps > 2
                              ? isIos
                                  ? 18
                                  : 16
                              : isIos
                                  ? 18
                                  : 20,
                        ),
                      ),
                    ),
                  ],
                ),
                if (subTitle == null) const Gap(4),
              ],
              if (subTitle != null) ...[
                Text(
                  subTitle!,
                  style: subTitleStyle ??
                      textTheme.ralewayRegular.copyWith(
                        color: Colors.white,
                        fontSize: 12,
                      ),
                  softWrap: true,
                ),
                const Gap(6),
              ],
            ],
          );

          // Create trailing text if necessary
          final trailingWidget = trailing != null
              ? Padding(
                  padding: const EdgeInsets.only(top: 2, left: 8),
                  child: Text(
                    '$trailing',
                    style: textTheme.ralewaySemiBold.copyWith(
                      fontSize: 16,
                      color: Colors.white,
                    ),
                  ),
                )
              : const SizedBox();

          // Calculate min space needed for content (this is a rough estimate)
          // In a real app, you might need more precise measurements
          final contentMinWidth = MediaQuery.textScalerOf(context).scale(200.0);
          final trailingWidth = trailing != null
              ? MediaQuery.textScalerOf(context).scale(80.0)
              : 0.0;

          // Now calculate how much space is left for progress bars
          final remainingWidth =
              maxWidth - contentMinWidth - trailingWidth - logoWidth;
          final useProgressBars = totalSteps > 0 && !showLogo;

          // If not enough space, don't show progress bars or adjust them
          final progressBarSection = useProgressBars && remainingWidth > 50
              ? _buildProgressBars(size, remainingWidth)
              : const SizedBox();

          // Build the final row with proper priorities
          return Row(
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              // Content with priority
              Expanded(
                child: contentColumn,
              ),

              // Trailing text
              if (trailing != null) trailingWidget,

              // Logo with fixed width
              if (showLogo) ...[
                const Gap(16),
                Image.asset(
                  AppAssets.g4lMfg,
                  width: 92,
                ),
              ],

              // Progress bars with available space
              if (useProgressBars && remainingWidth > 50) ...[
                const Gap(16),
                progressBarSection,
              ],
            ],
          );
        },
      ),
    );
  }

  Widget _buildProgressBars(Size size, double availableWidth) {
    // Maximum width for each bar
    final double maxBarWidth =
        totalSteps > 2 ? size.width * 0.075 : size.width * 0.10;

    // Calculate width per bar based on available space
    const double spaceBetweenBars = 4.0;
    final double totalSpacingWidth = spaceBetweenBars * (totalSteps - 1);

    // Calculate how many bars we can fully show
    final int barsWithFullWidth =
        ((availableWidth - totalSpacingWidth) / maxBarWidth).floor();

    // If we can show all bars at full width
    if (barsWithFullWidth >= totalSteps) {
      return _buildFullSizeBars(maxBarWidth, spaceBetweenBars);
    }

    // Otherwise, adjust the bars to fit
    final double adjustedBarWidth =
        (availableWidth - totalSpacingWidth) / totalSteps;

    return _buildAdjustedBars(
        adjustedBarWidth.clamp(2.0, maxBarWidth), spaceBetweenBars);
  }

  Widget _buildFullSizeBars(double barWidth, double spacing) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: List.generate(
        totalSteps,
        (index) => Padding(
          padding: EdgeInsets.only(left: index > 0 ? spacing : 0, top: 4),
          child: Container(
            height: 4,
            width: barWidth,
            decoration: BoxDecoration(
              color: index < currentStep ? AppColors.coral : AppColors.grey,
              borderRadius: BorderRadius.circular(10),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildAdjustedBars(double barWidth, double spacing) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: List.generate(
        totalSteps,
        (index) => Padding(
          padding: EdgeInsets.only(left: index > 0 ? spacing : 0, top: 4),
          child: Container(
            height: 4,
            width: barWidth,
            decoration: BoxDecoration(
              color: index < currentStep ? AppColors.coral : AppColors.grey,
              borderRadius: BorderRadius.circular(10),
            ),
          ),
        ),
      ),
    );
  }
}
