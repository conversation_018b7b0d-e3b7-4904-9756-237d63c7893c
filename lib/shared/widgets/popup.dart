import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:gotcha_mfg_app/config/theme/app_colors.dart';

import '../../core/utils/platform_utils.dart';

class CustomCupertinoAlertDialog {
  static Future<bool?> yesOrNoPopup(
    BuildContext context, {
    required String title,
    required String content,
    String yesButtonText = 'Yes',
    String noButtonText = 'No',
    required VoidCallback onYes,
    required VoidCallback onNo,
  }) async {
    return showCupertinoDialog<bool>(
      context: context,
      builder: (BuildContext context) {
        return AnnotatedRegion<SystemUiOverlayStyle>(
          value: const SystemUiOverlayStyle(
            statusBarColor: Colors.transparent,
            systemNavigationBarColor: Colors.transparent,
          ),
          child: CupertinoAlertDialog(
            title: Text(
              title,
              style: const TextStyle(color: AppColors.navy),
            ),
            content: Padding(
              padding: EdgeInsets.only(top: isIos ? 8 : 8),
              child: Text(
                content,
                style: const TextStyle(color: AppColors.navy),
                // style: Theme.of(context).textTheme.bodyRegular,
              ),
            ),
            actions: <Widget>[
              CupertinoDialogAction(
                isDestructiveAction: true,
                textStyle: const TextStyle(color: AppColors.coral),
                // textStyle: Theme.of(context).textTheme.bodyRegular,
                onPressed: () {
                  onNo(); // Execute the No callback
                },
                child: Text(
                  noButtonText,
                  // style: Theme.of(context)
                  //     .textTheme
                  //     .bodyRegular
                  //     .copyWith(color: AppColors.navy),
                ),
              ),
              CupertinoDialogAction(
                isDefaultAction: true,
                textStyle: const TextStyle(color: AppColors.navy),
                // textStyle: Theme.of(context).textTheme.bodyEmphasis,
                onPressed: () {
                  Navigator.of(context).pop(true);
                  onYes();
                },
                child: Text(
                  yesButtonText,
                  // style: Theme.of(context).textTheme.bodyEmphasis,
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  static Future<void> showAlertPopup(
    BuildContext context, {
    required String title,
    required String content,
    String okButtonText = 'Ok',
    VoidCallback? onOk,
  }) async {
    return showCupertinoDialog<void>(
      context: context,
      builder: (BuildContext context) {
        return AnnotatedRegion<SystemUiOverlayStyle>(
          value: const SystemUiOverlayStyle(
            statusBarColor: Colors.transparent,
            systemNavigationBarColor: Colors.transparent,
          ),
          child: CupertinoAlertDialog(
            title: Padding(
              padding: EdgeInsets.only(bottom: isIos ? 8 : 8),
              child: Text(
                title,
                style: const TextStyle(color: AppColors.navy),
                // style: Theme.of(context).textTheme.bodyEmphasis,
              ),
            ),
            content: Text(
              content,
              style: const TextStyle(color: AppColors.navy),
              // style: Theme.of(context).textTheme.bodyRegular,
            ),
            actions: <Widget>[
              CupertinoDialogAction(
                isDefaultAction: true,
                // isDestructiveAction: true,
                // textStyle: Theme.of(context).textTheme.bodyEmphasis,
                textStyle: const TextStyle(color: AppColors.navy),
                onPressed: () {
                  Navigator.of(context).pop();
                  if (onOk != null) {
                    onOk();
                  }
                },
                child: Text(
                  okButtonText,
                  // style: Theme.of(context)
                  //     .textTheme
                  //     .bodyEmphasis
                  //     .copyWith(color: AppColors.navy),
                ),
              ),
            ],
          ),
        );
      },
    );
  }
}
