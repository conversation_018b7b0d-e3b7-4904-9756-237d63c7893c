import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:gotcha_mfg_app/config/theme/app_assets.dart';
import 'package:gotcha_mfg_app/config/theme/app_colors.dart';

import 'loading_widget.dart';

// class NetworkImageWithIndicator extends StatelessWidget {
//   final String imageUrl;

//   final BoxFit? fit;

//   const NetworkImageWithIndicator({
//     super.key,
//     required this.imageUrl,
//     this.fit = BoxFit.cover,
//   });

//   @override
//   Widget build(BuildContext context) {
//     return Image.network(
//       imageUrl,
//       fit: fit,
//       loadingBuilder: (BuildContext context, Widget child,
//           ImageChunkEvent? loadingProgress) {
//         if (loadingProgress == null) {
//           return child;
//         }

//         return Container(
//           color: AppColors.midBlue,
//           child: const Center(
//             child: CupertinoActivityIndicator(color: AppColors.navy),
//           ),
//         );
//       },
//       errorBuilder:
//           (BuildContext context, Object error, StackTrace? stackTrace) {
//         return Container(
//           color: AppColors.midBlue,
//           child: const Icon(
//             Icons.image_not_supported_outlined,
//             color: AppColors.navy,
//           ),
//         );
//       },
//     );
//   }
// }

class NetworkImageWithIndicator extends StatelessWidget {
  final String imageUrl;
  final BoxFit? fit;

  const NetworkImageWithIndicator({
    super.key,
    required this.imageUrl,
    this.fit = BoxFit.cover,
  });

  @override
  Widget build(BuildContext context) {
    return CachedNetworkImage(
      imageUrl: imageUrl,
      fit: fit,
      placeholder: (context, url) => Container(
        color: AppColors.midBlue,
        child: const Center(
          child: LoadingWidget(color: AppColors.midBlue),
        ),
      ),
      // errorWidget: (context, url, error) => Container(
      //   color: AppColors.midBlue,
      //   child: const Icon(
      //     Icons.image_not_supported_outlined,
      //     color: AppColors.navy,
      //   ),
      // ),
      errorWidget: (context, url, error) => Image.asset(
        AppAssets.placeholder,
        fit: BoxFit.cover,
        // color: AppColors.navy,
      ),
    );
  }
}

class NetworkImageWithIndicatorSmall extends StatelessWidget {
  final String imageUrl;
  final BoxFit? fit;

  const NetworkImageWithIndicatorSmall({
    super.key,
    required this.imageUrl,
    this.fit = BoxFit.cover,
  });

  @override
  Widget build(BuildContext context) {
    return CachedNetworkImage(
      imageUrl: imageUrl,
      fit: fit,
      // placeholder: (context, url) => Container(
      //   color: AppColors.midBlue,
      //   child: const Center(
      //     child: CupertinoActivityIndicator(color: AppColors.navy),
      //   ),
      // ),
      errorWidget: (context, url, error) => Container(
        color: Colors.transparent,
        child: const Icon(
          Icons.image_not_supported_outlined,
          color: AppColors.navy,
          size: 16,
        ),
      ),
    );
  }
}
