import 'package:flutter/material.dart';

class CurvedSeparator extends StatelessWidget {
  final Color outerColor;
  final Color innerColor;
  final double height;

  const CurvedSeparator({
    super.key,
    required this.outerColor,
    required this.innerColor,
    this.height = 28,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        Row(
          children: [
            SpaceFixerHorizontalLine(
              context: context,
              overflowColor: outerColor,
              overflowHeight: 0.5,
              width: height,
            ),
            Expanded(
              child: SpaceFixerHorizontalLine(
                context: context,
                overflowColor: innerColor,
                overflowHeight: 0,
              ),
            ),
            SpaceFixerHorizontalLine(
              context: context,
              overflowColor: outerColor,
              overflowHeight: 0.5,
              width: height,
            ),
          ],
        ),
        Container(
          height: height,
          color: outerColor,
          child: Container(
            decoration: BoxDecoration(
              color: innerColor,
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(30),
                topRight: Radius.circular(30),
              ),
              // border: Border(
              //   top: BorderSide(color: outerColor, width: 0.0),
              //   // left: BorderSide(color: outerColor, width: 1.0),
              //   // right: BorderSide(color: outerColor, width: 1.0),
              //   bottom: BorderSide(color: innerColor, width: 0.0),
              // ),
            ),
          ),
        ),
        SpaceFixerHorizontalLine(
          context: context,
          overflowColor: innerColor,
          overflowHeight: 0.5,
        ),
      ],
    );
  }
}

class SpaceFixerHorizontalLine extends StatelessWidget {
  SpaceFixerHorizontalLine({
    super.key,
    required this.context,
    required this.overflowColor,
    this.width,
    required this.overflowHeight,
  }) {
    width ??= MediaQuery.of(context).size.width;
  }

  BuildContext context;
  Color overflowColor;
  double? width;
  double overflowHeight;

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      width: width,
      height: 0,
      child: Stack(
        clipBehavior: Clip.none,
        alignment: AlignmentDirectional.center,
        children: [
          Positioned(
            width: width,
            height: overflowHeight,
            child: Container(
              width: width,
              height: overflowHeight,
              color: overflowColor,
            ),
          ),
        ],
      ),
    );
  }
}

class SpaceFixerVerticalLine extends StatelessWidget {
  SpaceFixerVerticalLine({
    super.key,
    required this.context,
    required this.overflowColor,
    this.overflowWidth = 2,
    this.height,
  }) {
    height ??= MediaQuery.of(context).size.height;
  }

  BuildContext context;
  Color overflowColor;
  double overflowWidth;
  double? height;

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      width: 0,
      height: height,
      child: Stack(
        clipBehavior: Clip.none,
        alignment: AlignmentDirectional.center,
        children: [
          Positioned(
            width: overflowWidth,
            height: height,
            child: Container(
              width: overflowWidth,
              height: height,
              color: overflowColor,
            ),
          ),
        ],
      ),
    );
  }
}
