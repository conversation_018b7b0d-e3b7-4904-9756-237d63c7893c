/// A generic class to handle operation results with success and failure states
class Result<T> {
  const Result._({
    this.data,
    this.error,
  });

  // Creates a success result with data
  factory Result.success(T data) {
    return Result._(data: data);
  }

  // Creates a failure result with error message
  factory Result.failure(String error) {
    return Result._(error: error);
  }
  final T? data;
  final String? error;

  // Helper method to check if result is success
  bool get isSuccess => error == null;

  // Helper method to check if result is failure
  bool get isFailure => error != null;

  // Pattern matching for different states
  R when<R>({
    required R Function(T data) success,
    required R Function(String error) failure,
  }) {
    if (isSuccess) {
      return success(data as T);
    } else {
      return failure(error!);
    }
  }
}
