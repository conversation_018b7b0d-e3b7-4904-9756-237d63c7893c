import 'dart:convert';

class RefreshTokenResponse {
  final String? message;
  final String? status;
  final Data? data;

  RefreshTokenResponse({
    this.message,
    this.status,
    this.data,
  });

  RefreshTokenResponse copyWith({
    String? message,
    String? status,
    Data? data,
  }) =>
      RefreshTokenResponse(
        message: message ?? this.message,
        status: status ?? this.status,
        data: data ?? this.data,
      );

  factory RefreshTokenResponse.fromRawJson(String str) =>
      RefreshTokenResponse.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory RefreshTokenResponse.fromJson(Map<String, dynamic> json) =>
      RefreshTokenResponse(
        message: json["message"],
        status: json["status"],
        data: json["data"] == null ? null : Data.fromJson(json["data"]),
      );

  Map<String, dynamic> toJson() => {
        "message": message,
        "status": status,
        "data": data?.toJson(),
      };
}

class Data {
  final String? accessToken;
  final String? refreshToken;
  final String? userId;

  Data({
    this.accessToken,
    this.refreshToken,
    this.userId,
  });

  Data copyWith({
    String? accessToken,
    String? refreshToken,
    String? userId,
  }) =>
      Data(
        accessToken: accessToken ?? this.accessToken,
        refreshToken: refreshToken ?? this.refreshToken,
        userId: userId ?? this.userId,
      );

  factory Data.fromRawJson(String str) => Data.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory Data.fromJson(Map<String, dynamic> json) => Data(
        accessToken: json["access_token"],
        refreshToken: json["refresh_token"],
        userId: json["user_id"],
      );

  Map<String, dynamic> toJson() => {
        "access_token": accessToken,
        "refresh_token": refreshToken,
        "user_id": userId,
      };
}
