import 'dart:convert';

class GenericResponse {
  String? message;
  String? status;
  dynamic data;

  GenericResponse({
    this.message,
    this.status,
    this.data,
  });

  factory GenericResponse.fromRawJson(String str) =>
      GenericResponse.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory GenericResponse.fromJson(Map<String, dynamic> json) =>
      GenericResponse(
        message: json["message"],
        status: json["status"],
        data: json["data"],
      );

  Map<String, dynamic> toJson() => {
        "message": message,
        "status": status,
        "data": data,
      };
}
