import 'package:auto_route/auto_route.dart';
import 'package:flutter/material.dart';
import 'package:gotcha_mfg_app/config/router/app_router.gr.dart';

final GlobalKey<NavigatorState> navigatorKey = GlobalKey<NavigatorState>();

@AutoRouterConfig(replaceInRouteName: 'Screen|Page,Route')
class AppRouter extends RootStackRouter {
  AppRouter({GlobalKey<NavigatorState>? navigatorKey})
      : super(navigatorKey: navigatorKey ?? GlobalKey<NavigatorState>());

  var transition = TransitionsBuilders.fadeIn;

  @override
  RouteType get defaultRouteType => const RouteType.cupertino();
// @AutoRouterConfig(replaceInRouteName: 'Screen|Page,Route')
// class AppRouter extends RootStackRouter {
//   var transition = TransitionsBuilders.fadeIn;

//   @override
//   RouteType get defaultRouteType => const RouteType.cupertino();

  @override
  List<AutoRoute> get routes => [
        AutoRoute(
          page: SplashRoute.page,
          initial: true,
          // transitionsBuilder: transition,
        ),
        AutoRoute(
          page: HomeRoute.page,
          path: '/home',
          // transitionsBuilder: transition,
        ),
        AutoRoute(
          page: WelcomeFeedRoute.page,
          // transitionsBuilder: transition,
        ),
        AutoRoute(
          page: OnboardingWelcomeRoute.page,
          // transitionsBuilder: transition,
        ),
        AutoRoute(
          page: OnboardingFeelingRoute.page,
          // transitionsBuilder: transition,
        ),
        AutoRoute(
          page: OnBoardingThanksRoute.page,
          // transitionsBuilder: transition,
        ),
        AutoRoute(
          page: OnboardingDealingWithRoute.page,
          // transitionsBuilder: transition,
        ),
        AutoRoute(
          page: OnboardingMentalBoostsRoute.page,
          // transitionsBuilder: transition,
        ),
        AutoRoute(
          page: ExerciseFeedbackRoute.page,
          // transitionsBuilder: transition,
        ),
        // AutoRoute(
        //   page: ReflectionCompleteRoute.page,
        //   // transitionsBuilder: transition,
        // ),
        // AutoRoute(
        //   page: ReflectionMultiRoute.page,
        //   // transitionsBuilder: transition,
        // ),
        // AutoRoute(
        //   page: ReflectionTextRoute.page,
        //   // transitionsBuilder: transition,
        // ),
        AutoRoute(
          page: ForgotPasswordRoute.page,
          // transitionsBuilder: transition,
        ),
        AutoRoute(
          page: WorkoutRoute.page,
          // transitionsBuilder: transition,
        ),
        AutoRoute(
          page: FavouritesRoute.page,
          // transitionsBuilder: transition,
        ),
        AutoRoute(
          page: LoginRoute.page,
          // transitionsBuilder: transition,
        ),
        AutoRoute(
          page: SignUpRoute.page,
          // transitionsBuilder: transition,
        ),
        AutoRoute(
          page: ProfileRoute.page,
          path: '/profile',
          // transitionsBuilder: transition,
        ),
        AutoRoute(
          page: PersonaliseAccountRoute.page,
          // transitionsBuilder: transition,
        ),
        AutoRoute(
          page: FeedBackResultsRoute.page,
          // transitionsBuilder: transition,
        ),
        AutoRoute(
          page: GotchaWebViewRoute.page,
          // transitionsBuilder: transition,
        ),
        AutoRoute(
          page: GymHistoryRoute.page,
          // transitionsBuilder: transition,
        ),
        AutoRoute(
          page: SelfAssesmentWelcomeRoute.page,
          // transitionsBuilder: transition,
        ),
        AutoRoute(
          page: SelfAssessmentStartRoute.page,
          // transitionsBuilder: transition,
        ),
        AutoRoute(
          page: HelpSeekingPathwayRoute.page,
          // transitionsBuilder: transition,
        ),
        AutoRoute(
          page: VillageHomeRoute.page,
          path: '/village',

          // transitionsBuilder: transition,
        ),
        AutoRoute(
          page: SelfAssesmentResultRoute.page,
          // transitionsBuilder: transition,
        ),
        AutoRoute(
          page: FeedBackRoute.page,
          // transitionsBuilder: transition,
        ),
        AutoRoute(
          page: NotificationAllowRoute.page,
          // transitionsBuilder: transition,
        ),
        // AutoRoute(
        //   page: ImageTextRoute.page,
        //   // transitionsBuilder: transition,
        // ),
        AutoRoute(
          page: ReflectionCompleteNewRoute.page,
          // transitionsBuilder: transition,
        ),
        AutoRoute(
          page: ReflectionMultiNewRoute.page,
          // transitionsBuilder: transition,
        ),
        AutoRoute(
          page: ReflectionTextNewRoute.page,
          // transitionsBuilder: transition,
        ),
      ];

  @override
  List<AutoRouteGuard> get guards => [
        // Add route guards here
      ];

  void pushRoute(HomeRoute homeRoute) {}
}
