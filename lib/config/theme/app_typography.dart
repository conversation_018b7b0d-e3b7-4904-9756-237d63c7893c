import 'dart:io';

import 'package:flutter/material.dart';
import 'package:gotcha_mfg_app/config/theme/app_colors.dart';

/// Class holding the font family names used in the app
abstract class AppFonts {
  /// The font family name for the Gotham Bold font
  static const String gothamBold = 'Gotham-Bold';

  /// The font family name for the Gotham Medium font
  static const String gothamMedium = 'Gotham-Medium';

  /// The font family name for the Raleway font
  static const String raleway = 'Raleway';
}

/// Typography class containing the font styles used in the app
abstract class AppTypography {
  /// Bold Gotham font style
  static const TextStyle gothamBold = TextStyle(
    fontFamily: AppFonts.gothamBold,
    fontSize: 24,
    color: AppColors.navy,
  );

  /// Medium Gotham font style
  static const TextStyle gothamMedium = TextStyle(
    fontFamily: AppFonts.gothamMedium,
    fontSize: 18,
    color: AppColors.navy,
  );

  /// Semi-Bold Raleway font style
  static const TextStyle ralewaySemiBold = TextStyle(
    fontFamily: AppFonts.raleway,
    fontWeight: FontWeight.w600,
    fontSize: 18,
    color: AppColors.navy,
  );

  /// Bold Raleway font style
  static const TextStyle ralewayBold = TextStyle(
    fontFamily: AppFonts.raleway,
    fontWeight: FontWeight.w700,
    fontSize: 18,
    color: AppColors.navy,
  );

  /// Medium Raleway font style
  static const TextStyle ralewayMedium = TextStyle(
    fontFamily: AppFonts.raleway,
    fontWeight: FontWeight.w500,
    fontSize: 16,
    color: AppColors.navy,
  );

  /// Regular Raleway font style
  static const TextStyle ralewayRegular = TextStyle(
    fontFamily: AppFonts.raleway,
    fontWeight: FontWeight.w400,
    fontSize: 14,
    color: AppColors.navy,
  );

  /// Light Raleway font style
  static const TextStyle ralewayLight = TextStyle(
    fontFamily: AppFonts.raleway,
    fontWeight: FontWeight.w300,
    fontSize: 14,
    color: AppColors.navy,
  );

  //////////////////////////////////////////////////////////////////////////////

  static var isIos = Platform.isIOS;

  static TextStyle primaryHeading = TextStyle(
    fontFamily: AppFonts.gothamMedium,
    fontSize: isIos ? 24 : 20,
    color: AppColors.navy,
  );

  static TextStyle sectionHeading = TextStyle(
    fontFamily: AppFonts.gothamMedium,
    fontSize: isIos ? 20 : 18,
    color: AppColors.navy,
  );

  static TextStyle bodyEmphasis = TextStyle(
    fontFamily: AppFonts.raleway,
    fontWeight: FontWeight.w600,
    fontSize: isIos ? 17 : 14,
    color: AppColors.navy,
  );

  static TextStyle bodyRegular = TextStyle(
    fontFamily: AppFonts.raleway,
    fontWeight: FontWeight.w400,
    fontSize: isIos ? 17 : 14,
    color: AppColors.navy,
  );

  static TextStyle labels = TextStyle(
    fontFamily: AppFonts.raleway,
    fontWeight: FontWeight.w500,
    fontSize: isIos ? 13 : 12,
    color: AppColors.navy,
  );

  static TextStyle agLabels = TextStyle(
    fontFamily: AppFonts.raleway,
    fontWeight: FontWeight.w500,
    fontSize: isIos ? 13 : 12,
    color: AppColors.navy,
  );

  static TextStyle placeholder = TextStyle(
    fontFamily: AppFonts.raleway,
    fontWeight: FontWeight.w400,
    fontSize: isIos ? 13 : 12,
    color: AppColors.navy,
  );

  static TextStyle linkText = TextStyle(
    fontFamily: AppFonts.raleway,
    fontWeight: FontWeight.w700,
    fontSize: isIos ? 13 : 12,
    color: AppColors.navy,
  );

  static TextStyle labelsBold = TextStyle(
    fontFamily: AppFonts.raleway,
    fontWeight: FontWeight.w700,
    fontSize: isIos ? 13 : 12,
    color: AppColors.navy,
  );
}
