import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:gotcha_mfg_app/config/theme/app_colors.dart';
import 'package:gotcha_mfg_app/config/theme/app_typography.dart';

/// Application theme definition
///
/// Provides a light theme with a white background and a seed color for the
/// color scheme.
abstract class AppTheme {
  /// Light theme
  ///
  /// The light theme is the default theme used by the application.
  static ThemeData get light {
    return ThemeData(
      appBarTheme: const AppBarTheme(
        elevation: 0,
        scrolledUnderElevation: 0,
        surfaceTintColor: Colors.transparent,
        color: Colors.white,
        titleTextStyle: AppTypography.gothamBold,
        systemOverlayStyle: SystemUiOverlayStyle(
          statusBarColor: Colors.white,
          statusBarIconBrightness: Brightness.dark,
          systemNavigationBarColor: Colors.white,
          systemNavigationBarIconBrightness: Brightness.dark,
        ),
      ),
      fontFamily: AppFonts.gothamBold,
      scaffoldBackgroundColor: Colors.white,
      primaryColor: AppColors.navy,
      splashColor: Colors.transparent,
      colorScheme: ColorScheme.fromSeed(seedColor: AppColors.coral),
      progressIndicatorTheme: const ProgressIndicatorThemeData(
        color: AppColors.coral,
      ),
      textTheme: const TextTheme(
        headlineLarge: AppTypography.gothamBold,
        headlineMedium: AppTypography.gothamBold,
        titleLarge: AppTypography.gothamBold,
        bodyLarge: AppTypography.gothamBold,
        bodyMedium: AppTypography.gothamBold,
      ),
    );
  }
}
