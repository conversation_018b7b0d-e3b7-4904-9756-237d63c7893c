import 'package:flutter/foundation.dart';

class AppPrint {
  static void log(dynamic message) {
    if (kDebugMode) {
      print('🔍 DEBUG: $message');
    }
  }

  static void error(dynamic message) {
    if (kDebugMode) {
      print('❌ ERROR: $message');
    }
  }

  static void warning(dynamic message) {
    if (kDebugMode) {
      print('⚠️ WARNING: $message');
    }
  }

  static void success(dynamic message) {
    if (kDebugMode) {
      print('✅ SUCCESS: $message');
    }
  }
}

void info(dynamic message) {
  if (kDebugMode) {
    print('✅ INFO: $message');
  }
}
