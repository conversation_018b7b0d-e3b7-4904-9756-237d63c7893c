import 'package:flutter/material.dart';
import 'package:toastification/toastification.dart';

class ToastService {
  // Private constructor to prevent direct instantiation
  ToastService._();

  // Static method to show different types of toasts
  static void show({
    required String message,
    ToastificationType type = ToastificationType.success,
    ToastificationStyle style = ToastificationStyle.flat,
    Duration duration = const Duration(seconds: 3),
  }) {
    toastification.show(
      type: type,
      style: style,
      title: Text(message),
      autoCloseDuration: duration,
      alignment: Alignment.bottomCenter,
      description: null,
      animationDuration: const Duration(milliseconds: 300),
      showProgressBar: false,
    );
  }

  // Specific methods for different toast types
  static void success({
    required String message,
    Duration duration = const Duration(seconds: 3),
  }) {
    show(
      message: message,
      type: ToastificationType.success,
      duration: duration,
    );
  }

  static void error({
    required String message,
    Duration duration = const Duration(seconds: 3),
  }) {
    show(
      message: message,
      type: ToastificationType.error,
      duration: duration,
    );
  }

  static void warning({
    required String message,
    Duration duration = const Duration(seconds: 3),
  }) {
    show(
      message: message,
      type: ToastificationType.warning,
      duration: duration,
    );
  }

  static void info({
    required String message,
    Duration duration = const Duration(seconds: 3),
  }) {
    show(
      message: message,
      type: ToastificationType.info,
      duration: duration,
    );
  }
}
