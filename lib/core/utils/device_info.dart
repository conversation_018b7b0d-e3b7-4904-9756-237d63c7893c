import 'dart:io';
import 'package:android_id/android_id.dart';
import 'package:device_info_plus/device_info_plus.dart';
import 'package:gotcha_mfg_app/core/utils/app_print.dart';

Future<String?> getDeviceId() async {
  var deviceInfo = DeviceInfoPlugin();
  if (Platform.isIOS) {
    var iosDeviceInfo = await deviceInfo.iosInfo;
    info(iosDeviceInfo.identifierForVendor);

    return iosDeviceInfo.identifierForVendor;
  } else if (Platform.isAndroid) {
    const androidIdPlugin = AndroidId();
    final String? androidId = await androidIdPlugin.getId();
    info(androidId);
    return androidId;
  }
  return null;
}

Future<String?> getDeviceName() async {
  var deviceInfo = DeviceInfoPlugin();
  if (Platform.isIOS) {
    var iosDeviceInfo = await deviceInfo.iosInfo;
    return iosDeviceInfo.name;
  } else if (Platform.isAndroid) {
    var androidDeviceInfo = await deviceInfo.androidInfo;
    return androidDeviceInfo.model;
  }
  return null;
}

Future<String?> getDeviceVersion() async {
  var deviceInfo = DeviceInfoPlugin();
  if (Platform.isIOS) {
    var iosDeviceInfo = await deviceInfo.iosInfo;
    return iosDeviceInfo.systemVersion;
  } else if (Platform.isAndroid) {
    var androidDeviceInfo = await deviceInfo.androidInfo;
    return androidDeviceInfo.version.release;
  }
  return null;
}
