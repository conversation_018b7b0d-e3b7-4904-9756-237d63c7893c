// import 'package:flutter/material.dart';
// import 'package:gotcha_mfg_app/config/theme/app_assets.dart';
// import 'package:lottie/lottie.dart';
// import 'package:custom_refresh_indicator/custom_refresh_indicator.dart';

// class LottieRefreshIndicator extends StatefulWidget {
//   final Widget child;
//   final Future<bool> Function() onRefresh;

//   const LottieRefreshIndicator({
//     Key? key,
//     required this.child,
//     required this.onRefresh
//   }) : super(key: key);

//   @override
//   _LottieRefreshIndicatorState createState() => _LottieRefreshIndicatorState();
// }

// class _LottieRefreshIndicatorState extends State<LottieRefreshIndicator> with SingleTickerProviderStateMixin {
//   @override
//   Widget build(BuildContext context) {
//     return CustomRefreshIndicator(
//       // Customize the trigger offset for refresh
//       // triggerOffset: 100,

//       // Customize the refresh indicator height
//       // indicatorHeight: 100,

//       // Required builder argument
//       builder: (BuildContext context, Widget child, IndicatorController controller) {
//         return Stack(
//           children: [
//             // Main child content
//             child,

//             // Custom indicator positioned at the top
//             if (controller.isDragging || controller.isLoading)
//               Positioned(
//                 top: 0,
//                 left: 0,
//                 right: 0,
//                 child: Transform.scale(
//                   scale: controller.value,
//                   child: Center(
//                     child: Stack(
//                       alignment: Alignment.center,
//                       children: [
//                         // Lottie animation
//                         // Replace 'assets/loader.json' with your Lottie file path
//                         Lottie.asset(
//                           AppAssets.lottieLoader,
//                           width: 200,
//                           height: 200,
//                           // Control Lottie animation based on refresh progress
//                           controller: controller.isLoading
//                             ? null  // Use default animation when loading
//                             : AnimationController(
//                                 value: controller.value,
//                                 vsync: this  // Use SingleTickerProviderStateMixin
//                               ),
//                         ),

//                         // Optional: Add text or additional widgets
//                         if (controller.isLoading)
//                           Text(
//                             'Refreshing...',
//                             style: TextStyle(
//                               color: Colors.blue,
//                               fontWeight: FontWeight.bold,
//                             ),
//                           ),
//                       ],
//                     ),
//                   ),
//                 ),
//               ),
//           ],
//         );
//       },

//       // Pass the child
//       child: widget.child,

//       // Async refresh function
//       onRefresh: widget.onRefresh,
//     );
//   }
// }

// import 'package:flutter/material.dart';
// import 'package:gotcha_mfg_app/config/theme/app_assets.dart';
// import 'package:lottie/lottie.dart';
// import 'package:custom_refresh_indicator/custom_refresh_indicator.dart';

// class LottieRefreshIndicator extends StatefulWidget {
//   final Widget child;
//   final Future<bool> Function() onRefresh;

//   const LottieRefreshIndicator({
//     Key? key,
//     required this.child,
//     required this.onRefresh
//   }) : super(key: key);

//   @override
//   _LottieRefreshIndicatorState createState() => _LottieRefreshIndicatorState();
// }

// class _LottieRefreshIndicatorState extends State<LottieRefreshIndicator> with SingleTickerProviderStateMixin {
//   late AnimationController _animationController;
//   late Animation<double> _animation;

//   @override
//   void initState() {
//     super.initState();
//     // Create a single AnimationController for the Lottie animation
//     _animationController = AnimationController(
//       vsync: this,
//       duration: Duration(seconds: 2),
//     );

//     // Create a Tween animation for smooth progression
//     _animation = Tween<double>(begin: 0, end: 1).animate(
//       CurvedAnimation(
//         parent: _animationController,
//         curve: Curves.easeInOut,
//       ),
//     );
//   }

//   @override
//   void dispose() {
//     // Always dispose of the AnimationController
//     _animationController.dispose();
//     super.dispose();
//   }

//   @override
//   Widget build(BuildContext context) {
//     return CustomRefreshIndicator(
//       // triggerOffset: 100,
//       // indicatorHeight: 100,

//       builder: (BuildContext context, Widget child, IndicatorController controller) {
//         // Trigger animation based on controller state
//         if (controller.isLoading) {
//           _animationController.repeat();
//         } else {
//           _animationController.stop();
//         }

//         return Stack(
//           children: [
//             child,

//             if (controller.isDragging || controller.isLoading)
//               Positioned(
//                 top: 0,
//                 left: 0,
//                 right: 0,
//                 child: Transform.scale(
//                   scale: controller.value,
//                   child: Center(
//                     child: Stack(
//                       alignment: Alignment.center,
//                       children: [
//                         // Lottie animation
//                         Lottie.asset(
//                           AppAssets.lottieLoader,
//                           width: 200,
//                           height: 200,
//                           controller: _animationController,
//                         ),

//                         if (controller.isLoading)
//                           Text(
//                             'Refreshing...',
//                             style: TextStyle(
//                               color: Colors.blue,
//                               fontWeight: FontWeight.bold,
//                             ),
//                           ),
//                       ],
//                     ),
//                   ),
//                 ),
//               ),
//           ],
//         );
//       },

//       child: widget.child,

//       onRefresh: widget.onRefresh,
//     );
//   }
// }
