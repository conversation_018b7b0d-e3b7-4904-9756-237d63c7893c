import 'package:dio/dio.dart';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import 'package:gotcha_mfg_app/core/utils/app_print.dart';
import 'package:shared_preferences/shared_preferences.dart';

Future<void> appFirstInstallCheck() async {
  // if (!isIos) return;

  final prefs = await SharedPreferences.getInstance();

  if (prefs.getBool('first_run') ?? true) {
    FlutterSecureStorage storage = const FlutterSecureStorage();
    await storage.deleteAll();
    prefs.setBool('first_run', false);
    pixelTrack();
  }
}

void pixelTrack() {
  var dio = Dio();
  try {
    dio.get(
        'https://mgln.ai/view.gif?token=01958ffbc9c57bdeaa4cbca5fd58b0b2&url=https%3A%2F%2Fwww.gotcha4life.org%2Finstall');
  } catch (e) {
    info(e);
  }
}
