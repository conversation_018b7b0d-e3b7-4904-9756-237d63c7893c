// import 'package:flutter/material.dart';
// import 'package:gotcha_mfg_app/config/theme/app_colors.dart';
// import 'package:gotcha_mfg_app/core/extensions/app_text_theme_extension.dart';

// class SnackBarService {
//   // Private constructor to prevent direct instantiation
//   SnackBarService._();

//   static void show({
//     required BuildContext context,
//     required String message,
//     Color? backgroundColor,
//     SnackBarType type = SnackBarType.info,
//   }) {
//     // Remove any existing SnackBars
//     ScaffoldMessenger.of(context).removeCurrentSnackBar();

//     // Determine background color based on type
//     final color = _getColorByType(type, backgroundColor);

//     final textTheme = Theme.of(context).textTheme;

//     ScaffoldMessenger.of(context).showSnackBar(
//       SnackBar(
//         elevation: 0,
//         content: Text(
//           message,
//           style: textTheme.bodyRegular.copyWith(
//             color: Colors.white,
//             fontSize: 12,
//           ),
//           overflow: TextOverflow.ellipsis,
//           maxLines: 2,
//           textAlign: TextAlign.center,
//         ),
//         backgroundColor: color,
//         behavior: SnackBarBehavior.floating,
//         margin: const EdgeInsets.symmetric(horizontal: 32, vertical: 8),
//         duration: const Duration(seconds: 3),
//         shape: RoundedRectangleBorder(
//           borderRadius: BorderRadius.circular(32),
//           side: const BorderSide(
//             color: Colors.transparent,
//           ),
//         ),
//         padding: const EdgeInsets.symmetric(
//           horizontal: 12,
//           vertical: 12,
//         ),
//       ),
//     );
//   }

//   static Color _getColorByType(SnackBarType type, Color? customColor) {
//     if (customColor != null) return customColor;

//     switch (type) {
//       case SnackBarType.success:
//         return Colors.green;
//       case SnackBarType.error:
//         return AppColors.coral;
//       case SnackBarType.warning:
//         return Colors.orange;
//       case SnackBarType.info:
//         return AppColors.navy;
//       default:
//         return AppColors.navy;
//     }
//   }

//   // Convenience methods for specific types
//   static void success({
//     required BuildContext context,
//     required String message,
//   }) {
//     show(context: context, message: message, type: SnackBarType.success);
//   }

//   static void error({
//     required BuildContext context,
//     required String message,
//   }) {
//     show(context: context, message: message, type: SnackBarType.error);
//   }

//   static void warning({
//     required BuildContext context,
//     required String message,
//   }) {
//     show(context: context, message: message, type: SnackBarType.warning);
//   }

//   static void info({
//     required BuildContext context,
//     required String message,
//   }) {
//     show(context: context, message: message, type: SnackBarType.info);
//   }
// }

// // Enum for SnackBar types
// enum SnackBarType { success, error, warning, info }
import 'package:flutter/material.dart';
// Assuming these imports are correct for your project
import 'package:gotcha_mfg_app/config/theme/app_colors.dart';
import 'package:gotcha_mfg_app/core/extensions/app_text_theme_extension.dart';

class SnackBarService {
  // Private constructor to prevent direct instantiation
  SnackBarService._();

  static void show({
    required BuildContext context,
    required String message,
    Color? backgroundColor,
    SnackBarType type = SnackBarType.info,
    double? positionFromBottom, // <-- New optional parameter
  }) {
    // Remove any existing SnackBars
    ScaffoldMessenger.of(context).removeCurrentSnackBar();

    // Determine background color based on type
    final color = _getColorByType(type, backgroundColor);

    final textTheme = Theme.of(context).textTheme;

    // Determine the vertical margin
    final double verticalMargin =
        positionFromBottom ?? 8.0; // <-- Use parameter or default

    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        elevation: 0,
        content: Text(
          message,
          // Assuming bodyRegular exists via your extension
          style: textTheme.bodyRegular.copyWith(
            color: Colors.white,
            fontSize: 12,
          ),
          overflow: TextOverflow.ellipsis,
          maxLines: 2,
          textAlign: TextAlign.center,
        ),
        backgroundColor: color,
        behavior: SnackBarBehavior.floating,
        // Use the calculated vertical margin
        margin: EdgeInsets.symmetric(
          horizontal: 32,
          vertical: verticalMargin, // <-- Apply calculated margin
        ),
        duration: const Duration(seconds: 3),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(32),
          side: const BorderSide(
            color: Colors.transparent,
          ),
        ),
        padding: const EdgeInsets.symmetric(
          horizontal: 12,
          vertical: 12,
        ),
      ),
    );
  }

  static Color _getColorByType(SnackBarType type, Color? customColor) {
    if (customColor != null) return customColor;

    switch (type) {
      case SnackBarType.success:
        return Colors.green; // Consider using AppColors if defined
      case SnackBarType.error:
        return AppColors.coral;
      case SnackBarType.warning:
        return Colors.orange; // Consider using AppColors if defined
      case SnackBarType.info:
      default: // Combined default case
        return AppColors.navy;
    }
  }

  // Convenience methods for specific types
  static void success({
    required BuildContext context,
    required String message,
    // You could add positionFromBottom here too if needed in the future
  }) {
    show(
      context: context,
      message: message,
      type: SnackBarType.success,
      // positionFromBottom: positionFromBottom, // If added above
    );
  }

  static void error({
    required BuildContext context,
    required String message,
    double? positionFromBottom, // <-- Add optional parameter here
  }) {
    show(
      context: context,
      message: message,
      type: SnackBarType.error,
      positionFromBottom:
          positionFromBottom, // <-- Pass it to the main show method
    );
  }

  static void warning({
    required BuildContext context,
    required String message,
    // You could add positionFromBottom here too if needed in the future
  }) {
    show(
      context: context,
      message: message,
      type: SnackBarType.warning,
      // positionFromBottom: positionFromBottom, // If added above
    );
  }

  static void info({
    required BuildContext context,
    required String message,
    // You could add positionFromBottom here too if needed in the future
  }) {
    show(
      context: context,
      message: message,
      type: SnackBarType.info,
      // positionFromBottom: positionFromBottom, // If added above
    );
  }
}

// Enum for SnackBar types
enum SnackBarType { success, error, warning, info }

// --- Example Usage ---
/*
void showErrorExample(BuildContext context) {
  // Default position (margin vertical: 8)
  SnackBarService.error(context: context, message: "An error occurred.");

  // Custom position (margin vertical: 50)
  SnackBarService.error(
    context: context,
    message: "Another error, positioned higher!",
    positionFromBottom: 50.0,
  );
}
*/
