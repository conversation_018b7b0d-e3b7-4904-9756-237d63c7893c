// ignore_for_file: use_build_context_synchronously

import 'package:gotcha_mfg_app/core/utils/snackbar_service.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:flutter/material.dart'; // Import for ScaffoldMessenger, optional but recommended for showing error messages

class UrlLauncher {
  /// Launches a URL. Handles various URL schemes (http, https, tel).
  ///
  /// - [url]: The URL string to launch.
  /// - [context]: Optional BuildContext to show error SnackBar. If null, error messages will be printed to console.
  /// - [mode]: LaunchMode to control how the URL is launched. Defaults to LaunchMode.platformDefault.
  /// - [webOnlyWindowName]: Web only window name.
  ///
  /// Returns `true` if the URL was launched successfully, `false` otherwise.
  static Future<bool> launchURL(
    String url, {
    required BuildContext context,
    LaunchMode mode = LaunchMode.platformDefault,
    String? webOnlyWindowName,
  }) async {
    final Uri uri = Uri.parse(url);
    if (await canLaunchUrl(uri)) {
      try {
        return await launchUrl(
          uri,
          mode: mode,
          webOnlyWindowName: webOnlyWindowName,
        );
      } catch (e) {
        SnackBarService.error(
          context: context,
          message: 'Could not launch URL: $url\nError: $e',
        );
        return false;
      }
    } else {
      SnackBarService.error(
        context: context,
        message:
            'Could not launch URL: $url\nUnsupported URL scheme or no handler found.',
      );
      return false;
    }
  }

  /// Launches a web URL (http or https).
  ///
  /// - [url]: The web URL string to launch.
  /// - [context]: Optional BuildContext to show error SnackBar.
  /// - [mode]: LaunchMode for web URLs.
  /// - [webOnlyWindowName]: Web only window name.
  static Future<bool> launchWebURL(
    String url, {
    required BuildContext context,
    LaunchMode mode = LaunchMode.platformDefault,
    String? webOnlyWindowName,
  }) async {
    if (!url.startsWith('http://') && !url.startsWith('https://')) {
      url = 'https://$url'; // Default to https if no scheme is provided
    }
    return launchURL(url,
        context: context, mode: mode, webOnlyWindowName: webOnlyWindowName);
  }

  /// Launches the phone dialer with a phone number.
  ///
  /// - [phoneNumber]: The phone number to dial.
  /// - [context]: Optional BuildContext to show error SnackBar.
  static Future<bool> launchPhone(
    String phoneNumber, {
    required BuildContext context,
  }) async {
    final Uri phoneUri = Uri(
      scheme: 'tel',
      path: phoneNumber,
    );
    return launchURL(phoneUri.toString(), context: context);
  }
}
