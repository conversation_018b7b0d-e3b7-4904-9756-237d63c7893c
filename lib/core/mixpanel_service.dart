import 'dart:async';

import 'package:gotcha_mfg_app/core/utils/app_print.dart';
import 'package:gotcha_mfg_app/features/exercise/data/models/reflection_request.dart';
import 'package:mixpanel_flutter/mixpanel_flutter.dart';

import '../features/home/<USER>/models/check_in_request.dart';
// Import material.dart

class MixpanelService {
  late Mixpanel _mixpanel;
  String?
      _currentUserId; // Track current user ID to prevent duplicate identify calls
  bool _isInitialized = false;
  bool _isIdentifying = false; // Prevent concurrent identify calls
  final Completer<void> _initCompleter = Completer<void>();

  Future<void> init(String token) async {
    try {
      _mixpanel = await Mixpanel.init(token, trackAutomaticEvents: true);
      _isInitialized = true;
      _initCompleter.complete();
      info('Mixpanel initialized successfully');
      // Opt-out of tracking for testing/debugging
      // _mixpanel.setOptOutTracking(true);
    } catch (e) {
      info('Error initializing Mixpanel: $e');
      _initCompleter.completeError(e);
      rethrow;
    }
  }

  /// Ensures Mixpanel is initialized before performing operations
  Future<void> _ensureInitialized() async {
    if (!_isInitialized) {
      await _initCompleter.future;
    }
  }

  /// Identifies a user following Mixpanel best practices
  /// This method is async to handle potential race conditions properly
  Future<void> identify(String userId) async {
    // Validate input
    if (userId.isEmpty) {
      info('Cannot identify user with empty userId');
      return;
    }

    // Ensure Mixpanel is initialized
    await _ensureInitialized();

    // Check if we're already identified with this user ID
    if (_currentUserId == userId) {
      info('User already identified with ID: $userId');
      return;
    }

    // Prevent concurrent identify calls
    if (_isIdentifying) {
      info(
          'Identify operation already in progress, skipping for userId: $userId');
      return;
    }

    _isIdentifying = true;

    try {
      // For Mixpanel's Simplified ID Merge API, we should NOT call reset() before identify()
      // unless we're switching users. The SDK handles the ID merging automatically.

      // If we have a different user, we need to reset first
      if (_currentUserId != null && _currentUserId != userId) {
        info('Switching from user $_currentUserId to $userId');
        await _performReset();
      }

      // Call identify - this will merge the device ID with the user ID
      _mixpanel.identify(userId);

      // Set user properties
      _mixpanel.getPeople().set("User ID", userId);

      // Update our tracking
      _currentUserId = userId;

      info('Successfully identified user: $userId');
    } catch (e) {
      info('Error identifying user $userId: $e');

      // Handle the specific error case
      if (e.toString().contains('erranondistinctidassignedalready')) {
        info('Distinct ID already assigned error - attempting recovery...');
        await _handleDistinctIdError(userId);
      } else {
        // For other errors, just log and continue
        info('Unexpected error during identify: $e');
      }
    } finally {
      _isIdentifying = false;
    }
  }

  /// Handles the distinct ID already assigned error
  Future<void> _handleDistinctIdError(String userId) async {
    try {
      // Reset the session and try again
      await _performReset();

      // Wait a brief moment for the reset to take effect
      await Future.delayed(const Duration(milliseconds: 100));

      // Try to identify again
      _mixpanel.identify(userId);
      _mixpanel.getPeople().set("User ID", userId);
      _currentUserId = userId;

      info('Successfully recovered from distinct ID error for user: $userId');
    } catch (retryError) {
      info('Failed to recover from distinct ID error: $retryError');
      // Clear our state since we couldn't identify
      _currentUserId = null;
    }
  }

  /// Performs a reset operation
  Future<void> _performReset() async {
    try {
      _mixpanel.reset();
      _currentUserId = null;
      info('Mixpanel session reset completed');
    } catch (e) {
      info('Error during reset: $e');
    }
  }

  /// Resets the Mixpanel session - call this when user logs out
  Future<void> reset() async {
    await _ensureInitialized();
    await _performReset();
  }

  /// Safely switches from one user to another
  /// This is the recommended method for user switching scenarios
  Future<void> switchUser(String newUserId) async {
    if (newUserId.isEmpty) {
      info('Cannot switch to user with empty userId');
      return;
    }

    await _ensureInitialized();

    if (_currentUserId != null && _currentUserId != newUserId) {
      info('Switching user from $_currentUserId to $newUserId');
      // Reset the session before identifying a new user
      await reset();
      // Small delay to ensure reset is processed
      await Future.delayed(const Duration(milliseconds: 100));
    }

    await identify(newUserId);
  }

  /// Sets user properties - ensures user is identified first
  Future<void> setUserProperties(Map<String, dynamic> properties) async {
    await _ensureInitialized();

    try {
      final people = _mixpanel.getPeople();
      properties.forEach((key, value) {
        people.set(key, value);
      });
      info('User properties set successfully');
    } catch (e) {
      info('Error setting user properties: $e');
    }
  }

  /// Tracks screen view events
  Future<void> trackScreenView(String screenName,
      {Map<String, dynamic>? properties}) async {
    await _ensureInitialized();

    try {
      final allProperties = {
        'Screen Name': screenName,
        if (properties != null) ...properties,
      };

      _mixpanel.track('Screen View', properties: allProperties);
      _mixpanel.flush();
    } catch (e) {
      info('Error tracking screen view: $e');
    }
  }

  void trackSearchQuery(String pageName, String queryName) {
    try {
      final searchProperties = {
        'Search Query': queryName,
        'Screen Name': pageName,
      };

      _mixpanel.track('Search Performed', properties: searchProperties);
      _mixpanel.flush();
    } catch (e) {
      info('Error tracking search query: $e');
    }
  }

  void trackSubmissionWithOptions(
    String pageName, {
    Map<String, dynamic>? properties,
    List<Question>? questions,
    List<Answer>? answers,
    Map<String, String>? feedbackNames,
  }) {
    try {
      final allProperties = {
        'Screen Name': pageName,
        if (properties != null) ...properties,
      };

      // Map question ID and answer ID to properties
      if (questions != null && questions.isNotEmpty) {
        for (int i = 0; i < questions.length; i++) {
          final question = questions[i];

          if (question.questionId != null) {
            allProperties['Question ID ${i + 1}'] = question.questionId;
          }

          if (question.answer != null) {
            allProperties['Answer Text ${i + 1}'] = question.answer;
          }

          if (question.answerId != null) {
            allProperties['Answer ID ${i + 1}'] = question.answerId;
          }
        }
      }

      // Map Answer data to properties
      if (answers != null && answers.isNotEmpty) {
        for (int i = 0; i < answers.length; i++) {
          final answer = answers[i];

          if (answer.questionId != null) {
            allProperties["Question ID ${i + 1}"] = answer.questionId;
          }

          if (answer.selectedChoiceId != null) {
            allProperties['Selected Choice ID ${i + 1}'] =
                answer.selectedChoiceId;
          }

          if (answer.answerText != null) {
            allProperties['Answer Text ${i + 1}'] = answer.answerText;
          }
        }
      }

      // Add feedback names and values
      if (feedbackNames != null && feedbackNames.isNotEmpty) {
        feedbackNames.forEach((name, value) {
          allProperties[name] = value;
        });
      }

      _mixpanel.track('Options Submitted', properties: allProperties);
      _mixpanel.flush();
    } catch (e) {
      info('Error tracking submission: $e');
    }
  }

  void trackCheckInWithOptions(
    String pageName, {
    Map<String, dynamic>? properties,
    List<Question>? questions,
  }) {
    try {
      final allProperties = {
        'Screen Name': pageName,
        if (properties != null) ...properties,
      };

      // Map question ID and answer ID to properties
      if (questions != null && questions.isNotEmpty) {
        for (int i = 0; i < questions.length; i++) {
          final question = questions[i];
          allProperties['Check In Option ${i + 1}'] = question.toJson();
        }
      }

      _mixpanel.track('Check In Options Selected', properties: allProperties);
      _mixpanel.flush();
    } catch (e) {
      info('Error tracking check-in: $e');
    }
  }

  void trackTextAnimation(String exerciseId, String pageName,
      {Map<String, dynamic>? additionalProperties}) {
    try {
      // Create properties map with required fields
      final Map<String, dynamic> properties = {
        'Exercise ID': exerciseId,
        'Page Name': pageName,
        'Timestamp': DateTime.now().toIso8601String(),
      };

      // Add any additional properties if provided
      if (additionalProperties != null) {
        properties.addAll(additionalProperties);
      }

      // Track the event using the existing trackScreenView method
      trackScreenView('Text Animation', properties: properties);
    } catch (e) {
      info('Error tracking text animation: $e');
    }
  }

  void trackButtonClick(String buttonName, {Map<String, dynamic>? properties}) {
    try {
      final allProperties = {
        'Button Name': buttonName,
        if (properties != null) ...properties,
      };

      _mixpanel.track('Button Click', properties: allProperties);
      _mixpanel.flush();
    } catch (e) {
      info('Error tracking button click: $e');
    }
  }

  void trackEvent(String eventName, {Map<String, dynamic>? properties}) {
    try {
      _mixpanel.track(eventName, properties: properties);
      _mixpanel.flush();
    } catch (e) {
      info('Error tracking event: $e');
    }
  }

  void trackNotificationPermission(bool isGranted, String platform,
      {Map<String, dynamic>? properties}) {
    try {
      final allProperties = {
        'Platform': platform,
        'Status': isGranted ? 'Granted' : 'Denied',
        'Timestamp': DateTime.now().toIso8601String(),
        if (properties != null) ...properties,
      };

      _mixpanel.track('Notification Permission Status',
          properties: allProperties);
      _mixpanel.flush();
    } catch (e) {
      info('Error tracking notification permission: $e');
    }
  }

  void trackPushNotification(String notificationType,
      {Map<String, dynamic>? properties}) {
    try {
      final allProperties = {
        'Notification Type': notificationType,
        'Timestamp': DateTime.now().toIso8601String(),
        if (properties != null) ...properties,
      };

      _mixpanel.track('Push Notification Received', properties: allProperties);
      _mixpanel.flush();
    } catch (e) {
      info('Error tracking push notification: $e');
    }
  }

  // Utility method to get current user ID
  String? getCurrentUserId() {
    return _currentUserId;
  }

  // Method to check if user is already identified
  bool isUserIdentified(String userId) {
    return _currentUserId == userId;
  }

  // Method to check if Mixpanel is initialized
  bool get isInitialized => _isInitialized;

  // Method to check if an identify operation is in progress
  bool get isIdentifying => _isIdentifying;

  /// Safely identifies a user on app startup when user is already logged in
  /// This method should be used when the app starts and the user is already authenticated
  Future<void> identifyOnStartup(String userId) async {
    if (userId.isEmpty) {
      info('Cannot identify on startup with empty userId');
      return;
    }

    await _ensureInitialized();

    // On startup, we don't need to reset - just identify if not already identified
    if (_currentUserId != userId) {
      info('Identifying user on startup: $userId');
      await identify(userId);
    } else {
      info('User already identified on startup: $userId');
    }
  }

  /// Safely handles user login scenario
  /// This method should be used when a user logs in
  Future<void> identifyOnLogin(String userId) async {
    if (userId.isEmpty) {
      info('Cannot identify on login with empty userId');
      return;
    }

    await _ensureInitialized();

    // On login, we might need to switch users
    if (_currentUserId != null && _currentUserId != userId) {
      info('User login detected - switching users');
      await switchUser(userId);
    } else {
      info('User login detected - identifying user: $userId');
      await identify(userId);
    }
  }

  /// Safely handles user logout scenario
  /// This method should be used when a user logs out
  Future<void> handleLogout() async {
    await _ensureInitialized();

    if (_currentUserId != null) {
      info(
          'User logout detected - resetting session for user: $_currentUserId');
      await reset();
    } else {
      info('User logout detected but no user was identified');
    }
  }
}
