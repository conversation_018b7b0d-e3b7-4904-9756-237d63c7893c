import 'package:firebase_crashlytics/firebase_crashlytics.dart';
import 'package:flutter/material.dart';

class CrashlyticsService {
  Future<void> recordError(dynamic exception, StackTrace stack,
      {dynamic reason, bool fatal = false}) async {
    await FirebaseCrashlytics.instance
        .recordError(exception, stack, reason: reason, fatal: fatal);
  }

  void recordFlutterError(FlutterErrorDetails flutterErrorDetails) {
    FirebaseCrashlytics.instance.recordFlutterFatalError(flutterErrorDetails);
  }

  Future<void> setUserIdentifier(String userId) async {
    await FirebaseCrashlytics.instance.setUserIdentifier(userId);
  }

  Future<void> setCustomKey(String key, dynamic value) async {
    await FirebaseCrashlytics.instance.setCustomKey(key, value);
  }

  Future<void> log(String message) async {
    await FirebaseCrashlytics.instance.log(message);
  }

  void crash() {
    FirebaseCrashlytics.instance.crash();
  }
}
