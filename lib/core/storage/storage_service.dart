import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import 'package:shared_preferences/shared_preferences.dart';

/// Interface for storage service
abstract class StorageService {
  /// Writes a secure data to the secure storage.
  Future<void> writeSecureData(String key, String value);

  /// Reads a secure data from the secure storage.
  Future<String?> readSecureData(String key);

  /// Deletes a secure data from the secure storage.
  Future<void> deleteSecureData(String key);

  /// Writes a data to the shared preferences.
  Future<void> writeData(String key, dynamic value);

  /// Reads a data from the shared preferences.
  Future<T?> readData<T>(String key);

  /// Deletes a data from the shared preferences.
  Future<void> deleteData(String key);

  /// Clears all data from both secure storage and shared preferences.
  Future<void> clearAll();

  Future<void> clearSecureStorage();
}

/// Implementation of [StorageService] using
/// FlutterSecureStorage and SharedPreferences.
class StorageServiceImpl implements StorageService {
  /// Creates a [StorageServiceImpl] instance.
  ///
  /// The SecureStorage and SharedPrefs must not be null.
  StorageServiceImpl(this._secureStorage, this._sharedPrefs);

  /// The secure storage used to store sensitive data.
  final FlutterSecureStorage _secureStorage;

  /// The shared preferences used to store non-sensitive data.
  final SharedPreferences _sharedPrefs;

  @override
  Future<void> writeSecureData(String key, String value) async {
    try {
      // Use secure storage to store the value associated with the key.
      await _secureStorage.write(key: key, value: value);
    } on Exception {
      // Handle the error. For example, log it or rethrow it.
    }
  }

  @override
  Future<String?> readSecureData(String key) async {
    try {
      // Retrieve the value associated with the key from secure storage.
      return await _secureStorage.read(key: key);
    } on Exception {
      // Handle the error. For example, log it or rethrow it.
      return null;
    }
  }

  @override
  Future<T?> readData<T>(String key) async {
    try {
      // Retrieve the value associated with the key from shared preferences.
      final value = _sharedPrefs.get(key);
      if (value is T) {
        return value;
      } else {
        return null;
      }
    } on Exception {
      // Handle the error. For example, log it or rethrow it.
      return null;
    }
  }

  @override
  Future<void> clearAll() async {
    try {
      // Clear all data from shared preferences and secure storage.
      await _sharedPrefs.clear();
      await _secureStorage.deleteAll();
    } on Exception {
      // Handle the error. For example, log it or rethrow it.
    }
  }

  @override
  Future<void> clearSecureStorage() async {
    try {
      // Clear all data from secure storage
      await _secureStorage.deleteAll();
    } on Exception {
      // Handle the error. For example, log it or rethrow it.
    }
  }

  @override
  Future<void> deleteData(String key) async {
    try {
      // Remove the value associated with the key from shared preferences.
      await _sharedPrefs.remove(key);
    } on Exception {
      // Handle the error. For example, log it or rethrow it.
    }
  }

  @override
  Future<void> deleteSecureData(String key) async {
    try {
      // Remove the value associated with the key from secure storage.
      await _secureStorage.delete(key: key);
    } on Exception {
      // Handle the error. For example, log it or rethrow it.
    }
  }

  @override
  Future<void> writeData(String key, dynamic value) async {
    try {
      // Store the value associated with the key in shared preferences.
      if (value is String) {
        await _sharedPrefs.setString(key, value);
      } else if (value is int) {
        await _sharedPrefs.setInt(key, value);
      } else if (value is double) {
        await _sharedPrefs.setDouble(key, value);
      } else if (value is bool) {
        await _sharedPrefs.setBool(key, value);
      } else if (value is List<String>) {
        await _sharedPrefs.setStringList(key, value);
      } else {
        // Handle unsupported value type.
      }
    } on Exception {
      // Handle the error. For example, log it or rethrow it.
    }
  }
}
