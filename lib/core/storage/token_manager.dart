import 'package:gotcha_mfg_app/core/constants/constants.dart';
import 'package:gotcha_mfg_app/core/storage/storage_service.dart';
import 'package:uuid/uuid.dart';

/// Interface for token management
abstract class TokenManager {
  /// Get access token
  Future<String?> getAccessToken();
  Future<String?> getRefsToken();

  /// Refresh access token
  Future<String?> refreshAccessToken();

  /// Save tokens
  Future<void> saveTokens({
    required String accessToken,
    required String refreshToken,
  });

  /// Clear tokens
  Future<void> clearTokens();

  /// Get unique id
  Future<String?> getUniqueId();

  /// Save unique id
  Future<void> saveUniqueId(String uniqueId);

  /// Generate unique id
  Future<String> generateUniqueId();

  /// Clear all data
  Future<void> clearAll();
}

/// A simple implementation of [TokenManager]
class TokenManagerImpl implements TokenManager {
  /// Constructor
  TokenManagerImpl(this._storage);

  final StorageService _storage;

  static const _accessTokenKey = Constants.accessToken;
  static const _refreshTokenKey = Constants.refreshToken;

  @override
  Future<String?> getAccessToken() async {
    return _storage.readData(_accessTokenKey);
  }

  @override
  Future<String?> getRefsToken() async {
    return _storage.readData(_refreshTokenKey);
  }

  @override
  Future<String?> refreshAccessToken() async {
    return null;
  }

  @override
  Future<void> saveTokens({
    required String accessToken,
    required String refreshToken,
  }) async {
    await _storage.writeData(_accessTokenKey, accessToken);
    await _storage.writeData(_refreshTokenKey, refreshToken);
  }

  @override
  Future<void> clearTokens() async {
    await _storage.deleteData(_accessTokenKey);
    await _storage.deleteData(_refreshTokenKey);
  }

  @override
  Future<void> saveUniqueId(String uniqueId) async {
    return _storage.writeData(Constants.uniqueId, uniqueId);
  }

  @override
  Future<String?> getUniqueId() {
    return _storage.readData(Constants.uniqueId);
  }

  @override
  Future<String> generateUniqueId() {
    const uuid = Uuid();
    var uniqueId = uuid.v4();
    _storage.writeData(Constants.uniqueId, uniqueId);
    return Future.value(uniqueId);
  }

  @override
  Future<void> clearAll() async {
    await _storage.clearAll();
  }
}
