import 'package:gotcha_mfg_app/core/utils/data_cache.dart';
import 'package:gotcha_mfg_app/features/splash/data/models/info_response.dart';

extension UrlExtensions on String {
  InfoResponse _getInfoResponse() => DataCache().getData() ?? InfoResponse();

  String _buildUrl(String? baseUrl) => '$baseUrl$this';

  String get categoryUrl => _buildUrl(_getInfoResponse().data?.cdns?.category);
  String get exerciseUrl => _buildUrl(_getInfoResponse().data?.cdns?.exercise);
  String get workoutUrl => _buildUrl(_getInfoResponse().data?.cdns?.workout);
  String get iconUrl => _buildUrl(_getInfoResponse().data?.cdns?.icon);
}
