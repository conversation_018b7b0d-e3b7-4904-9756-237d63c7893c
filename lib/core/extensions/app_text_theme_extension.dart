import 'package:flutter/material.dart';
import 'package:gotcha_mfg_app/config/theme/app_typography.dart';

extension AppTextThemeExtension on TextTheme {
  // Gotham styles
  TextStyle get gothamBold => AppTypography.gothamBold;
  TextStyle get gothamMedium => AppTypography.gothamMedium;

  // Raleway styles
  TextStyle get ralewayBold => AppTypography.ralewayBold;
  TextStyle get ralewaySemiBold => AppTypography.ralewaySemiBold;
  TextStyle get ralewayMedium => AppTypography.ralewayMedium;
  TextStyle get ralewayRegular => AppTypography.ralewayRegular;
  TextStyle get ralewayLight => AppTypography.ralewayLight;

  // New styles
  TextStyle get primaryHeading => AppTypography.primaryHeading;
  TextStyle get sectionHeading => AppTypography.sectionHeading;
  TextStyle get bodyEmphasis => AppTypography.bodyEmphasis;
  TextStyle get bodyRegular => AppTypography.bodyRegular;
  TextStyle get labels => AppTypography.labels;
  TextStyle get agLabels => AppTypography.agLabels;
  TextStyle get placeholder => AppTypography.placeholder;
  TextStyle get linkText => AppTypography.linkText;
  TextStyle get labelsBold => AppTypography.labelsBold;
}
