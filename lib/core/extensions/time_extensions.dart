extension DurationFormatting on Duration {
  String toMinutesSecondsString() {
    // Calculate total minutes and seconds
    int totalSeconds = inSeconds;
    int minutes = totalSeconds ~/ 60;
    int seconds = totalSeconds % 60;

    // Format minutes and seconds
    String formattedMinutes = minutes.toString();
    String formattedSeconds = seconds.toString().padLeft(2, '0');

    // Combine and return the formatted string
    return '$formattedMinutes.$formattedSeconds';
  }
}
