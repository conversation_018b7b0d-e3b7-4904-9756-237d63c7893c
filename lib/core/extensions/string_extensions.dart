extension StringExtensions on String {
  /// Capitalizes the first letter of the string, ignoring leading emojis.
  String capitalizeFirstLetter() {
    if (isEmpty) return this;

    // Find the index of the first non-emoji character
    int firstCharIndex = 0;
    while (firstCharIndex < length && codeUnitAt(firstCharIndex) > 255) {
      firstCharIndex++;
    }

    if (firstCharIndex >= length) return this; // If the string is all emojis

    return substring(0, firstCharIndex) +
        this[firstCharIndex].toUpperCase() +
        substring(firstCharIndex + 1);
  }

  // Capitalizes the first letter of each word in the string, ignoring leading emojis.
  String capitalizeFirstLetterOfEachWord() {
    if (isEmpty) return this;

    // Find the index of the first non-emoji character
    int firstCharIndex = 0;
    while (firstCharIndex < length && codeUnitAt(firstCharIndex) > 255) {
      firstCharIndex++;
    }

    if (firstCharIndex >= length) return this; // If the string is all emojis

    return substring(0, firstCharIndex) +
        this[firstCharIndex].toUpperCase() +
        substring(firstCharIndex + 1);

    // if (isEmpty) return this;

    // Split into words, capitalize each word, and join them back
    // return split(' ').map((word) {
    //   if (word.isEmpty) return word;
    //   return word.capitalizeFirstLetter();
    // }).join(' ');
  }
}
