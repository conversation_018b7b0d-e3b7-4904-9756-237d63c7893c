import 'package:gotcha_mfg_app/config/theme/app_assets.dart';

extension MoodToIcon on String {
  String get icon {
    switch (toLowerCase()) {
      case 'pleasant':
        return AppAssets.elevated;
      case 'settled':
        return AppAssets.settled;
      case 'neutral':
        return AppAssets.neutral;
      case 'unsettled':
        return AppAssets.unsettled;
      case 'unpleasant':
        return AppAssets.heavy;
      default:
        return AppAssets.neutral;
    }
  }
}
