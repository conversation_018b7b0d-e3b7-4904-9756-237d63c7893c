import 'package:internet_connection_checker_plus/internet_connection_checker_plus.dart';

/// Interface for network info
abstract class NetworkInfo {
  /// Check if the device is connected to the internet
  Future<bool> get isConnected;
}

/// Implementation of [NetworkInfo]
class NetworkInfoImpl implements NetworkInfo {
  /// Constructor
  NetworkInfoImpl(this.internetConnection);

  /// Instance of [InternetConnection]
  final InternetConnection internetConnection;

  @override
  Future<bool> get isConnected => internetConnection.hasInternetAccess;
}
