/// Configuration for network client
class NetworkConfig {
  /// Constructor
  const NetworkConfig({
    required this.baseUrl,
    this.connectTimeout = const Duration(seconds: 20),
    this.receiveTimeout = const Duration(seconds: 20),
    this.maxRetries = 1,
    this.retryDelay = const Duration(seconds: 1),
  });

  /// Development configuration
  factory NetworkConfig.development() {
    return const NetworkConfig(
      baseUrl: 'https://staging-api.app.gotcha4life.org/api',
      maxRetries: 1,
      retryDelay: Duration(seconds: 1),
    );
  }

  /// Production configuration
  factory NetworkConfig.production() {
    return const NetworkConfig(
      baseUrl: 'https://api.app.gotcha4life.org/api',
      maxRetries: 1,
      retryDelay: Duration(seconds: 1),
    );
  }

  /// Staging configuration
  factory NetworkConfig.staging() {
    return const NetworkConfig(
      baseUrl: 'https://staging-api.gotcha.mfg',
      maxRetries: 1,
      retryDelay: Duration(seconds: 1),
    );
  }

  /// Base url
  final String baseUrl;

  /// Timeout
  final Duration connectTimeout;

  /// Timeout
  final Duration receiveTimeout;

  /// Max retries
  final int maxRetries;

  /// Retry delay
  final Duration retryDelay;
}
