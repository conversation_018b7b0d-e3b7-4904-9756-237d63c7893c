// ApiCallNameUseCase.dart
import '../../../../core/usecase/usecase.dart';
import '../../../../shared/models/result.dart';
import '../../data/models/update_village_user.dart';
import '../../data/models/village_user_common_response.dart';
import '../repositories/village_repository.dart';

class UpdateVillageUserUseCase
    implements UseCase<Result<VillageUserCommonResponse>, UpdateVillageUser> {
  UpdateVillageUserUseCase(this._repository);

  final VillageRepository _repository;

  @override
  Future<Result<VillageUserCommonResponse>> call(
      UpdateVillageUser params) async {
    return _repository.updateVillageUser(params);
  }
}
