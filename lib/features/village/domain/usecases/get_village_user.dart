// ApiCallNameUseCase.dart
import 'package:gotcha_mfg_app/core/usecase/usecase.dart';
import 'package:gotcha_mfg_app/features/village/data/models/user_get_response.dart';
import 'package:gotcha_mfg_app/features/village/domain/repositories/village_repository.dart';
import 'package:gotcha_mfg_app/shared/models/result.dart';

class GetVillageUserUseCase
    implements UseCase<Result<VillageUserResponse>, NoParams> {
  GetVillageUserUseCase(this._repository);

  final VillageRepository _repository;

  @override
  Future<Result<VillageUserResponse>> call(NoParams params) async {
    return _repository.getVillageUser();
  }
}
