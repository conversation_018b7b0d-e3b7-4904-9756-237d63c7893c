// ApiCallNameUseCase.dart

import '../../../../core/usecase/usecase.dart';
import '../../../../shared/models/result.dart';
import '../../data/models/village_user_common_response.dart';
import '../repositories/village_repository.dart';

class DeleteVillageUserUseCase
    implements UseCase<Result<VillageUserCommonResponse>, String> {
  DeleteVillageUserUseCase(this._repository);

  final VillageRepository _repository;

  @override
  Future<Result<VillageUserCommonResponse>> call(String params) async {
    return _repository.deleteVillageUser(params);
  }
}
