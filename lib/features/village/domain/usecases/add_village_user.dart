// ApiCallNameUseCase.dart

import '../../../../core/usecase/usecase.dart';
import '../../../../shared/models/result.dart';
import '../../data/models/add_village_user.dart';
import '../../data/models/village_user_common_response.dart';
import '../repositories/village_repository.dart';

class AddVillageUserUseCase
    implements UseCase<Result<VillageUserCommonResponse>, AddVillageUser> {
  AddVillageUserUseCase(this._repository);

  final VillageRepository _repository;

  @override
  Future<Result<VillageUserCommonResponse>> call(AddVillageUser params) async {
    return _repository.addVillageUser(params);
  }
}
