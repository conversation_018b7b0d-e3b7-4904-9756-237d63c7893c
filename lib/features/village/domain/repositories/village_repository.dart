// FeatureRepository.dart
import 'package:gotcha_mfg_app/shared/models/result.dart';
import '../../data/models/add_village_user.dart';
import '../../data/models/update_village_user.dart';
import '../../data/models/user_get_response.dart';
import '../../data/models/village_user_common_response.dart';

abstract class VillageRepository {
  Future<Result<VillageUserResponse>> getVillageUser();
  Future<Result<VillageUserCommonResponse>> addVillageUser(
      AddVillageUser request);
  Future<Result<VillageUserCommonResponse>> updateVillageUser(
      UpdateVillageUser request);
  Future<Result<VillageUserCommonResponse>> deleteVillageUser(String id);
}
