import 'package:auto_route/auto_route.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:gap/gap.dart';
import 'package:gotcha_mfg_app/config/theme/app_assets.dart';
import 'package:gotcha_mfg_app/core/extensions/app_text_theme_extension.dart';
import 'package:gotcha_mfg_app/core/extensions/url_extensions.dart';
import 'package:gotcha_mfg_app/features/village/data/models/add_village_user.dart';
import 'package:gotcha_mfg_app/features/village/presentations/cubits/village/village_state.dart';
import 'package:gotcha_mfg_app/shared/widgets/app_header.dart';
import 'package:gotcha_mfg_app/shared/widgets/retry_widget.dart';
import 'package:gotcha_mfg_app/shared/widgets/show_info.dart';

import '../../../../config/theme/app_colors.dart';
import '../../../../core/mixpanel_service.dart';
import '../../../../core/utils/app_print.dart';
import '../../../../core/utils/platform_utils.dart';
import '../../../../core/utils/snackbar_service.dart';
import '../../../../locator.dart';
import '../../../../shared/widgets/loading_widget.dart';
import '../../../exercise/presentation/pages/mixed_exercise_page.dart';
import '../../data/models/update_village_user.dart';
import '../cubits/village/village_cubit.dart';
import '../widgets/communication_prompts.dart';
import '../widgets/listen_to_us.dart';
import '../widgets/rating_widget.dart';

@RoutePage()
class VillageHomePage extends StatefulWidget {
  const VillageHomePage({super.key});

  @override
  State<VillageHomePage> createState() => _VillageHomePageState();
}

class _VillageHomePageState extends State<VillageHomePage> {
  bool isAdded = false;
  // bool isFaded = false;
  bool isEdit = false;
  String? editingUserId;
  @override
  void initState() {
    super.initState();
    sl<MixpanelService>().trackScreenView('Village Page',
        properties: {'Code': 'screen_view.village_page'});

    context.read<VillageCubit>().getVillageUser();
    isAdded = false;
    // isFaded = false;
    isEdit = false;
  }

  @override
  Widget build(BuildContext context) {
    final textTheme = Theme.of(context).textTheme;
    final size = MediaQuery.sizeOf(context);
    return BlocConsumer<VillageCubit, VillageState>(listener: (context, state) {
      if (state is VillageError) {
        SnackBarService.error(
          context: context,
          message: state.message,
        );
      }
      if (state is AddVillageUser) {
        // SnackBarService.info(
        //   context: context,
        //   message: 'Success.',
        // );
      }
    }, builder: (context, state) {
      var size = MediaQuery.of(context).size;
      if (state is VillageError) {
        return RetryWidget(
          onRetry: () => context.read<VillageCubit>().getVillageUser(),
          color: Colors.white,
        );
      }

      if (state is VillageLoading) {
        return const LoadingWidget(color: Colors.white);
      }

      if (state is VillageLoaded) {
        info(
            '----village---${state.villageUserResponse.data?.villageExercises}');
        final users = state.villageUserResponse.data;

        return Scaffold(
          appBar: AppBar(
            toolbarHeight: 0,
            elevation: 0,
            systemOverlayStyle: const SystemUiOverlayStyle(
              statusBarColor: Colors.white,
              systemNavigationBarIconBrightness: Brightness.dark,
              statusBarBrightness: Brightness.light,
              statusBarIconBrightness: Brightness.dark,
              systemNavigationBarColor: AppColors.lightBlue,
            ),
          ),
          body: GestureDetector(
              onTap: () {
                FocusScope.of(context).unfocus();
              },
              behavior: HitTestBehavior.translucent,
              child: Padding(
                padding: EdgeInsets.only(
                  left: 8,
                  right: 8,
                  top: isIos ? 0 : 8,
                ),
                child: Container(
                  decoration: const BoxDecoration(
                      color: AppColors.lightBlue,
                      borderRadius: BorderRadius.only(
                          topLeft: Radius.circular(30),
                          topRight: Radius.circular(30))),
                  child: ListView(
                    children: [
                      AppHeader(
                        onBackTap: () {
                          Navigator.pop(context);
                        },
                        currentStep: 0,
                        totalSteps: 0,
                        title: "Your village",
                      ),
                      Container(
                        color: AppColors.navy,
                        // height: size.height, // REMOVE FIXED HEIGHT
                        child: Container(
                          // height: size.height,
                          decoration: const BoxDecoration(
                            borderRadius: BorderRadius.only(
                              topLeft: Radius.circular(30),
                              topRight: Radius.circular(30),
                            ),
                            color: AppColors.grey,
                          ),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              const Gap(24),
                              if (editingUserId == null &&
                                  state.villageUserResponse.data
                                          ?.villageExercises !=
                                      null) // Hide when editing
                                Padding(
                                  padding: const EdgeInsets.symmetric(
                                      horizontal: 24.0),
                                  child: ListenToGusWidget(
                                    tap: () {
                                      Navigator.of(context).push(
                                        MaterialPageRoute(
                                          builder: (context) =>
                                              MixedExercisePage(
                                            notification: false,
                                            id: users?.villageExercises?.id ??
                                                '',
                                            seriesId: "",
                                            isLast: false,
                                            isFirst: false,
                                            isOverride: false,
                                          ),
                                        ),
                                      );
                                      // mixpanel
                                      sl<MixpanelService>().trackButtonClick(
                                          'Listen to Gus',
                                          properties: {
                                            'Page': 'Village Page',
                                            'Code':
                                                'click.village_page.listen_to_gus'
                                          });
                                      return;
                                    },
                                    text:
                                        "Listen to Gus Worland on why looking after your village matters.",
                                    imageUrl: users?.villageExercises
                                            ?.thumbnailUrl?.exerciseUrl ??
                                        '',
                                  ),
                                ),
                              if (editingUserId == null &&
                                  state.villageUserResponse.data
                                          ?.villageExercises !=
                                      null)
                                const Gap(16),
                              Padding(
                                padding: const EdgeInsets.symmetric(
                                    horizontal: 24.0),
                                child: InfoMessage(
                                  message: editingUserId != null
                                      ? "Who are the people you couldn’t imagine doing life without? Write down their names and check in on how you’re showing up in these relationships. Score yourself out of 10."
                                      : "Write down the names of everyone you love, adore and can't imagine doing life without. Give yourself a mark for how you are doing in these relationships. Use this space as a reminder to check in and make time for the people in your village.",
                                  color: AppColors.lightBlue,
                                  asset: editingUserId != null
                                      ? AppAssets.infomessage
                                      : AppAssets.infobulb,
                                ),
                              ),
                              const Gap(16),
                              if (users != null)
                                Column(
                                  children: (() {
                                    final sortedUsers =
                                        users.villageUsers!.toList()
                                          ..sort((a, b) {
                                            if (a.name?.toLowerCase() == 'me') {
                                              return -1;
                                            }
                                            if (b.name?.toLowerCase() == 'me') {
                                              return 1;
                                            }
                                            return 0;
                                          });
                                    return sortedUsers.map((userData) {
                                      return Padding(
                                        padding: const EdgeInsets.symmetric(
                                            horizontal: 24.0, vertical: 6),
                                        child: editingUserId == userData.id
                                            ? RatingAddWidget(
                                                isFaded:
                                                    false, // When editing, isFaded is always false
                                                id: userData.id,
                                                name: userData.name ?? 'N/A',
                                                rating: userData.score,
                                                onSubmit: (name, rating) {
                                                  setState(() {
                                                    editingUserId = null;
                                                  });
                                                  var params =
                                                      UpdateVillageUser(
                                                          name: name,
                                                          score: rating,
                                                          id: userData.id ??
                                                              '');
                                                  context
                                                      .read<VillageCubit>()
                                                      .updateVillageUser(
                                                          params);
                                                },
                                                edit: true,
                                                onDelete: (val) {
                                                  setState(() {
                                                    editingUserId = null;
                                                  });
                                                  context
                                                      .read<VillageCubit>()
                                                      .deleteVillageUser(
                                                          userData.id ?? '');
                                                },
                                              )
                                            : RatingWidget(
                                                isFaded: editingUserId !=
                                                            null &&
                                                        editingUserId !=
                                                            userData.id ||
                                                    isAdded ==
                                                        true, // Only fade if another user is being edited
                                                onNameEditTapped: (value) {
                                                  setState(() {
                                                    editingUserId =
                                                        editingUserId ==
                                                                userData.id
                                                            ? null
                                                            : userData.id;
                                                  });
                                                },
                                                isEdit: false,
                                                onNameChanged: (value) {
                                                  // Your onNameChanged logic here
                                                },
                                                name: userData.name ?? 'N/A',
                                                rating: userData.score ?? 0,
                                                totalRating: 10,
                                              ),
                                      );
                                    }).toList();
                                  })(),
                                )
                              else
                                const Text(
                                    'No users found.'), // Handle the case where users are null
                              isAdded ? const Gap(8) : const SizedBox(),
                              isAdded
                                  ? Padding(
                                      padding: const EdgeInsets.symmetric(
                                          horizontal: 24.0),
                                      child: RatingAddWidget(
                                        isFaded: false,
                                        name: '',
                                        rating: 0,
                                        edit: false,
                                        onDelete: (users) {},
                                        id: '',
                                        onSubmit: (name, rating) {
                                          setState(() {
                                            editingUserId = null;
                                          });
                                          var params = AddVillageUser(
                                              name: name, score: rating);
                                          context
                                              .read<VillageCubit>()
                                              .addVillageUser(params);
                                          isAdded = !isAdded;
                                        },
                                      ),
                                    )
                                  : const SizedBox(),
                              isAdded ? const Gap(8) : const SizedBox(),
                              Padding(
                                padding: const EdgeInsets.symmetric(
                                  horizontal: 24,
                                  vertical: 12,
                                ),
                                child: GestureDetector(
                                  onTap: () {
                                    setState(() {
                                      isAdded = !isAdded;
                                    });
                                  },
                                  child: Row(
                                    children: [
                                      const Icon(
                                        Icons.add,
                                        color: AppColors.coral,
                                        size: 20,
                                      ),
                                      Text(
                                        'Add someone to your village',
                                        style: textTheme.linkText.copyWith(
                                          color: AppColors.coral,
                                          overflow: TextOverflow.ellipsis,
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                              ),
                              const Gap(16),
                              const Communication(),
                              // const Gap(32),
                            ],
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              )),
        );
      }
      return const SizedBox();
    });
  }
}
