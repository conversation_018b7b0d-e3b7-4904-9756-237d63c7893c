// import 'package:auto_route/auto_route.dart';
// import 'package:flutter/material.dart';
// import 'package:flutter/services.dart';
// import 'package:gap/gap.dart';
// import 'package:gotcha_mfg_app/shared/widgets/app_header.dart';
// import 'package:gotcha_mfg_app/shared/widgets/show_info.dart';

// import '../../../../config/theme/app_colors.dart';
// import '../../../../core/utils/platform_utils.dart';
// import '../widgets/rating_widget.dart';

// @RoutePage()
// class VillageHomeEditPage extends StatefulWidget {
//   final String name;
//   final int rating;
//   final int totalrating;
//   final ValueChanged<String> onNameChanged; // Callback for name change

//   const VillageHomeEditPage(
//       {super.key,
//       required this.name,
//       required this.rating,
//       required this.totalrating,
//       required this.onNameChanged});

//   @override
//   State<VillageHomeEditPage> createState() => _VillageHomeEditPageState();
// }

// class _VillageHomeEditPageState extends State<VillageHomeEditPage> {
//   TextEditingController namecontroller = TextEditingController();
//   bool? isEdit = false;
//   bool? isTextEdit = false;
//   @override
//   Widget build(BuildContext context) {
//     final textTheme = Theme.of(context).textTheme;
//     final size = MediaQuery.sizeOf(context);
//     return Scaffold(
//         appBar: AppBar(
//           toolbarHeight: 0,
//           elevation: 0,
//           systemOverlayStyle: const SystemUiOverlayStyle(
//             statusBarColor: Colors.white,
//             systemNavigationBarIconBrightness: Brightness.dark,
//           ),
//         ),
//         body: Padding(
//             padding: EdgeInsets.only(
//               left: 8,
//               right: 8,
//               top: isIos ? 4 : 8,
//             ),
//             child: ListView(children: [
//               AppHeader(
//                 onBackTap: () {
//                   Navigator.pop(context);
//                 },
//                 currentStep: 0,
//                 totalSteps: 0,
//                 title: "Your village",
//               ),
//               Container(
//                   color: AppColors.navy,
//                   height: size.height - 100,
//                   // Categories and Workouts or Search Results
//                   child: Container(
//                       height: size.height,
//                       decoration: const BoxDecoration(
//                         borderRadius: BorderRadius.only(
//                           topLeft: Radius.circular(30),
//                           topRight: Radius.circular(30),
//                         ),
//                         color: AppColors.grey,
//                       ),
//                       child: Column(
//                           crossAxisAlignment: CrossAxisAlignment.start,
//                           children: [
//                             const Gap(16),
//                             const Padding(
//                               padding: EdgeInsets.symmetric(horizontal: 24.0),
//                               child: InfoMessage(
//                                 message:
//                                     'Write down the names of people you love, adore and can’t imagine doing life without. Give yourself a mark out of ten for how YOU are performing in each of these relationships',
//                                 color: AppColors.lightBlue,
//                               ),
//                             ),
//                             const Gap(16),
//                             Padding(
//                               padding:
//                                   const EdgeInsets.symmetric(horizontal: 24.0),
//                               child: RatingWidget(
//                                 isEdit: true,
//                                 name: widget.name,
//                                 rating: widget.rating,
//                                 totalRating: widget.totalrating,
//                                 onNameChanged: (value) {
//                                   setState(() {
//                                     // widget.onNameChanged(value);
//                                   });
//                                 },
//                                 onNameEditTapped: (value) {
//                                   setState(() {
//                                     // isTextEdit = !isTextEdit!;
//                                   });
//                                 },
//                               ),
//                             ),
//                             isTextEdit == true
//                                 ? Padding(
//                                     padding: const EdgeInsets.symmetric(
//                                         horizontal: 24.0),
//                                     child: TextField(
//                                       controller: namecontroller,
//                                       decoration: const InputDecoration(
//                                         border: OutlineInputBorder(),
//                                         labelText: 'Name',
//                                       ),
//                                       onChanged: (value) {
//                                         setState(() {
//                                           widget.onNameChanged(value);
//                                         });
//                                       },
//                                     ),
//                                   )
//                                 : const SizedBox(),
//                             const Gap(16),
//                           ])))
//             ])));
//   }
// }
