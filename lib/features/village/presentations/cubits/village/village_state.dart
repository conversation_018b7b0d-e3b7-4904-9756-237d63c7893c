// VillageState.dart
import 'package:gotcha_mfg_app/features/village/data/models/user_get_response.dart';

import '../../../data/models/village_user_common_response.dart';

abstract class VillageState {}

class VillageInitial extends VillageState {}

class VillageLoading extends VillageState {}

class VillageLoaded extends VillageState {
  VillageLoaded(this.villageUserResponse);
  final VillageUserResponse villageUserResponse;
}

class VillageUserAdded extends VillageState {
  VillageUserAdded(this.villageUserResponse);
  final VillageUserCommonResponse villageUserResponse;
}

class VillageError extends VillageState {
  VillageError(this.message);
  final String message;
}
