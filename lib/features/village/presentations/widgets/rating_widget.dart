import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:gap/gap.dart';
import 'package:gotcha_mfg_app/config/theme/app_assets.dart';
import 'package:gotcha_mfg_app/core/extensions/app_text_theme_extension.dart';

import '../../../../config/theme/app_colors.dart';
import '../../../../core/utils/snackbar_service.dart';

class RatingWidget extends StatelessWidget {
  final String name;
  final int rating;
  final int totalRating;
  final bool isEdit;
  final bool isFaded; // New parameter for opacity control
  final ValueChanged<String> onNameChanged;
  final ValueChanged<bool> onNameEditTapped;

  const RatingWidget({
    super.key,
    required this.name,
    required this.rating,
    required this.totalRating,
    required this.isEdit,
    this.isFaded = false, // Default to false
    required this.onNameChanged,
    required this.onNameEditTapped,
  });

  @override
  Widget build(BuildContext context) {
    final textTheme = Theme.of(context).textTheme;
    final size = MediaQuery.sizeOf(context);

    return Opacity(
      opacity: isFaded ? 0.5 : 1.0, // Apply opacity based on isFaded
      child: Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(16),
          boxShadow: const [
            // BoxShadow(
            //   color: Colors.black.withOpacity(0.05),
            //   blurRadius: 8,
            //   offset: const Offset(0, 2),
            // ),
          ],
        ),
        child: Row(
          children: [
            Text(
              name,
              style: textTheme.labels.copyWith(
                overflow: TextOverflow.ellipsis,
              ),
            ),
            const Spacer(),
            Text(
              rating == 0 ? '?' : '$rating',
              style: textTheme.sectionHeading.copyWith(
                overflow: TextOverflow.ellipsis,
              ),
            ),
            Text(
              '/$totalRating',
              style: textTheme.ralewayMedium.copyWith(
                fontSize: 14,
                color: AppColors.navy,
                overflow: TextOverflow.ellipsis,
              ),
            ),
            const Gap(8),
            GestureDetector(
              onTap: () {
                onNameEditTapped(true);
                // !isEdit
                //     ? context.pushRoute(
                //         VillageHomeEditRoute(
                //           onNameChanged: (val) {},
                //           name: name,
                //           rating: rating,
                //           totalrating: totalRating,
                //         ),
                //       )
                //     : const SizedBox();
              },
              child: Image.asset(
                AppAssets.penciledit,
                width: 20,
                color: AppColors.coral,
              ),
            ),
          ],
        ),
      ),
    );
  }
}

class RatingAddWidget extends StatefulWidget {
  final String name;
  final int? rating;
  final String? id;
  final bool edit;
  final bool isFaded; // New parameter for opacity control

  final Function(String name, int rating) onSubmit;
  final Function(String val) onDelete;

  const RatingAddWidget({
    super.key, // Added Key? key for consistency
    required this.onSubmit,
    required this.edit,
    required this.id,
    required this.name,
    required this.rating,
    required this.onDelete,
    this.isFaded = false,
  });

  @override
  State<RatingAddWidget> createState() => _RatingAddWidgetState();
}

class _RatingAddWidgetState extends State<RatingAddWidget> {
  late TextEditingController _nameController;
  late TextEditingController _ratingController;
  final int totalRating = 10;
  final FocusNode _nameFocus = FocusNode();
  final FocusNode _ratingFocus = FocusNode();

  @override
  void initState() {
    super.initState();
    _nameController = TextEditingController(text: widget.name);
    _ratingController = TextEditingController(
        text: widget.rating != null && widget.rating! > 0
            ? widget.rating.toString()
            : '');
  }

  @override
  void dispose() {
    _nameController.dispose();
    _ratingController.dispose();
    _nameFocus.dispose();
    _ratingFocus.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final textTheme = Theme.of(context).textTheme;

    return Opacity(
      opacity: widget.isFaded ? 0.5 : 1.0,
      child: Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(16),
          boxShadow: const [
            // BoxShadow(
            //   color: Colors.black.withOpacity(0.05),
            //   blurRadius: 8,
            //   offset: const Offset(0, 2),
            // ),
          ],
        ),
        child: Row(
          children: [
            Expanded(
              child: TextField(
                controller: _nameController,
                focusNode: _nameFocus,
                style: textTheme.labels,
                textInputAction: TextInputAction.next,
                onSubmitted: (_) {
                  _ratingFocus.requestFocus();
                },
                decoration: const InputDecoration(
                  hintText: 'Enter name',
                  contentPadding: EdgeInsets.zero,
                  border: InputBorder.none,
                  isDense: true,
                ),
                inputFormatters: [LengthLimitingTextInputFormatter(10)],
              ),
            ),
            const Gap(16),
            SizedBox(
              width: 50,
              child: TextField(
                controller: _ratingController,
                focusNode: _ratingFocus,
                style: textTheme.sectionHeading.copyWith(
                    color: widget.edit ? AppColors.coral : AppColors.navy),
                keyboardType: TextInputType.number,
                textAlign: TextAlign.center,
                inputFormatters: [
                  LengthLimitingTextInputFormatter(2),
                  FilteringTextInputFormatter.digitsOnly,
                  TextInputFormatter.withFunction((oldValue, newValue) {
                    if (newValue.text.isEmpty) return newValue;
                    final int? value = int.tryParse(newValue.text);
                    if (value == null || value < 1 || value > 10) {
                      return oldValue;
                    }
                    return newValue;
                  }),
                ],
                decoration: const InputDecoration(
                  hintText: '?',
                  contentPadding: EdgeInsets.zero,
                  border: InputBorder.none,
                  isDense: true,
                ),
              ),
            ),
            Text(
              '/$totalRating',
              style: textTheme.ralewayMedium.copyWith(
                fontSize: 14,
                color: AppColors.navy,
              ),
            ),
            const Gap(8),
            Row(
              children: [
                GestureDetector(
                  onTap: () {
                    if (_nameController.text.trim().isNotEmpty) {
                      final rating = int.tryParse(_ratingController.text);
                      if (rating != null &&
                          rating >= 1 &&
                          rating <= totalRating) {
                        widget.onSubmit(_nameController.text, rating);
                      } else if (_ratingController.text.isEmpty) {
                        SnackBarService.info(
                          context: context,
                          message:
                              'Please enter a rating between 1 and $totalRating',
                        );
                      } else {
                        SnackBarService.error(
                          context: context,
                          message: 'Rating must be between 1 and $totalRating',
                        );
                      }
                    } else {
                      SnackBarService.info(
                        context: context,
                        message: 'Please enter a name',
                      );
                    }
                  },
                  child: const Icon(Icons.check, color: AppColors.coral),
                ),
                widget.edit
                    ? GestureDetector(
                        onTap: () {
                          widget.onDelete('val'); // Call onDelete here
                        },
                        child: const Row(
                          children: [
                            Gap(4),
                            Icon(Icons.close, color: AppColors.coral),
                          ],
                        ),
                      )
                    : const SizedBox(),
              ],
            )
          ],
        ),
      ),
    );
  }
}
