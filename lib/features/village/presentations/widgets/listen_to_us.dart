import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:gotcha_mfg_app/config/theme/app_assets.dart';
import 'package:gotcha_mfg_app/core/extensions/app_text_theme_extension.dart';
import 'package:gotcha_mfg_app/shared/widgets/network_image_indicator.dart';

import '../../../../config/theme/app_colors.dart';

class ListenToGusWidget extends StatelessWidget {
  final String text;
  final VoidCallback tap;
  final String imageUrl;

  const ListenToGusWidget({
    super.key,
    required this.text,
    required this.tap,
    required this.imageUrl,
  });

  @override
  Widget build(BuildContext context) {
    final textTheme = Theme.of(context).textTheme;
    final size = MediaQuery.sizeOf(context);

    return GestureDetector(
      onTap: () {
        tap();
        // Navigate to audio player or video player
      },
      child: Container(
        padding: const EdgeInsets.all(16), // Responsive padding
        decoration: BoxDecoration(
          color: AppColors.lightRed,
          borderRadius: BorderRadius.circular(12),
          boxShadow: const [
            // BoxShadow(
            //   color: Colors.black.withOpacity(0.05),
            //   blurRadius: 8,
            //   offset: const Offset(0, 2),
            // ),
          ],
        ),
        child: Row(
          children: [
            Container(
              width: 36,
              height: 50,
              // Responsive red box width
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(10),
              ),
              clipBehavior: Clip.hardEdge,
              child: NetworkImageWithIndicator(
                fit: BoxFit.cover,
                imageUrl: imageUrl,
              ),
            ),

            const Gap(18), // Responsive SizedBox width
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    text,
                    style: textTheme.labels.copyWith(
                      // color: AppColors.navy,
                      overflow: TextOverflow.ellipsis,
                    ),
                    maxLines: 3,
                  ),
                ],
              ),
            ),
            const Gap(18), // Responsive SizedBox width

            Image.asset(
              AppAssets.arrowforward,
              color: AppColors.navy,
              width: 20,
            )
          ],
        ),
      ),
    );
  }
}
