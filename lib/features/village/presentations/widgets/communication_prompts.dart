import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:gotcha_mfg_app/core/extensions/app_text_theme_extension.dart';
import 'package:share_plus/share_plus.dart';

import '../../../../config/theme/app_colors.dart';
import 'communication_prompt.dart';

class Communication extends StatefulWidget {
  // Changed to StatefulWidget
  const Communication({super.key});

  @override
  State<Communication> createState() => _CommunicationState();
}

class _CommunicationState extends State<Communication> {
  // State class created
  String message1 =
      'Love you, miss you, see you soon xoxo'; // State variable for message1
  String message2 =
      "Hey, haven’t seen you in a while - are you around to catch up this weekend? ";
  String message3 =
      'Just checking-in. How’re you doing today on a scale of 1-10?'; // State variable for message2

  @override
  Widget build(BuildContext context) {
    final textTheme = Theme.of(context).textTheme;
    final size = MediaQuery.sizeOf(context);
    return Container(
      color: AppColors.grey,
      padding: const EdgeInsets.only(),
      child: Container(
        // height: size.height*.6,
        decoration: const BoxDecoration(
          borderRadius: BorderRadius.only(
            topLeft: Radius.circular(30),
            topRight: Radius.circular(30),
          ),
          color: AppColors.lightBlue,
        ),
        child: Padding(
          padding: const EdgeInsets.symmetric(horizontal: 24.0, vertical: 24),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'Simple ways to check-in',
                style: textTheme.sectionHeading.copyWith(
                  overflow: TextOverflow.ellipsis,
                ),
                maxLines: 2,
              ),
              const Gap(16),
              MessageWidget(
                message: message1, // Use state variable message1
                onMessageChanged: (newValue) {
                  // Callback for MessageWidget 1
                  setState(() {
                    message1 =
                        newValue; // Update state variable when text changes
                  });
                },
                onSendPressed: () {
                  Share.share(
                    message1,
                    sharePositionOrigin: Rect.fromLTWH(
                      0,
                      0,
                      MediaQuery.of(context).size.width,
                      MediaQuery.of(context).size.height / 2,
                    ),
                  );
                },
              ),
              const Gap(16),
              MessageWidget(
                message: message2, // Use state variable message2
                onMessageChanged: (newValue) {
                  // Callback for MessageWidget 2
                  setState(() {
                    message2 =
                        newValue; // Update state variable when text changes
                  });
                },
                onSendPressed: () {
                  Share.share(
                    message2,
                    sharePositionOrigin: Rect.fromLTWH(
                      0,
                      0,
                      MediaQuery.of(context).size.width,
                      MediaQuery.of(context).size.height / 2,
                    ),
                  );
                },
              ),
              const Gap(16),
              MessageWidget(
                message: message3, // Use state variable message1
                onMessageChanged: (newValue) {
                  // Callback for MessageWidget 1
                  setState(() {
                    message3 =
                        newValue; // Update state variable when text changes
                  });
                },
                onSendPressed: () {
                  Share.share(
                    message3,
                    sharePositionOrigin: Rect.fromLTWH(
                      0,
                      0,
                      MediaQuery.of(context).size.width,
                      MediaQuery.of(context).size.height / 2,
                    ),
                  );
                },
              ),
              const Gap(80),
            ],
          ),
        ),
      ),
    );
  }
}
