import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:gotcha_mfg_app/config/theme/app_assets.dart';
import 'package:gotcha_mfg_app/core/extensions/app_text_theme_extension.dart';

import '../../../../config/theme/app_colors.dart';
import '../../../../core/utils/platform_utils.dart';

class MessageWidget extends StatefulWidget {
  final String message;
  final Function(String)? onMessageChanged; // Optional callback
  final VoidCallback? onSendPressed;

  const MessageWidget({
    super.key,
    required this.message,
    this.onMessageChanged, // Optional callback in constructor
    this.onSendPressed,
  });

  @override
  State<MessageWidget> createState() => _MessageWidgetState();
}

class _MessageWidgetState extends State<MessageWidget> {
  late TextEditingController _messageController;

  @override
  void initState() {
    super.initState();
    _messageController = TextEditingController(text: widget.message);
  }

  @override
  void dispose() {
    _messageController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final textTheme = Theme.of(context).textTheme;
    return ConstrainedBox(
      constraints: BoxConstraints(
        maxWidth: MediaQuery.of(context).size.width,
      ),
      child: Container(
        padding: EdgeInsets.only(
          top: isIos ? 4 : 8,
          bottom: 8,
          left: 12,
          right: 8,
        ),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(16),
        ),
        child: Padding(
          padding: const EdgeInsets.symmetric(vertical: 0.0),
          child: Row(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment:
                CrossAxisAlignment.center, // Keep existing crossAxisAlignment
            children: [
              Flexible(
                child: Align(
                  // Wrap TextField with Align
                  alignment: Alignment.center,
                  child: TextField(
                    controller: _messageController,
                    style: textTheme.placeholder.copyWith(
                      color: AppColors.navy,
                    ),
                    maxLines: 3,
                    minLines: 1, // Add minLines to allow single-line height
                    textInputAction: TextInputAction.done,
                    decoration: const InputDecoration(
                      border: InputBorder.none,
                      isDense:
                          true, // Use isDense: true for tighter vertical spacing
                      contentPadding: EdgeInsets.symmetric(
                          horizontal: 8,
                          vertical: 8), // Adjust vertical padding
                    ),
                    onChanged: (value) {
                      widget.onMessageChanged?.call(value);
                    },
                  ),
                ),
              ),
              const Gap(12),
              // Wrap GestureDetector with a SizedBox.  Important for proper alignment.
              SizedBox(
                height: 40, // Same height as width of the image
                width: 40,
                child: Align(
                  // Wrap GestureDetector with Align for vertical centering.
                  alignment: Alignment.center,
                  child: GestureDetector(
                    onTap: widget.onSendPressed,
                    child: Image.asset(
                      AppAssets.newforward,
                      width: 40,
                    ),
                  ),
                ),
              ),

              const Gap(6),
            ],
          ),
        ),
      ),
    );
  }
}
