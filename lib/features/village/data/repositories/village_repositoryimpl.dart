// FeatureRepositoryImpl.dart
import 'package:gotcha_mfg_app/features/village/data/data_source/village_data_source.dart';
import 'package:gotcha_mfg_app/features/village/domain/repositories/village_repository.dart';
import 'package:gotcha_mfg_app/shared/models/result.dart';
import '../models/add_village_user.dart';
import '../models/update_village_user.dart';
import '../models/user_get_response.dart';
import '../models/village_user_common_response.dart';

class VillageRepositoryImpl implements VillageRepository {
  VillageRepositoryImpl(this._remoteDataSource);

  final VillageRemoteDataSource _remoteDataSource;

  @override
  Future<Result<VillageUserResponse>> getVillageUser() async {
    return await _remoteDataSource.getVillageUser();
  }

  @override
  Future<Result<VillageUserCommonResponse>> addVillageUser(
      AddVillageUser request) async {
    return await _remoteDataSource.addVillageUser(request);
  }

  @override
  Future<Result<VillageUserCommonResponse>> updateVillageUser(
      UpdateVillageUser request) async {
    return await _remoteDataSource.updateVillageUser(request);
  }

  @override
  Future<Result<VillageUserCommonResponse>> deleteVillageUser(String id) async {
    return await _remoteDataSource.deleteVillageUser(id);
  }
}
