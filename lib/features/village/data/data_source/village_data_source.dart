// FeatureRemoteDataSource.dart
import 'package:dio/dio.dart';
import 'package:firebase_crashlytics/firebase_crashlytics.dart';
import 'package:gotcha_mfg_app/shared/models/result.dart';
import '../models/add_village_user.dart';
import '../models/update_village_user.dart';
import '../models/user_get_response.dart';
import '../models/village_user_common_response.dart';

abstract class VillageRemoteDataSource {
  Future<Result<VillageUserResponse>> getVillageUser();
  Future<Result<VillageUserCommonResponse>> addVillageUser(
      AddVillageUser request);
  Future<Result<VillageUserCommonResponse>> updateVillageUser(
      UpdateVillageUser request);
  Future<Result<VillageUserCommonResponse>> deleteVillageUser(String id);
}

// VillageRemoteDataSourceImpl.dart

class VillageRemoteDataSourceImpl implements VillageRemoteDataSource {
  VillageRemoteDataSourceImpl(this._dio);

  final Dio _dio;

  @override
  Future<Result<VillageUserResponse>> getVillageUser() async {
    try {
      final response = await _dio.get('/app/village');

      if (response.statusCode == 200) {
        final data = response.data;
        final villageUserResponse = VillageUserResponse.fromJson(data);
        return Result.success(villageUserResponse);
      } else {
        return Result.failure(
            '${response.statusCode} - ${response.data['message']}');
      }
    } catch (e) {
      FirebaseCrashlytics.instance.recordError(e, StackTrace.current);
      return Result.failure(
          'An error occurred during getting village user: ${e.toString()}');
    }
  }

  @override
  Future<Result<VillageUserCommonResponse>> addVillageUser(
      AddVillageUser request) async {
    try {
      final response = await _dio.post('/app/village', data: request);

      if (response.statusCode == 200) {
        final data = response.data;
        final villageUserResponse = VillageUserCommonResponse.fromJson(data);
        return Result.success(villageUserResponse);
      } else {
        return Result.failure(
            '${response.statusCode} - ${response.data['message']}');
      }
    } catch (e) {
      FirebaseCrashlytics.instance.recordError(e, StackTrace.current);
      return Result.failure(
          'An error occurred during adding village user: ${e.toString()}');
    }
  }

  @override
  Future<Result<VillageUserCommonResponse>> updateVillageUser(
      UpdateVillageUser request) async {
    String id = request.id.toString();
    final requestBody = {
      "name": request.name,
      "score": request.score,
    };
    try {
      final response = await _dio.put('/app/village/$id', data: requestBody);

      if (response.statusCode == 200) {
        final data = response.data;
        final villageUserCommonResponse =
            VillageUserCommonResponse.fromJson(data);
        return Result.success(villageUserCommonResponse);
      } else {
        return Result.failure(
            '${response.statusCode} - ${response.data['message']}');
      }
    } catch (e) {
      FirebaseCrashlytics.instance.recordError(e, StackTrace.current);
      return Result.failure(
          'An error occurred during updating village user: ${e.toString()}');
    }
  }

  @override
  Future<Result<VillageUserCommonResponse>> deleteVillageUser(String id) async {
    try {
      final response = await _dio.delete('/app/village/$id');

      if (response.statusCode == 200) {
        final data = response.data;
        final villageUserCommonResponse =
            VillageUserCommonResponse.fromJson(data);
        return Result.success(villageUserCommonResponse);
      } else {
        return Result.failure(
            '${response.statusCode} - ${response.data['message']}');
      }
    } catch (e) {
      FirebaseCrashlytics.instance.recordError(e, StackTrace.current);
      return Result.failure(
          'An error occurred during deleting village user: ${e.toString()}');
    }
  }
}
