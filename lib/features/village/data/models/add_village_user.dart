// request/add_village_user.dart
class AddVillageUser {
  AddVillageUser({
    required this.name,
    required this.score,
  });

  final String name;
  final int score;

  factory AddVillageUser.fromJson(Map<String, dynamic> json) => AddVillageUser(
        name: json["name"],
        score: json["score"],
      );

  Map<String, dynamic> toJson() => {
        "name": name,
        "score": score,
      };
}
