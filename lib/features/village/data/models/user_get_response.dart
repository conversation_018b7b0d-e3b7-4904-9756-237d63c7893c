// import 'dart:convert';

// class VillageUserResponse {
//   final String? message;
//   final String? status;
//   final List<Datum>? data;

//   VillageUserResponse({
//     this.message,
//     this.status,
//     this.data,
//   });

//   VillageUserResponse copyWith({
//     String? message,
//     String? status,
//     List<Datum>? data,
//   }) =>
//       VillageUserResponse(
//         message: message ?? this.message,
//         status: status ?? this.status,
//         data: data ?? this.data,
//       );

//   factory VillageUserResponse.fromRawJson(String str) =>
//       VillageUserResponse.fromJson(json.decode(str));

//   String toRawJson() => json.encode(toJson());

//   factory VillageUserResponse.fromJson(Map<String, dynamic> json) =>
//       VillageUserResponse(
//         message: json["message"],
//         status: json["status"],
//         data: json["data"] == null
//             ? []
//             : List<Datum>.from(json["data"]!.map((x) => Datum.fromJson(x))),
//       );

//   Map<String, dynamic> toJson() => {
//         "message": message,
//         "status": status,
//         "data": data == null
//             ? []
//             : List<dynamic>.from(data!.map((x) => x.toJson())),
//       };
// }

// class Datum {
//   final String? id;
//   final String? name;
//   final String? userId;
//   final int? score;
//   final bool? isSelf;
//   final DateTime? createdAt;
//   final DateTime? updatedAt;

//   Datum({
//     this.id,
//     this.name,
//     this.userId,
//     this.score,
//     this.isSelf,
//     this.createdAt,
//     this.updatedAt,
//   });

//   Datum copyWith({
//     String? id,
//     String? name,
//     String? userId,
//     int? score,
//     bool? isSelf,
//     DateTime? createdAt,
//     DateTime? updatedAt,
//   }) =>
//       Datum(
//         id: id ?? this.id,
//         name: name ?? this.name,
//         userId: userId ?? this.userId,
//         score: score ?? this.score,
//         isSelf: isSelf ?? this.isSelf,
//         createdAt: createdAt ?? this.createdAt,
//         updatedAt: updatedAt ?? this.updatedAt,
//       );

//   factory Datum.fromRawJson(String str) => Datum.fromJson(json.decode(str));

//   String toRawJson() => json.encode(toJson());

//   factory Datum.fromJson(Map<String, dynamic> json) => Datum(
//         id: json["id"],
//         name: json["name"],
//         userId: json["user_id"],
//         score: json["score"],
//         isSelf: json["is_self"],
//         createdAt: json["created_at"] == null
//             ? null
//             : DateTime.parse(json["created_at"]),
//         updatedAt: json["updated_at"] == null
//             ? null
//             : DateTime.parse(json["updated_at"]),
//       );

//   Map<String, dynamic> toJson() => {
//         "id": id,
//         "name": name,
//         "user_id": userId,
//         "score": score,
//         "is_self": isSelf,
//         "created_at": createdAt?.toIso8601String(),
//         "updated_at": updatedAt?.toIso8601String(),
//       };
// }
import 'dart:convert';

class VillageUserResponse {
  final String? message;
  final String? status;
  final Data? data;

  VillageUserResponse({
    this.message,
    this.status,
    this.data,
  });

  VillageUserResponse copyWith({
    String? message,
    String? status,
    Data? data,
  }) =>
      VillageUserResponse(
        message: message ?? this.message,
        status: status ?? this.status,
        data: data ?? this.data,
      );

  factory VillageUserResponse.fromRawJson(String str) =>
      VillageUserResponse.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory VillageUserResponse.fromJson(Map<String, dynamic> json) =>
      VillageUserResponse(
        message: json["message"],
        status: json["status"],
        data: json["data"] == null ? null : Data.fromJson(json["data"]),
      );

  Map<String, dynamic> toJson() => {
        "message": message,
        "status": status,
        "data": data?.toJson(),
      };
}

class Data {
  final List<VillageUser>? villageUsers;
  final VillageExercises? villageExercises;

  Data({
    this.villageUsers,
    this.villageExercises,
  });

  Data copyWith({
    List<VillageUser>? villageUsers,
    VillageExercises? villageExercises,
  }) =>
      Data(
        villageUsers: villageUsers ?? this.villageUsers,
        villageExercises: villageExercises ?? this.villageExercises,
      );

  factory Data.fromRawJson(String str) => Data.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory Data.fromJson(Map<String, dynamic> json) => Data(
        villageUsers: json["village_users"] == null
            ? []
            : List<VillageUser>.from(
                json["village_users"]!.map((x) => VillageUser.fromJson(x))),
        villageExercises: json["village_exercises"] == null
            ? null
            : VillageExercises.fromJson(json["village_exercises"]),
      );

  Map<String, dynamic> toJson() => {
        "village_users": villageUsers == null
            ? []
            : List<dynamic>.from(villageUsers!.map((x) => x.toJson())),
        "village_exercises": villageExercises?.toJson(),
      };
}

class VillageExercises {
  final String? id;
  final String? title;
  final String? thumbnailUrl;
  final String? mediaDuration;
  final String? protectiveFactors;
  final dynamic protectiveFactorSkills;
  final int? potentialImpacts;
  final int? difficultyLevel;
  final String? evidenceSources;
  final dynamic originalContributors;
  final String? exerciseType;
  final bool? isForWorkouts;
  final bool? isDeleted;
  final bool? isPopular;
  final DateTime? createdAt;
  final DateTime? updatedAt;
  final dynamic message;
  final String? ideaName;
  final bool? isVillageExercise;

  VillageExercises({
    this.id,
    this.title,
    this.thumbnailUrl,
    this.mediaDuration,
    this.protectiveFactors,
    this.protectiveFactorSkills,
    this.potentialImpacts,
    this.difficultyLevel,
    this.evidenceSources,
    this.originalContributors,
    this.exerciseType,
    this.isForWorkouts,
    this.isDeleted,
    this.isPopular,
    this.createdAt,
    this.updatedAt,
    this.message,
    this.ideaName,
    this.isVillageExercise,
  });

  VillageExercises copyWith({
    String? id,
    String? title,
    String? thumbnailUrl,
    String? mediaDuration,
    String? protectiveFactors,
    dynamic protectiveFactorSkills,
    int? potentialImpacts,
    int? difficultyLevel,
    String? evidenceSources,
    dynamic originalContributors,
    String? exerciseType,
    bool? isForWorkouts,
    bool? isDeleted,
    bool? isPopular,
    DateTime? createdAt,
    DateTime? updatedAt,
    dynamic message,
    String? ideaName,
    bool? isVillageExercise,
  }) =>
      VillageExercises(
        id: id ?? this.id,
        title: title ?? this.title,
        thumbnailUrl: thumbnailUrl ?? this.thumbnailUrl,
        mediaDuration: mediaDuration ?? this.mediaDuration,
        protectiveFactors: protectiveFactors ?? this.protectiveFactors,
        protectiveFactorSkills:
            protectiveFactorSkills ?? this.protectiveFactorSkills,
        potentialImpacts: potentialImpacts ?? this.potentialImpacts,
        difficultyLevel: difficultyLevel ?? this.difficultyLevel,
        evidenceSources: evidenceSources ?? this.evidenceSources,
        originalContributors: originalContributors ?? this.originalContributors,
        exerciseType: exerciseType ?? this.exerciseType,
        isForWorkouts: isForWorkouts ?? this.isForWorkouts,
        isDeleted: isDeleted ?? this.isDeleted,
        isPopular: isPopular ?? this.isPopular,
        createdAt: createdAt ?? this.createdAt,
        updatedAt: updatedAt ?? this.updatedAt,
        message: message ?? this.message,
        ideaName: ideaName ?? this.ideaName,
        isVillageExercise: isVillageExercise ?? this.isVillageExercise,
      );

  factory VillageExercises.fromRawJson(String str) =>
      VillageExercises.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory VillageExercises.fromJson(Map<String, dynamic> json) =>
      VillageExercises(
        id: json["id"],
        title: json["title"],
        thumbnailUrl: json["thumbnail_url"],
        mediaDuration: json["media_duration"],
        protectiveFactors: json["protective_factors"],
        protectiveFactorSkills: json["protective_factor_skills"],
        potentialImpacts: json["potential_impacts"],
        difficultyLevel: json["difficulty_level"],
        evidenceSources: json["evidence_sources"],
        originalContributors: json["original_contributors"],
        exerciseType: json["exercise_type"],
        isForWorkouts: json["is_for_workouts"],
        isDeleted: json["is_deleted"],
        isPopular: json["is_popular"],
        createdAt: json["created_at"] == null
            ? null
            : DateTime.parse(json["created_at"]),
        updatedAt: json["updated_at"] == null
            ? null
            : DateTime.parse(json["updated_at"]),
        message: json["message"],
        ideaName: json["idea_name"],
        isVillageExercise: json["is_village_exercise"],
      );

  Map<String, dynamic> toJson() => {
        "id": id,
        "title": title,
        "thumbnail_url": thumbnailUrl,
        "media_duration": mediaDuration,
        "protective_factors": protectiveFactors,
        "protective_factor_skills": protectiveFactorSkills,
        "potential_impacts": potentialImpacts,
        "difficulty_level": difficultyLevel,
        "evidence_sources": evidenceSources,
        "original_contributors": originalContributors,
        "exercise_type": exerciseType,
        "is_for_workouts": isForWorkouts,
        "is_deleted": isDeleted,
        "is_popular": isPopular,
        "created_at": createdAt?.toIso8601String(),
        "updated_at": updatedAt?.toIso8601String(),
        "message": message,
        "idea_name": ideaName,
        "is_village_exercise": isVillageExercise,
      };
}

class VillageUser {
  final String? id;
  final String? name;
  final String? userId;
  final int? score;
  final bool? isSelf;
  final DateTime? createdAt;
  final DateTime? updatedAt;

  VillageUser({
    this.id,
    this.name,
    this.userId,
    this.score,
    this.isSelf,
    this.createdAt,
    this.updatedAt,
  });

  VillageUser copyWith({
    String? id,
    String? name,
    String? userId,
    int? score,
    bool? isSelf,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) =>
      VillageUser(
        id: id ?? this.id,
        name: name ?? this.name,
        userId: userId ?? this.userId,
        score: score ?? this.score,
        isSelf: isSelf ?? this.isSelf,
        createdAt: createdAt ?? this.createdAt,
        updatedAt: updatedAt ?? this.updatedAt,
      );

  factory VillageUser.fromRawJson(String str) =>
      VillageUser.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory VillageUser.fromJson(Map<String, dynamic> json) => VillageUser(
        id: json["id"],
        name: json["name"],
        userId: json["user_id"],
        score: json["score"],
        isSelf: json["is_self"],
        createdAt: json["created_at"] == null
            ? null
            : DateTime.parse(json["created_at"]),
        updatedAt: json["updated_at"] == null
            ? null
            : DateTime.parse(json["updated_at"]),
      );

  Map<String, dynamic> toJson() => {
        "id": id,
        "name": name,
        "user_id": userId,
        "score": score,
        "is_self": isSelf,
        "created_at": createdAt?.toIso8601String(),
        "updated_at": updatedAt?.toIso8601String(),
      };
}
