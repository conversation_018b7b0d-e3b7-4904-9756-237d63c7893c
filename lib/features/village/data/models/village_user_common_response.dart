// response/village_user_response.dart
class VillageUserCommonResponse {
  VillageUserCommonResponse({
    required this.message,
    required this.status,
    this.data,
  });

  final String message;
  final String status;
  final dynamic data;

  factory VillageUserCommonResponse.fromJson(Map<String, dynamic> json) =>
      VillageUserCommonResponse(
        message: json["message"],
        status: json["status"],
        data: json["data"],
      );

  Map<String, dynamic> toJson() => {
        "message": message,
        "status": status,
        "data": data,
      };
}
