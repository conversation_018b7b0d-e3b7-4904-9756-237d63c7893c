// request/update_village_user.dart
class UpdateVillageUser {
  UpdateVillageUser({
    required this.id, // Add the id
    required this.name,
    required this.score,
  });

  final String id;
  final String name;
  final int score;

  factory UpdateVillageUser.fromJson(Map<String, dynamic> json) =>
      UpdateVillageUser(
        id: json["id"], // Correct deserialization for ID
        name: json["name"],
        score: json["score"],
      );

  Map<String, dynamic> toJson() => {
        "id": id, // Include ID in toJson
        "name": name,
        "score": score,
      };
}
