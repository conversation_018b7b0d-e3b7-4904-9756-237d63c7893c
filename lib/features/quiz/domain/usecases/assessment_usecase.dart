import '../../../../core/usecase/usecase.dart';
import '../../../../shared/models/result.dart';
import '../../data/models/assessment_response.dart';
import '../../data/repositories/quiz_repository.dart';

class GetQuestionsUseCase
    implements UseCase<Result<AssessmentResponse>, NoParams> {
  GetQuestionsUseCase(this._repository);
  final QuizRepository _repository;

  @override
  Future<Result<AssessmentResponse>> call(NoParams params) async {
    return _repository.getQuestions();
  }
}
