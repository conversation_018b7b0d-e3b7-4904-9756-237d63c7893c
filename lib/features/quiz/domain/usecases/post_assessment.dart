import 'package:gotcha_mfg_app/core/usecase/usecase.dart';
import 'package:gotcha_mfg_app/features/quiz/data/models/assessment_result_response.dart';
import 'package:gotcha_mfg_app/features/quiz/data/repositories/quiz_repository.dart';
import 'package:gotcha_mfg_app/shared/models/result.dart';

import '../../data/models/assessment_submit.dart';

/// UseCase for post assessment
class PostAssessmentUseCase
    implements
        UseCase<Result<AssessmentResultResponse>, PostAssessmentRequest> {
  /// Constructor
  PostAssessmentUseCase(this._repository);
  final QuizRepository _repository;

  @override
  Future<Result<AssessmentResultResponse>> call(
      PostAssessmentRequest request) async {
    return _repository.postAssessment(request);
  }
}
