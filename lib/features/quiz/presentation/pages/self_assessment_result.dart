import 'package:auto_route/auto_route.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:gap/gap.dart';
import 'package:gotcha_mfg_app/config/router/app_router.gr.dart';
import 'package:gotcha_mfg_app/core/extensions/app_text_theme_extension.dart';
import 'package:gotcha_mfg_app/features/quiz/data/models/assessment_submit.dart';
import 'package:gotcha_mfg_app/shared/widgets/loading_widget.dart';

import '../../../../config/theme/app_colors.dart';
import '../../../../core/mixpanel_service.dart';
import '../../../../core/utils/platform_utils.dart';
import '../../../../core/utils/snackbar_service.dart';
import '../../../../locator.dart';
import '../../../../shared/widgets/app_header.dart';
import '../../../../shared/widgets/primary_button.dart';
import '../../../../shared/widgets/retry_widget.dart';
import '../cubit/assessment/quiz_cubit.dart';
import '../widgets/build_tip.dart';

@RoutePage()
class SelfAssesmentResultPage extends StatefulWidget {
  final Map<String, int> selectedAnswers;
  const SelfAssesmentResultPage({super.key, required this.selectedAnswers});

  @override
  State<SelfAssesmentResultPage> createState() =>
      _SelfAssesmentResultPageState();
}

class _SelfAssesmentResultPageState extends State<SelfAssesmentResultPage> {
  @override
  void initState() {
    super.initState();
    sl<MixpanelService>().trackScreenView('Self Assessment - Result Page',
        properties: {'Code': 'screen_view.self_assessment_result_page'});

    context
        .read<QuizCubit>()
        .postAssessment(PostAssessmentRequest(answers: widget.selectedAnswers));
  }

  bool areValuesAboveTen(int value1, int value2, int value3) {
    return value1 > 10 && value2 > 10 && value3 > 10;
  }

  @override
  Widget build(BuildContext context) {
    final textTheme = Theme.of(context).textTheme;
    final size = MediaQuery.sizeOf(context);
    return BlocConsumer<QuizCubit, QuizState>(
      listener: (context, state) {
        if (state is QuizError) {
          SnackBarService.error(
            context: context,
            message: state.message,
          );
        }
      },
      builder: (context, state) {
        if (state is QuizLoading) {
          return SizedBox(
              height: size.height,
              child: const LoadingWidget(color: Colors.white));
        }
        if (state is QuizError) {
          return RetryWidget(
            onRetry: () => context.read<QuizCubit>().getQuestions(),
            color: Colors.white,
          );
        }
        if (state is QuizAssessmentLoaded) {
          final assessmentresult = state.assessmentResponse;
          return Scaffold(
            appBar: AppBar(
              toolbarHeight: 0,
              elevation: 0,
              systemOverlayStyle: const SystemUiOverlayStyle(
                statusBarColor: Colors.white,
                systemNavigationBarIconBrightness: Brightness.dark,
                statusBarBrightness: Brightness.light,
                statusBarIconBrightness: Brightness.dark,
                systemNavigationBarColor: Colors.white,
              ),
            ),
            body: Padding(
              padding: EdgeInsets.only(
                left: 8,
                right: 8,
                top: isIos ? 4 : 8,
              ),
              child: Column(
                children: [
                  const AppHeader(
                    trailing: 'Results',
                    currentStep: 0,
                    totalSteps: 0,
                    title: "Self-assessment",
                  ),
                  Expanded(
                    child: Container(
                      color: AppColors.navy,
                      child: Container(
                        height: size.height,
                        decoration: const BoxDecoration(
                          borderRadius: BorderRadius.only(
                            topLeft: Radius.circular(30),
                            topRight: Radius.circular(30),
                          ),
                          color: AppColors.grey,
                        ),
                        child: Padding(
                          padding: const EdgeInsets.symmetric(horizontal: 24.0),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              const Spacer(),
                              Align(
                                alignment: Alignment.topLeft,
                                child: Text('Your scores:',
                                    style: textTheme.bodyEmphasis),
                              ),
                              const Gap(16),
                              buildResultTipItem(
                                  'Depression:  ${assessmentresult.data?.depression ?? 'N/A'}  / 42',
                                  textTheme),
                              buildResultTipItem(
                                  'Anxiety:  ${assessmentresult.data?.anxiety ?? 'N/A'}  / 42',
                                  textTheme),
                              buildResultTipItem(
                                  'Stress:  ${assessmentresult.data?.stress ?? 'N/A'}  / 42',
                                  textTheme),
                              const Gap(16),
                              buildResultTipItem(
                                  'Higher scores indicate higher degrees of stress, anxiety or depression.',
                                  textTheme),
                              const Gap(16),
                              if ((assessmentresult.data?.depression ?? 0) >
                                      28 ||
                                  (assessmentresult.data?.anxiety ?? 0) > 28 ||
                                  (assessmentresult.data?.stress ?? 0) > 28)
                                buildResultTipItem(
                                    'For high scores like yours, we strongly recommend consulting a mental health professional.',
                                    textTheme)
                              else if ((assessmentresult.data?.depression ??
                                          0) >
                                      14 ||
                                  (assessmentresult.data?.anxiety ?? 0) > 14 ||
                                  (assessmentresult.data?.stress ?? 0) > 14)
                                buildResultTipItem(
                                    'For moderate scores like yours, consider reaching out to a counselor or therapist.',
                                    textTheme)
                              else
                                buildResultTipItem(
                                    'Your scores are within normal ranges. Maintaining healthy habits is recommended.',
                                    textTheme),
                              const Spacer(),
                              SizedBox(
                                width: double.infinity,
                                child: PrimaryButton(
                                  text: 'Explore professional support',
                                  onPressed: () {
                                    context.router.replaceAll(
                                      [
                                        HomeRoute(index: 0),
                                      ],
                                      updateExistingRoutes: false,
                                    );
                                    // mixpanel
                                    sl<MixpanelService>().trackButtonClick(
                                        'Explore Professional Support',
                                        properties: {
                                          'Page':
                                              'Self Assessment - Result Page',
                                          'Code':
                                              'click.self_assessment_result_page.explore_professional_support'
                                        });
                                  },
                                ),
                              ),
                              const Gap(24),
                              areValuesAboveTen(
                                      assessmentresult.data?.anxiety ?? 0,
                                      assessmentresult.data?.depression ?? 0,
                                      (assessmentresult.data?.stress) ?? 0)
                                  ? SizedBox(
                                      width: size.width - 50,
                                      child: PrimaryButton(
                                        text: 'Explore gym exercises',
                                        onPressed: () {
                                          // context.replaceRoute(const HomeRoute());
                                          context.router.replaceAll(
                                            [
                                              HomeRoute(index: 1),
                                            ],
                                            updateExistingRoutes: false,
                                          );
                                          // mixpanel
                                          sl<MixpanelService>()
                                              .trackButtonClick(
                                                  'Explore Gym Exercises',
                                                  properties: {
                                                'Page':
                                                    'Self Assessment - Result Page',
                                                'Code':
                                                    'click.self_assessment_result_page.explore_gym_exercises'
                                              });
                                        },
                                      ),
                                    )
                                  : const SizedBox(),
                              const Gap(24),
                            ],
                          ),
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          );
        }
        return const SizedBox();
      },
    );
  }
}
