import 'package:auto_route/auto_route.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:gotcha_mfg_app/config/router/app_router.gr.dart';
import 'package:gotcha_mfg_app/core/utils/platform_utils.dart';
import 'package:gotcha_mfg_app/features/quiz/data/models/assessment_response.dart';
import '../../../../config/theme/app_colors.dart';
import '../../../../core/mixpanel_service.dart';
import '../../../../locator.dart';
import '../../../../shared/widgets/app_header.dart';
import '../../../../shared/widgets/primary_button.dart';
import '../widgets/multiple_question.dart';

@RoutePage()
class SelfAssessmentStartPage extends StatefulWidget {
  final List<Datum>? assessments;
  const SelfAssessmentStartPage({super.key, required this.assessments});

  @override
  State<SelfAssessmentStartPage> createState() =>
      _SelfAssessmentStartPageState();
}

class _SelfAssessmentStartPageState extends State<SelfAssessmentStartPage> {
  final PageController _pageController = PageController();
  Map<int, int?> allSelectedAnswers = {};
  int pageVal = 0;
  List<Widget> pages = [];
  bool isCurrentPageAnswered = false;

  @override
  void initState() {
    super.initState();
    sl<MixpanelService>().trackScreenView('Self Assessment - Start Page',
        properties: {'Code': 'screen_view.self_assessment_start_page'});

    _generatePages();
  }

  @override
  void didUpdateWidget(covariant SelfAssessmentStartPage oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.assessments != widget.assessments) {
      _generatePages();
    }
  }

  void _generatePages() {
    if (widget.assessments != null) {
      final questionsData = widget.assessments!;
      pages = questionsData.map((question) {
        return MultipleQuestion(
          questions: [question],
          onAnswerSelected: onAnswerSelected,
          initialSelections: allSelectedAnswers,
        );
      }).toList();
    }
  }

  void onAnswerSelected(Map<int, int?> selectedAnswers) {
    setState(() {
      allSelectedAnswers = {...allSelectedAnswers, ...selectedAnswers};
      isCurrentPageAnswered = true;
    });
  }

  void onPageChange(int index) {
    if (!isCurrentPageAnswered && index > pageVal) {
      _pageController.animateToPage(
        pageVal,
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
      );
      return;
    }

    setState(() {
      pageVal = index;
      if (widget.assessments != null && index < widget.assessments!.length) {
        isCurrentPageAnswered =
            allSelectedAnswers.containsKey(index + 1) ? true : false;
      }
    });

    if (index == pages.length - 1) {
      _onLastPageSwiped();
    }
  }

  void _previousPage() {
    if (pageVal > 0) {
      _pageController.previousPage(
          duration: const Duration(milliseconds: 300), curve: Curves.easeInOut);
    } else {
      Navigator.of(context).pop();
    }
  }

  void _nextPage() {
    if (pageVal < pages.length - 1 && isCurrentPageAnswered) {
      _pageController.nextPage(
          duration: const Duration(milliseconds: 300), curve: Curves.easeInOut);
    }
  }

  void _onLastPageSwiped() {
    if (isCurrentPageAnswered) {
      final output = transformAnswers(allSelectedAnswers);
      context.pushRoute(SelfAssesmentResultRoute(
          selectedAnswers: output['answers'] as Map<String, int>));

      // Navigator.of(context).push(
      //   MaterialPageRoute(
      //     builder: (context) => SelfAssesmentResultPage(
      //         selectedAnswers: output['answers'] as Map<String, int>),
      //   ),
      // );
    }
  }

  Map<String, dynamic> transformAnswers(Map<int, int?> inputMap) {
    final answers = <String, int>{};
    for (int i = 0; i < widget.assessments!.length; i++) {
      final questionId = widget.assessments![i].questionNumber;
      answers['$questionId'] = inputMap[i] ?? 0;
    }
    return {"answers": answers};
  }

  @override
  Widget build(BuildContext context) {
    final size = MediaQuery.sizeOf(context);
    return Scaffold(
      appBar: AppBar(
        toolbarHeight: 0,
        elevation: 0,
        systemOverlayStyle: const SystemUiOverlayStyle(
          statusBarColor: Colors.white,
          systemNavigationBarIconBrightness: Brightness.dark,
          statusBarBrightness: Brightness.light,
          statusBarIconBrightness: Brightness.dark,
          systemNavigationBarColor: Colors.white,
        ),
      ),
      body: Padding(
        padding: EdgeInsets.fromLTRB(
          8,
          isIos ? 4 : 8,
          8,
          0,
        ),
        child: Column(
          children: [
            AppHeader(
              trailing: 'Question ${pageVal + 1} / ${pages.length}',
              currentStep: 0,
              totalSteps: 0,
              title: "Self-assessment",
            ),
            Expanded(
              child: Container(
                color: AppColors.navy,
                child: Container(
                  decoration: const BoxDecoration(
                    borderRadius: BorderRadius.only(
                      topLeft: Radius.circular(30),
                      topRight: Radius.circular(30),
                    ),
                    color: AppColors.grey,
                  ),
                  child: Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 0.0),
                    child: PageView.builder(
                      controller: _pageController,
                      onPageChanged: onPageChange,
                      physics: isCurrentPageAnswered
                          ? const ClampingScrollPhysics()
                          : const NeverScrollableScrollPhysics(),
                      scrollDirection: Axis.vertical,
                      itemCount: pages.length,
                      itemBuilder: (context, index) {
                        return Padding(
                          padding: const EdgeInsets.all(16.0),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.center,
                            children: [
                              Align(
                                alignment: Alignment.centerLeft,
                                child: GestureDetector(
                                  onTap: _previousPage,
                                  child: const Icon(
                                    Icons.arrow_back,
                                    color: AppColors.coral,
                                  ),
                                ),
                              ),
                              const Spacer(flex: 2),
                              pages[index],
                              const Spacer(flex: 3),
                            ],
                          ),
                        );
                      },
                    ),
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
      floatingActionButtonLocation: FloatingActionButtonLocation.centerFloat,
      floatingActionButton: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 20.0),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            SizedBox(
                width: size.width - 50,
                child: PrimaryButton(
                  text: pageVal == pages.length - 1 ? 'Submit' : 'Next',
                  onPressed: () {
                    if (isCurrentPageAnswered) {
                      if (pageVal == pages.length - 1) {
                        _onLastPageSwiped();
                      } else {
                        _nextPage();
                      }
                    } else {
                      //  CustomCupertinoAlertDialog.showAlertPopup(
                      //               context,
                      //               title: 'Alert',
                      //               content:
                      //                   "Please select one first.",
                      //             );
                    }
                    // mixpanel
                    sl<MixpanelService>().trackButtonClick('Next', properties: {
                      'Page': 'Self Assessment - Start Page',
                      'Code': 'click.self_assessment_start_page.next'
                    });
                  },
                  isEnabled: isCurrentPageAnswered,
                )),
          ],
        ),
      ),
    );
  }
}
