import 'package:auto_route/auto_route.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:gap/gap.dart';
import 'package:gotcha_mfg_app/config/router/app_router.gr.dart';
import 'package:gotcha_mfg_app/core/extensions/app_text_theme_extension.dart';
import 'package:gotcha_mfg_app/features/quiz/presentation/cubit/assessment/quiz_cubit.dart';
import 'package:gotcha_mfg_app/shared/widgets/curved_separator.dart';
import 'package:gotcha_mfg_app/shared/widgets/loading_widget.dart';
import 'package:gotcha_mfg_app/shared/widgets/show_info.dart';

import '../../../../config/theme/app_colors.dart';
import '../../../../core/mixpanel_service.dart';
import '../../../../core/utils/platform_utils.dart';
import '../../../../core/utils/snackbar_service.dart';
import '../../../../locator.dart';
import '../../../../shared/widgets/app_header.dart';
import '../../../../shared/widgets/primary_button.dart';
import '../../../../shared/widgets/retry_widget.dart';
import '../widgets/build_tip.dart';

@RoutePage()
class SelfAssesmentWelcomePage extends StatefulWidget {
  const SelfAssesmentWelcomePage({super.key});

  @override
  State<SelfAssesmentWelcomePage> createState() =>
      _SelfAssesmentWelcomePageState();
}

class _SelfAssesmentWelcomePageState extends State<SelfAssesmentWelcomePage> {
  @override
  void initState() {
    super.initState();
    sl<MixpanelService>().trackScreenView('Self Assessment - Welcome Page',
        properties: {'Code': 'screen_view.self_assessment_welcome_page'});
    context.read<QuizCubit>().getQuestions();
  }

  @override
  Widget build(BuildContext context) {
    final textTheme = Theme.of(context).textTheme;
    final size = MediaQuery.sizeOf(context);
    return BlocConsumer<QuizCubit, QuizState>(
      listener: (context, state) {
        if (state is QuizError) {
          SnackBarService.error(
            context: context,
            message: state.message,
          );
        }
      },
      builder: (context, state) {
        if (state is QuizLoading) {
          return SizedBox(
            height: size.height,
            child: const LoadingWidget(color: Colors.white),
          );
        }
        if (state is QuizError) {
          return RetryWidget(
            onRetry: () => context.read<QuizCubit>().getQuestions(),
            color: Colors.white,
          );
        }
        if (state is QuizQuestionsLoaded) {
          final assessment = state.assessmentResponse;
          return Scaffold(
            appBar: AppBar(
              toolbarHeight: 0,
              elevation: 0,
              systemOverlayStyle: const SystemUiOverlayStyle(
                statusBarColor: Colors.white,
                systemNavigationBarIconBrightness: Brightness.dark,
                statusBarBrightness: Brightness.light,
                statusBarIconBrightness: Brightness.dark,
                systemNavigationBarColor: Colors.white,
              ),
            ),
            floatingActionButtonLocation:
                FloatingActionButtonLocation.centerFloat,
            floatingActionButton: Padding(
              padding: const EdgeInsets.symmetric(horizontal: 24.0),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  SizedBox(
                    width: size.width - 50,
                    child: PrimaryButton(
                      text: 'Start',
                      onPressed: () {
                        context.pushRoute(
                            SelfAssessmentStartRoute(assessments: assessment));
                        // mixpanel
                        sl<MixpanelService>()
                            .trackButtonClick('Start', properties: {
                          'Page': 'Self Assessment - Welcome Page',
                          'Code': 'click.self_assessment_welcome_page.start'
                        });

                        // Navigator.of(context).push(
                        //   MaterialPageRoute(
                        //     builder: (context) => SelfAssessmentStartPage(
                        //         assessments: assessment),
                        //   ),
                        // );
                      },
                    ),
                  ),
                ],
              ),
            ),
            body: Padding(
              padding: EdgeInsets.only(
                left: 8,
                right: 8,
                top: isIos ? 4 : 8,
              ),
              child: Column(
                children: [
                  const AppHeader(
                    currentStep: 0,
                    totalSteps: 0,
                    title: "Self-assessment",
                  ),
                  const CurvedSeparator(
                    outerColor: AppColors.navy,
                    innerColor: AppColors.grey,
                  ),
                  Expanded(
                    child: Container(
                      decoration: const BoxDecoration(
                        color: AppColors.grey,
                      ),
                      child: Padding(
                        padding: const EdgeInsets.symmetric(horizontal: 24.0),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Align(
                              alignment: Alignment.centerLeft,
                              child: GestureDetector(
                                onTap: () => Navigator.of(context).pop(),
                                child: const Icon(
                                  Icons.arrow_back,
                                  color: AppColors.coral,
                                ),
                              ),
                            ),
                            const Spacer(),
                            Align(
                              alignment: Alignment.topLeft,
                              child: Text('A few tips before you get started:',
                                  style: textTheme.bodyEmphasis),
                            ),
                            const SizedBox(height: 16),
                            buildTipItem(
                                'Please read each statement and answer based on how you have felt over the past week.',
                                1,
                                textTheme,
                                true),
                            const SizedBox(height: 12),
                            buildTipItem('There are no right or wrong answers.',
                                2, textTheme, false),
                            const SizedBox(height: 12),
                            buildTipItem(
                                'Don\'t spend too much time on any question.',
                                3,
                                textTheme,
                                false),
                            const Gap(24),
                            const InfoMessage(
                              color: AppColors.lightRed,
                              message:
                                  'This self-assessment is inspired by the DASS-21, a well-used tool designed to measure the negative emotional states of depression, anxiety and stress. It\'s both used clinically and outside of clinics.',
                            ),
                            const Spacer(flex: 2),
                          ],
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          );
        }
        return const SizedBox();
      },
    );
  }
}
