part of 'quiz_cubit.dart';

sealed class QuizState extends Equatable {
  const QuizState();

  @override
  List<Object> get props => [];
}

final class QuizInitial extends QuizState {}

final class QuizLoading extends QuizState {}

final class QuizQuestionsLoaded extends QuizState {
  final List<Datum>? assessmentResponse;

  const QuizQuestionsLoaded({required this.assessmentResponse});
}

final class QuizError extends QuizState {
  final String message;

  const QuizError({required this.message});
}

final class QuizAssessmentLoaded extends QuizState {
  final AssessmentResultResponse assessmentResponse;

  const QuizAssessmentLoaded({required this.assessmentResponse});
}
