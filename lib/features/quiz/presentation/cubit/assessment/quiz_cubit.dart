import 'package:bloc/bloc.dart';
import 'package:equatable/equatable.dart';

import '../../../../../core/usecase/usecase.dart';
import '../../../data/models/assessment_response.dart';
import '../../../data/models/assessment_result_response.dart';
import '../../../data/models/assessment_submit.dart';
import '../../../domain/usecases/assessment_usecase.dart';
import '../../../domain/usecases/post_assessment.dart';

part 'quiz_state.dart';

class QuizCubit extends Cubit<QuizState> {
  QuizCubit(this._getQuestionsUseCase, this._postAssessmentUseCase)
      : super(QuizInitial());

  final GetQuestionsUseCase _getQuestionsUseCase;
  final PostAssessmentUseCase _postAssessmentUseCase;

  Future<void> getQuestions() async {
    emit(QuizLoading());
    final result = await _getQuestionsUseCase.call(NoParams());
    if (result.isSuccess) {
      emit(QuizQuestionsLoaded(assessmentResponse: result.data!.data));
    } else {
      emit(QuizError(message: result.error!));
    }
  }

  /// Post Assessment
  Future<void> postAssessment(PostAssessmentRequest request) async {
    emit(QuizLoading());
    final result = await _postAssessmentUseCase.call(request);
    if (result.isSuccess) {
      emit(QuizAssessmentLoaded(assessmentResponse: result.data!));
    } else {
      emit(QuizError(message: result.error!));
    }
  }
}
