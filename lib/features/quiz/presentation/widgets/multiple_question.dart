import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:gotcha_mfg_app/core/extensions/app_text_theme_extension.dart';
import 'package:gotcha_mfg_app/features/quiz/data/models/assessment_response.dart';

import '../../../exercise/presentation/widgets/reflection_choice.dart';

class MultipleQuestion extends StatefulWidget {
  final List<Datum> questions;
  final Function(Map<int, int?>) onAnswerSelected;
  final Map<int, int?> initialSelections;

  const MultipleQuestion({
    super.key,
    required this.questions,
    required this.onAnswerSelected,
    required this.initialSelections,
  });

  @override
  State<MultipleQuestion> createState() => _MultipleQuestionState();
}

class _MultipleQuestionState extends State<MultipleQuestion> {
  List<String> ans = ['Never', 'Sometimes', 'Often', 'Almost always'];
  Map<int, int?> selectedAnswers = {}; // <questionId, selectedAnswerIndex>

  @override
  void initState() {
    super.initState();
    selectedAnswers = widget.initialSelections;
  }

  @override
  Widget build(BuildContext context) {
    final textTheme = Theme.of(context).textTheme;
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        const Gap(12),
        Padding(
          padding: const EdgeInsets.symmetric(
            horizontal: 16,
            vertical: 16,
          ),
          child: ListView.builder(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            itemCount: widget.questions.length,
            itemBuilder: (context, questionIndex) {
              final currentQuestion = widget.questions[questionIndex];
              return Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Text('Over the last week', style: textTheme.agLabels),
                  const Gap(4),
                  Text(currentQuestion.questionText ?? 'N/A',
                      style: textTheme.bodyEmphasis),
                  const Gap(20),
                  ListView.builder(
                    shrinkWrap: true,
                    physics: const NeverScrollableScrollPhysics(),
                    itemCount: ans.length,
                    itemBuilder: (context, answerIndex) {
                      return Align(
                        alignment: Alignment.centerLeft,
                        child: ReflectionChoice(
                          isSelected:
                              selectedAnswers[currentQuestion.questionNumber] ==
                                  answerIndex,
                          title: ans[answerIndex],
                          onTap: () {
                            setState(() {
                              selectedAnswers[currentQuestion.questionNumber!
                                  .toInt()] = answerIndex;
                              widget.onAnswerSelected(selectedAnswers);
                            });
                          },
                        ),
                      );
                    },
                  ),
                ],
              );
            },
          ),
        )
      ],
    );
  }
}
