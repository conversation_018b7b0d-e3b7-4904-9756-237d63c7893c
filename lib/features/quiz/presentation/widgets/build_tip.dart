import 'package:flutter/material.dart';
import 'package:gotcha_mfg_app/core/extensions/app_text_theme_extension.dart';

import '../../../../config/theme/app_colors.dart';

Widget buildTipItem(String text, int? num, TextTheme textTheme, bool past) {
  return Row(
    crossAxisAlignment: CrossAxisAlignment.start,
    children: [
      Text(
        ' $num.',
        style: textTheme.ralewayRegular.copyWith(
          fontSize: 17,
          color: AppColors.navy,
        ),
      ),
      const SizedBox(width: 8),
      Expanded(
        child: past
            ? RichText(
                text: TextSpan(
                  style: textTheme.ralewayRegular.copyWith(
                    height: 1.5,
                    fontSize: 17,
                    color: AppColors.navy, // or your default text color
                  ),
                  children: [
                    const TextSpan(
                        text:
                            'Please read each statement and answer based on how you have felt over '),
                    TextSpan(
                      text: 'the past week',
                      style: textTheme.ralewayBold.copyWith(
                          fontSize: 17,
                          color: AppColors
                              .navy // or your specific red color like AppColors.red
                          ),
                    ),
                    const TextSpan(text: '.'),
                  ],
                ),
              )
            : Text(
                text,
                style: textTheme.ralewayRegular.copyWith(
                  fontSize: 17,
                  color: AppColors.navy,
                ),
              ),
      ),
    ],
  );
}

Widget buildResultTipItem(String text, TextTheme textTheme) {
  return Padding(
    padding: const EdgeInsets.symmetric(vertical: 2.0),
    child: Text(text, style: textTheme.bodyRegular),
  );
}
