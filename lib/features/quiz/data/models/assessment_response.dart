import 'dart:convert';

class AssessmentResponse {
  final String? message;
  final String? status;
  final List<Datum>? data;

  AssessmentResponse({
    this.message,
    this.status,
    this.data,
  });

  AssessmentResponse copyWith({
    String? message,
    String? status,
    List<Datum>? data,
  }) =>
      AssessmentResponse(
        message: message ?? this.message,
        status: status ?? this.status,
        data: data ?? this.data,
      );

  factory AssessmentResponse.fromRawJson(String str) =>
      AssessmentResponse.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory AssessmentResponse.fromJson(Map<String, dynamic> json) =>
      AssessmentResponse(
        message: json["message"],
        status: json["status"],
        data: json["data"] == null
            ? []
            : List<Datum>.from(json["data"].map((x) => Datum.fromJson(x))),
      );

  Map<String, dynamic> toJson() => {
        "message": message,
        "status": status,
        "data": data == null
            ? []
            : List<dynamic>.from(data!.map((x) => x.toJson())),
      };
}

class Datum {
  final String? id;
  final String? questionText;
  final String? category;
  final int? questionNumber;

  Datum({
    this.id,
    this.questionText,
    this.category,
    this.questionNumber,
  });

  Datum copyWith({
    String? id,
    String? questionText,
    String? category,
    int? questionNumber,
  }) =>
      Datum(
        id: id ?? this.id,
        questionText: questionText ?? this.questionText,
        category: category ?? this.category,
        questionNumber: questionNumber ?? this.questionNumber,
      );

  factory Datum.fromRawJson(String str) => Datum.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory Datum.fromJson(Map<String, dynamic> json) => Datum(
        id: json["id"],
        questionText: json["question_text"],
        category: json["category"],
        questionNumber: json["question_number"],
      );

  Map<String, dynamic> toJson() => {
        "id": id,
        "question_text": questionText,
        "category": category,
        "question_number": questionNumber,
      };
}
