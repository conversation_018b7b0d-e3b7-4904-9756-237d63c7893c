import 'dart:convert';

class PostAssessmentRequest {
  final Map<String, int>? answers;

  PostAssessmentRequest({
    this.answers,
  });

  PostAssessmentRequest copyWith({
    Map<String, int>? answers,
  }) =>
      PostAssessmentRequest(
        answers: answers ?? this.answers,
      );

  factory PostAssessmentRequest.fromRawJson(String str) =>
      PostAssessmentRequest.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory PostAssessmentRequest.fromJson(Map<String, dynamic> json) =>
      PostAssessmentRequest(
        answers: Map.from(json["answers"]!)
            .map((k, v) => MapEntry<String, int>(k, v)),
      );

  Map<String, dynamic> toJson() => {
        "answers":
            Map.from(answers!).map((k, v) => MapEntry<String, dynamic>(k, v)),
      };
}
