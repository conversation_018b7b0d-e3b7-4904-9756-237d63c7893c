import 'dart:convert';

class AssessmentResultResponse {
  final String? message;
  final String? status;
  final Data? data;

  AssessmentResultResponse({
    this.message,
    this.status,
    this.data,
  });

  AssessmentResultResponse copyWith({
    String? message,
    String? status,
    Data? data,
  }) =>
      AssessmentResultResponse(
        message: message ?? this.message,
        status: status ?? this.status,
        data: data ?? this.data,
      );

  factory AssessmentResultResponse.fromRawJson(String str) =>
      AssessmentResultResponse.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory AssessmentResultResponse.fromJson(Map<String, dynamic> json) =>
      AssessmentResultResponse(
        message: json["message"],
        status: json["status"],
        data: json["data"] == null ? null : Data.fromJson(json["data"]),
      );

  Map<String, dynamic> toJson() => {
        "message": message,
        "status": status,
        "data": data?.toJson(),
      };
}

class Data {
  final int? depression;
  final int? anxiety;
  final int? stress;

  Data({
    this.depression,
    this.anxiety,
    this.stress,
  });

  Data copyWith({
    int? depression,
    int? anxiety,
    int? stress,
  }) =>
      Data(
        depression: depression ?? this.depression,
        anxiety: anxiety ?? this.anxiety,
        stress: stress ?? this.stress,
      );

  factory Data.fromRawJson(String str) => Data.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory Data.fromJson(Map<String, dynamic> json) => Data(
        depression: json["depression"],
        anxiety: json["anxiety"],
        stress: json["stress"],
      );

  Map<String, dynamic> toJson() => {
        "depression": depression,
        "anxiety": anxiety,
        "stress": stress,
      };
}
