import 'package:dio/dio.dart';
import 'package:firebase_crashlytics/firebase_crashlytics.dart';
import 'package:gotcha_mfg_app/features/quiz/data/models/assessment_submit.dart';

import '../../../../shared/models/result.dart';
import '../models/assessment_response.dart';
import '../models/assessment_result_response.dart';

abstract class QuizRemoteDataSource {
  Future<Result<AssessmentResponse>> getQuestions();
  Future<Result<AssessmentResultResponse>> postAssessment(
      PostAssessmentRequest request);
}

class QuizRemoteDataSourceImpl implements QuizRemoteDataSource {
  QuizRemoteDataSourceImpl(this._dio);
  final Dio _dio;

  @override
  Future<Result<AssessmentResponse>> getQuestions() async {
    try {
      final response = await _dio.get('/app/assessment/question');
      if (response.statusCode == 200) {
        final data = response.data;
        final assessmentResponse = AssessmentResponse.fromJson(data);
        return Result.success(assessmentResponse);
      } else {
        return Result.failure(
            '${response.statusCode} - ${response.data['message']}');
      }
    } catch (e) {
      FirebaseCrashlytics.instance.recordError(e, StackTrace.current);
      return Result.failure(
          'An error occurred during getting questions: ${e.toString()}');
    }
  }

  @override
  @override
  Future<Result<AssessmentResultResponse>> postAssessment(
      PostAssessmentRequest request) async {
    try {
      final response =
          await _dio.post('/app/assessment/score', data: request.toJson());
      if (response.statusCode == 200) {
        final data = response.data;
        final assessmentResultResponse =
            AssessmentResultResponse.fromJson(data);
        return Result.success(assessmentResultResponse);
      } else {
        return Result.failure(
            '${response.statusCode} - ${response.data['message']}');
      }
    } catch (e) {
      FirebaseCrashlytics.instance.recordError(e, StackTrace.current);
      return Result.failure(
          'An error occurred during posting assessment: ${e.toString()}');
    }
  }
}
