import 'package:gotcha_mfg_app/features/quiz/data/models/assessment_submit.dart';

import '../../../../shared/models/result.dart';
import '../models/assessment_response.dart';
import '../models/assessment_result_response.dart';
import '../repositories/quiz_repository.dart';
import 'quiz_remote_datasource.dart';

class QuizRepositoryImpl implements QuizRepository {
  QuizRepositoryImpl(this._remoteDataSource);
  final QuizRemoteDataSource _remoteDataSource;

  @override
  Future<Result<AssessmentResponse>> getQuestions() async {
    return await _remoteDataSource.getQuestions();
  }

  @override
  Future<Result<AssessmentResultResponse>> postAssessment(
      PostAssessmentRequest request) async {
    return await _remoteDataSource.postAssessment(request);
  }
}
