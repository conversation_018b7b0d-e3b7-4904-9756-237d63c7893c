import 'package:gotcha_mfg_app/core/usecase/usecase.dart';

import 'package:gotcha_mfg_app/shared/models/result.dart';

import '../../data/models/profile_response.dart';
import '../../data/repositories/profile_repository.dart';

class GetProfileLinksUseCase
    implements UseCase<Result<ProfileResponse>, NoParams> {
  GetProfileLinksUseCase(this._repository);

  final ProfileRepository _repository;

  @override
  Future<Result<ProfileResponse>> call(NoParams params) async {
    return _repository.getProfileLinks();
  }
}
