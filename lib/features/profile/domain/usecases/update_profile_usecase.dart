import '../../../../core/usecase/usecase.dart';
import '../../../../shared/models/result.dart';
import '../../data/models/profile_edit_request.dart';
import '../../data/models/profile_update_response.dart';
import '../../data/repositories/profile_repository.dart';

class UpdateProfileUseCase
    implements UseCase<Result<ProfileUpdateResponse>, UpdateProfileParams> {
  final ProfileRepository _repository;

  UpdateProfileUseCase(this._repository);

  @override
  Future<Result<ProfileUpdateResponse>> call(UpdateProfileParams params) async {
    return await _repository.updateProfile(params);
  }
}
