import 'package:gotcha_mfg_app/features/profile/data/models/gym_req.dart';

import '../../../../core/usecase/usecase.dart';
import '../../../../shared/models/result.dart';
import '../../data/models/gym_history_response.dart';
import '../../data/repositories/profile_repository.dart';

class GetGymHistoryUseCase
    implements UseCase<Result<GymHistoryResponse>, GymReq> {
  final ProfileRepository _repository;

  GetGymHistoryUseCase(this._repository);

  @override
  Future<Result<GymHistoryResponse>> call(GymReq request) async {
    return await _repository.getGymHistory(request);
  }
}
