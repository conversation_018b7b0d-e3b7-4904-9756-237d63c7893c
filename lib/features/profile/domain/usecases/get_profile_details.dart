import '../../../../core/usecase/usecase.dart';
import '../../../../shared/models/result.dart';
import '../../data/models/profile_detail_reponse.dart';
import '../../data/repositories/profile_repository.dart';

class GetProfileDetailUseCase
    implements UseCase<Result<ProfileDetailResponse>, NoParams> {
  /// Constructor
  GetProfileDetailUseCase(this._repository);

  final ProfileRepository _repository;

  @override
  Future<Result<ProfileDetailResponse>> call(NoParams noParams) async {
    return _repository.getProfileDetail();
  }
}
