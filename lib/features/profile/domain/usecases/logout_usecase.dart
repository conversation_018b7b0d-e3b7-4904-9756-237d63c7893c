// api_call_name_use_case.dart
import 'package:gotcha_mfg_app/core/usecase/usecase.dart';
import 'package:gotcha_mfg_app/features/profile/data/models/logout_req.dart';
import 'package:gotcha_mfg_app/features/profile/data/models/logout_response.dart';
import 'package:gotcha_mfg_app/features/profile/data/repositories/profile_repository.dart';
import 'package:gotcha_mfg_app/shared/models/result.dart';

/// UseCase for logout
class LogoutUseCase
    implements UseCase<Result<LogoutResponse>, LogoutRequestParams> {
  /// Constructor
  LogoutUseCase(this._repository);

  final ProfileRepository _repository;

  @override
  Future<Result<LogoutResponse>> call(LogoutRequestParams request) async {
    return _repository.logout(request);
  }
}
