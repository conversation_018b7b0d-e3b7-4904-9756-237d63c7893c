class UpdateProfileParams {
  final String? firstName;
  final String? lastName;
  final String? gender;
  final String? age;
  final List<String?> identityGroup;
  final String? otherGender;
  final String? otherDemographics;

  UpdateProfileParams({
    required this.otherGender,
    required this.otherDemographics,
    required this.firstName,
    required this.lastName,
    required this.gender,
    required this.age,
    required this.identityGroup,
  });

  Map<String, dynamic> to<PERSON>son() {
    return {
      'first_name': firstName,
      'last_name': lastName,
      'gender': gender,
      'age': age,
      'identity_group': identityGroup,
      "other_gender": otherGender,
      "other_demographics": otherDemographics,
    };
  }
}
