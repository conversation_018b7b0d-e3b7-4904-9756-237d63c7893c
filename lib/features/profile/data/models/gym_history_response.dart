import 'dart:convert';

class GymHistoryResponse {
  final String? message;
  final String? status;
  final Data? data;

  GymHistoryResponse({
    this.message,
    this.status,
    this.data,
  });

  GymHistoryResponse copyWith({
    String? message,
    String? status,
    Data? data,
  }) =>
      GymHistoryResponse(
        message: message ?? this.message,
        status: status ?? this.status,
        data: data ?? this.data,
      );

  factory GymHistoryResponse.fromRawJson(String str) =>
      GymHistoryResponse.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory GymHistoryResponse.fromJson(Map<String, dynamic> json) =>
      GymHistoryResponse(
        message: json["message"],
        status: json["status"],
        data: json["data"] == null ? null : Data.fromJson(json["data"]),
      );

  Map<String, dynamic> toJson() => {
        "message": message,
        "status": status,
        "data": data?.toJson(),
      };
}

class Data {
  final GymHistory? gymHistory;

  Data({
    this.gymHistory,
  });

  Data copyWith({
    GymHistory? gymHistory,
  }) =>
      Data(
        gymHistory: gymHistory ?? this.gymHistory,
      );

  factory Data.fromRawJson(String str) => Data.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory Data.fromJson(Map<String, dynamic> json) => Data(
        gymHistory: json["gym_history"] == null
            ? null
            : GymHistory.fromJson(json["gym_history"]),
      );

  Map<String, dynamic> toJson() => {
        "gym_history": gymHistory?.toJson(),
      };
}

class GymHistory {
  final int? previousCheckIns;
  final int? previousExercises;
  final List<MostCommonCheckInTag>? mostCommonCheckInTags;
  final List<Exercise>? exercises;
  final List<Workout>? workouts;
  final List<CheckIn>? checkIns;
  final DateTime? filterDate;

  GymHistory({
    this.previousCheckIns,
    this.previousExercises,
    this.mostCommonCheckInTags,
    this.exercises,
    this.workouts,
    this.checkIns,
    this.filterDate,
  });

  GymHistory copyWith({
    int? previousCheckIns,
    int? previousExercises,
    List<MostCommonCheckInTag>? mostCommonCheckInTags,
    List<Exercise>? exercises,
    List<Workout>? workouts,
    List<CheckIn>? checkIns,
    DateTime? filterDate,
  }) =>
      GymHistory(
        previousCheckIns: previousCheckIns ?? this.previousCheckIns,
        previousExercises: previousExercises ?? this.previousExercises,
        mostCommonCheckInTags:
            mostCommonCheckInTags ?? this.mostCommonCheckInTags,
        exercises: exercises ?? this.exercises,
        workouts: workouts ?? this.workouts,
        checkIns: checkIns ?? this.checkIns,
        filterDate: filterDate ?? this.filterDate,
      );

  factory GymHistory.fromRawJson(String str) =>
      GymHistory.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory GymHistory.fromJson(Map<String, dynamic> json) => GymHistory(
        previousCheckIns: json["previous_check_ins"],
        previousExercises: json["previous_exercises"],
        mostCommonCheckInTags: json["most_common_check_in_tags"] == null
            ? []
            : List<MostCommonCheckInTag>.from(json["most_common_check_in_tags"]!
                .map((x) => MostCommonCheckInTag.fromJson(x))),
        exercises: json["exercises"] == null
            ? []
            : List<Exercise>.from(
                json["exercises"]!.map((x) => Exercise.fromJson(x))),
        workouts: json["workouts"] == null
            ? []
            : List<Workout>.from(
                json["workouts"]!.map((x) => Workout.fromJson(x))),
        checkIns: json["check_ins"] == null
            ? []
            : List<CheckIn>.from(
                json["check_ins"]!.map((x) => CheckIn.fromJson(x))),
        filterDate: json["filter_date"] == null
            ? null
            : DateTime.parse(json["filter_date"]),
      );

  Map<String, dynamic> toJson() => {
        "previous_check_ins": previousCheckIns,
        "previous_exercises": previousExercises,
        "most_common_check_in_tags": mostCommonCheckInTags == null
            ? []
            : List<dynamic>.from(mostCommonCheckInTags!.map((x) => x.toJson())),
        "exercises": exercises == null
            ? []
            : List<dynamic>.from(exercises!.map((x) => x.toJson())),
        "workouts": workouts == null
            ? []
            : List<dynamic>.from(workouts!.map((x) => x.toJson())),
        "check_ins": checkIns == null
            ? []
            : List<dynamic>.from(checkIns!.map((x) => x.toJson())),
        "filter_date":
            "${filterDate!.year.toString().padLeft(4, '0')}-${filterDate!.month.toString().padLeft(2, '0')}-${filterDate!.day.toString().padLeft(2, '0')}",
      };
}

class CheckIn {
  final String? checkInId;
  final DateTime? createdAt;
  final String? feeling;
  final String? feelingId;
  final String? iconUrl;
  final Tags? tags;

  CheckIn({
    this.checkInId,
    this.createdAt,
    this.feeling,
    this.feelingId,
    this.iconUrl,
    this.tags,
  });

  CheckIn copyWith({
    String? checkInId,
    DateTime? createdAt,
    String? feeling,
    String? feelingId,
    String? iconUrl,
    Tags? tags,
  }) =>
      CheckIn(
        checkInId: checkInId ?? this.checkInId,
        createdAt: createdAt ?? this.createdAt,
        feeling: feeling ?? this.feeling,
        feelingId: feelingId ?? this.feelingId,
        iconUrl: iconUrl ?? this.iconUrl,
        tags: tags ?? this.tags,
      );

  factory CheckIn.fromRawJson(String str) => CheckIn.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory CheckIn.fromJson(Map<String, dynamic> json) => CheckIn(
        checkInId: json["check_in_id"],
        createdAt: json["created_at"] == null
            ? null
            : DateTime.parse(json["created_at"]),
        feeling: json["feeling"],
        feelingId: json["feeling_id"],
        iconUrl: json["icon_url"],
        tags: json["tags"] == null ? null : Tags.fromJson(json["tags"]),
      );

  Map<String, dynamic> toJson() => {
        "check_in_id": checkInId,
        "created_at": createdAt?.toIso8601String(),
        "feeling": feeling,
        "feeling_id": feelingId,
        "icon_url": iconUrl,
        "tags": tags?.toJson(),
      };
}

class Tags {
  final List<String>? emotions;
  final List<String>? factors;
  final List<dynamic>? gratitude;

  Tags({
    this.emotions,
    this.factors,
    this.gratitude,
  });

  Tags copyWith({
    List<String>? emotions,
    List<String>? factors,
    List<dynamic>? gratitude,
  }) =>
      Tags(
        emotions: emotions ?? this.emotions,
        factors: factors ?? this.factors,
        gratitude: gratitude ?? this.gratitude,
      );

  factory Tags.fromRawJson(String str) => Tags.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory Tags.fromJson(Map<String, dynamic> json) => Tags(
        emotions: json["emotions"] == null
            ? []
            : List<String>.from(json["emotions"]!.map((x) => x)),
        factors: json["factors"] == null
            ? []
            : List<String>.from(json["factors"]!.map((x) => x)),
        gratitude: json["gratitude"] == null
            ? []
            : List<dynamic>.from(json["gratitude"]!.map((x) => x)),
      );

  Map<String, dynamic> toJson() => {
        "emotions":
            emotions == null ? [] : List<dynamic>.from(emotions!.map((x) => x)),
        "factors":
            factors == null ? [] : List<dynamic>.from(factors!.map((x) => x)),
        "gratitude": gratitude == null
            ? []
            : List<dynamic>.from(gratitude!.map((x) => x)),
      };
}

class Exercise {
  final String? id;
  final String? userId;
  final String? type;
  final String? activityId;
  final dynamic seriesId;
  final bool? isCompleted;
  final DateTime? completedAt;
  final bool? isViewed;
  final String? timeSpent;
  final DateTime? createdAt;
  final DateTime? updatedAt;
  final String? exerciseTitle;
  final dynamic exerciseText;
  final String? thumbnailUrl;
  final List<ReflectionQuestion>? reflectionQuestions;

  Exercise({
    this.id,
    this.userId,
    this.type,
    this.activityId,
    this.seriesId,
    this.isCompleted,
    this.completedAt,
    this.isViewed,
    this.timeSpent,
    this.createdAt,
    this.updatedAt,
    this.exerciseTitle,
    this.exerciseText,
    this.thumbnailUrl,
    this.reflectionQuestions,
  });

  Exercise copyWith({
    String? id,
    String? userId,
    String? type,
    String? activityId,
    dynamic seriesId,
    bool? isCompleted,
    DateTime? completedAt,
    bool? isViewed,
    String? timeSpent,
    DateTime? createdAt,
    DateTime? updatedAt,
    String? exerciseTitle,
    dynamic exerciseText,
    String? thumbnailUrl,
    List<ReflectionQuestion>? reflectionQuestions,
  }) =>
      Exercise(
        id: id ?? this.id,
        userId: userId ?? this.userId,
        type: type ?? this.type,
        activityId: activityId ?? this.activityId,
        seriesId: seriesId ?? this.seriesId,
        isCompleted: isCompleted ?? this.isCompleted,
        completedAt: completedAt ?? this.completedAt,
        isViewed: isViewed ?? this.isViewed,
        timeSpent: timeSpent ?? this.timeSpent,
        createdAt: createdAt ?? this.createdAt,
        updatedAt: updatedAt ?? this.updatedAt,
        exerciseTitle: exerciseTitle ?? this.exerciseTitle,
        exerciseText: exerciseText ?? this.exerciseText,
        thumbnailUrl: thumbnailUrl ?? this.thumbnailUrl,
        reflectionQuestions: reflectionQuestions ?? this.reflectionQuestions,
      );

  factory Exercise.fromRawJson(String str) =>
      Exercise.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory Exercise.fromJson(Map<String, dynamic> json) => Exercise(
        id: json["id"],
        userId: json["user_id"],
        type: json["type"],
        activityId: json["activity_id"],
        seriesId: json["series_id"],
        isCompleted: json["is_completed"],
        completedAt: json["completed_at"] == null
            ? null
            : DateTime.parse(json["completed_at"]),
        isViewed: json["is_viewed"],
        timeSpent: json["time_spent"],
        createdAt: json["created_at"] == null
            ? null
            : DateTime.parse(json["created_at"]),
        updatedAt: json["updated_at"] == null
            ? null
            : DateTime.parse(json["updated_at"]),
        exerciseTitle: json["exercise_title"],
        exerciseText: json["exercise_text"],
        thumbnailUrl: json["thumbnail_url"],
        reflectionQuestions: json["reflection_questions"] == null
            ? []
            : List<ReflectionQuestion>.from(json["reflection_questions"]!
                .map((x) => ReflectionQuestion.fromJson(x))),
      );

  Map<String, dynamic> toJson() => {
        "id": id,
        "user_id": userId,
        "type": type,
        "activity_id": activityId,
        "series_id": seriesId,
        "is_completed": isCompleted,
        "completed_at": completedAt?.toIso8601String(),
        "is_viewed": isViewed,
        "time_spent": timeSpent,
        "created_at": createdAt?.toIso8601String(),
        "updated_at": updatedAt?.toIso8601String(),
        "exercise_title": exerciseTitle,
        "exercise_text": exerciseText,
        "thumbnail_url": thumbnailUrl,
        "reflection_questions": reflectionQuestions == null
            ? []
            : List<dynamic>.from(reflectionQuestions!.map((x) => x.toJson())),
      };
}

class ReflectionQuestion {
  final String? text;
  final bool? isMultiChoice;
  final String? answer;

  ReflectionQuestion({
    this.text,
    this.isMultiChoice,
    this.answer,
  });

  ReflectionQuestion copyWith({
    String? text,
    bool? isMultiChoice,
    String? answer,
  }) =>
      ReflectionQuestion(
        text: text ?? this.text,
        isMultiChoice: isMultiChoice ?? this.isMultiChoice,
        answer: answer ?? this.answer,
      );

  factory ReflectionQuestion.fromRawJson(String str) =>
      ReflectionQuestion.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory ReflectionQuestion.fromJson(Map<String, dynamic> json) =>
      ReflectionQuestion(
        text: json["text"],
        isMultiChoice: json["is_multi_choice"],
        answer: json["answer"],
      );

  Map<String, dynamic> toJson() => {
        "text": text,
        "is_multi_choice": isMultiChoice,
        "answer": answer,
      };
}

class MostCommonCheckInTag {
  final String? feeling;
  final int? count;
  final String? iconUrl;

  MostCommonCheckInTag({
    this.feeling,
    this.count,
    this.iconUrl,
  });

  MostCommonCheckInTag copyWith({
    String? feeling,
    int? count,
    String? iconUrl,
  }) =>
      MostCommonCheckInTag(
        feeling: feeling ?? this.feeling,
        count: count ?? this.count,
        iconUrl: iconUrl ?? this.iconUrl,
      );

  factory MostCommonCheckInTag.fromRawJson(String str) =>
      MostCommonCheckInTag.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory MostCommonCheckInTag.fromJson(Map<String, dynamic> json) =>
      MostCommonCheckInTag(
        feeling: json["feeling"],
        count: json["count"],
        iconUrl: json["icon_url"],
      );

  Map<String, dynamic> toJson() => {
        "feeling": feeling,
        "count": count,
        "icon_url": iconUrl,
      };
}

class Workout {
  final String? id;
  final String? userId;
  final String? type;
  final String? activityId;
  final String? seriesId;
  final bool? isCompleted;
  final DateTime? completedAt;
  final bool? isViewed;
  final String? timeSpent;
  final DateTime? createdAt;
  final DateTime? updatedAt;
  final int? orderOfExercise;
  final String? title;
  final String? thumbnailUrl;
  final String? exerciseId;
  final List<ReflectionQuestion>? reflectionQuestions;

  Workout({
    this.id,
    this.userId,
    this.type,
    this.activityId,
    this.seriesId,
    this.isCompleted,
    this.completedAt,
    this.isViewed,
    this.timeSpent,
    this.createdAt,
    this.updatedAt,
    this.orderOfExercise,
    this.title,
    this.thumbnailUrl,
    this.exerciseId,
    this.reflectionQuestions,
  });

  Workout copyWith({
    String? id,
    String? userId,
    String? type,
    String? activityId,
    String? seriesId,
    bool? isCompleted,
    DateTime? completedAt,
    bool? isViewed,
    String? timeSpent,
    DateTime? createdAt,
    DateTime? updatedAt,
    int? orderOfExercise,
    String? title,
    String? thumbnailUrl,
    String? exerciseId,
    List<ReflectionQuestion>? reflectionQuestions,
  }) =>
      Workout(
        id: id ?? this.id,
        userId: userId ?? this.userId,
        type: type ?? this.type,
        activityId: activityId ?? this.activityId,
        seriesId: seriesId ?? this.seriesId,
        isCompleted: isCompleted ?? this.isCompleted,
        completedAt: completedAt ?? this.completedAt,
        isViewed: isViewed ?? this.isViewed,
        timeSpent: timeSpent ?? this.timeSpent,
        createdAt: createdAt ?? this.createdAt,
        updatedAt: updatedAt ?? this.updatedAt,
        orderOfExercise: orderOfExercise ?? this.orderOfExercise,
        title: title ?? this.title,
        thumbnailUrl: thumbnailUrl ?? this.thumbnailUrl,
        exerciseId: exerciseId ?? this.exerciseId,
        reflectionQuestions: reflectionQuestions ?? this.reflectionQuestions,
      );

  factory Workout.fromRawJson(String str) => Workout.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory Workout.fromJson(Map<String, dynamic> json) => Workout(
        id: json["id"],
        userId: json["user_id"],
        type: json["type"],
        activityId: json["activity_id"],
        seriesId: json["series_id"],
        isCompleted: json["is_completed"],
        completedAt: json["completed_at"] == null
            ? null
            : DateTime.parse(json["completed_at"]),
        isViewed: json["is_viewed"],
        timeSpent: json["time_spent"],
        createdAt: json["created_at"] == null
            ? null
            : DateTime.parse(json["created_at"]),
        updatedAt: json["updated_at"] == null
            ? null
            : DateTime.parse(json["updated_at"]),
        orderOfExercise: json["order_of_exercise"],
        title: json["title"],
        thumbnailUrl: json["thumbnail_url"],
        exerciseId: json["exercise_id"],
        reflectionQuestions: json["reflection_questions"] == null
            ? []
            : List<ReflectionQuestion>.from(json["reflection_questions"]!
                .map((x) => ReflectionQuestion.fromJson(x))),
      );

  Map<String, dynamic> toJson() => {
        "id": id,
        "user_id": userId,
        "type": type,
        "activity_id": activityId,
        "series_id": seriesId,
        "is_completed": isCompleted,
        "completed_at": completedAt?.toIso8601String(),
        "is_viewed": isViewed,
        "time_spent": timeSpent,
        "created_at": createdAt?.toIso8601String(),
        "updated_at": updatedAt?.toIso8601String(),
        "order_of_exercise": orderOfExercise,
        "title": title,
        "thumbnail_url": thumbnailUrl,
        "exercise_id": exerciseId,
        "reflection_questions": reflectionQuestions == null
            ? []
            : List<dynamic>.from(reflectionQuestions!.map((x) => x.toJson())),
      };
}
