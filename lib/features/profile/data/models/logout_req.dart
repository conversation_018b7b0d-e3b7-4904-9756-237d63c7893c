class LogoutRequestParams {
  final String? id;

  LogoutRequestParams({required this.id});

  // Factory method to create a ForgotRequestParams instance from JSON
  factory LogoutRequestParams.fromJson(Map<String, dynamic> json) {
    return LogoutRequestParams(
      id: json['id'],
    );
  }

  // Method to convert the ForgotRequestParams instance to JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
    };
  }
}
