class GymReq {
  final String date;
  final int? limit;

  GymReq({
    required this.date,
    this.limit = 1000,
  });

  // Convert the model to JSON
  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = {
      "filter_date": date,
    };

    // Only add limit to the JSON if it's not null
    // if (limit != null) {
    //   data["limit"] = limit;
    // }

    return data;
  }
}
