// import 'dart:convert';

// class ProfileResponse {
//   final String? message;
//   final String? status;
//   final Data? data;

//   ProfileResponse({
//     this.message,
//     this.status,
//     this.data,
//   });

//   ProfileResponse copyWith({
//     String? message,
//     String? status,
//     Data? data,
//   }) =>
//       ProfileResponse(
//         message: message ?? this.message,
//         status: status ?? this.status,
//         data: data ?? this.data,
//       );

//   factory ProfileResponse.fromRawJson(String str) =>
//       ProfileResponse.fromJson(json.decode(str));

//   String toRawJson() => json.encode(toJson());

//   factory ProfileResponse.fromJson(Map<String, dynamic> json) =>
//       ProfileResponse(
//         message: json["message"],
//         status: json["status"],
//         data: json["data"] == null ? null : Data.fromJson(json["data"]),
//       );

//   Map<String, dynamic> toJson() => {
//         "message": message,
//         "status": status,
//         "data": data?.toJson(),
//       };
// }

// class Data {
//   final ProfileLinks? profileLinks;
//   final bool? isSignUpCompleted;

//   Data({
//     this.profileLinks,
//     this.isSignUpCompleted,
//   });

//   Data copyWith({
//     ProfileLinks? profileLinks,
//     bool? isSignUpCompleted,
//   }) =>
//       Data(
//         profileLinks: profileLinks ?? this.profileLinks,
//         isSignUpCompleted: isSignUpCompleted ?? this.isSignUpCompleted,
//       );

//   factory Data.fromRawJson(String str) => Data.fromJson(json.decode(str));

//   String toRawJson() => json.encode(toJson());

//   factory Data.fromJson(Map<String, dynamic> json) => Data(
//         profileLinks: json["profile_links"] == null
//             ? null
//             : ProfileLinks.fromJson(json["profile_links"]),
//         isSignUpCompleted: json["is_sign_up_completed"],
//       );

//   Map<String, dynamic> toJson() => {
//         "profile_links": profileLinks?.toJson(),
//         "is_sign_up_completed": isSignUpCompleted,
//       };
// }

// class ProfileLinks {
//   final String? id;
//   final String? suggestedImprovements;
//   final String? contactUs;
//   final String? exportMyData;
//   final String? help;
//   final String? parents;
//   final String? teachers;
//   final String? managers;
//   final String? helpOthers;
//   final String? privacyPolicy;
//   final String? termsAndConditions;
//   final String? faq;
//   final String? voluntaryMembership;
//   final DateTime? createdAt;
//   final DateTime? updatedAt;

//   ProfileLinks({
//     this.id,
//     this.suggestedImprovements,
//     this.contactUs,
//     this.exportMyData,
//     this.help,
//     this.parents,
//     this.teachers,
//     this.managers,
//     this.helpOthers,
//     this.privacyPolicy,
//     this.termsAndConditions,
//     this.faq,
//     this.voluntaryMembership,
//     this.createdAt,
//     this.updatedAt,
//   });

//   ProfileLinks copyWith({
//     String? id,
//     String? suggestedImprovements,
//     String? contactUs,
//     String? exportMyData,
//     String? help,
//     String? parents,
//     String? teachers,
//     String? managers,
//     String? helpOthers,
//     String? privacyPolicy,
//     String? termsAndConditions,
//     String? faq,
//     String? voluntaryMembership,
//     DateTime? createdAt,
//     DateTime? updatedAt,
//   }) =>
//       ProfileLinks(
//         id: id ?? this.id,
//         suggestedImprovements:
//             suggestedImprovements ?? this.suggestedImprovements,
//         contactUs: contactUs ?? this.contactUs,
//         exportMyData: exportMyData ?? this.exportMyData,
//         help: help ?? this.help,
//         parents: parents ?? this.parents,
//         teachers: teachers ?? this.teachers,
//         managers: managers ?? this.managers,
//         helpOthers: helpOthers ?? this.helpOthers,
//         privacyPolicy: privacyPolicy ?? this.privacyPolicy,
//         termsAndConditions: termsAndConditions ?? this.termsAndConditions,
//         faq: faq ?? this.faq,
//         voluntaryMembership: voluntaryMembership ?? this.voluntaryMembership,
//         createdAt: createdAt ?? this.createdAt,
//         updatedAt: updatedAt ?? this.updatedAt,
//       );

//   factory ProfileLinks.fromRawJson(String str) =>
//       ProfileLinks.fromJson(json.decode(str));

//   String toRawJson() => json.encode(toJson());

//   factory ProfileLinks.fromJson(Map<String, dynamic> json) => ProfileLinks(
//         id: json["id"],
//         suggestedImprovements: json["suggested_improvements"],
//         contactUs: json["contact_us"],
//         exportMyData: json["export_my_data"],
//         help: json["help"],
//         parents: json["parents"],
//         teachers: json["teachers"],
//         managers: json["managers"],
//         helpOthers: json["help_others"],
//         privacyPolicy: json["privacy_policy"],
//         termsAndConditions: json["terms_and_conditions"],
//         faq: json["faq"],
//         voluntaryMembership: json["voluntary_membership"],
//         createdAt: json["created_at"] == null
//             ? null
//             : DateTime.parse(json["created_at"]),
//         updatedAt: json["updated_at"] == null
//             ? null
//             : DateTime.parse(json["updated_at"]),
//       );

//   Map<String, dynamic> toJson() => {
//         "id": id,
//         "suggested_improvements": suggestedImprovements,
//         "contact_us": contactUs,
//         "export_my_data": exportMyData,
//         "help": help,
//         "parents": parents,
//         "teachers": teachers,
//         "managers": managers,
//         "help_others": helpOthers,
//         "privacy_policy": privacyPolicy,
//         "terms_and_conditions": termsAndConditions,
//         "faq": faq,
//         "voluntary_membership": voluntaryMembership,
//         "created_at": createdAt?.toIso8601String(),
//         "updated_at": updatedAt?.toIso8601String(),
//       };
// }

import 'dart:convert';

class ProfileResponse {
  String? message;
  String? status;
  Data? data;

  ProfileResponse({
    this.message,
    this.status,
    this.data,
  });

  factory ProfileResponse.fromRawJson(String str) =>
      ProfileResponse.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory ProfileResponse.fromJson(Map<String, dynamic> json) =>
      ProfileResponse(
        message: json["message"],
        status: json["status"],
        data: json["data"] == null ? null : Data.fromJson(json["data"]),
      );

  Map<String, dynamic> toJson() => {
        "message": message,
        "status": status,
        "data": data?.toJson(),
      };
}

class Data {
  ProfileLinks? profileLinks;
  bool? isSignUpCompleted;

  Data({
    this.profileLinks,
    this.isSignUpCompleted,
  });

  factory Data.fromRawJson(String str) => Data.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory Data.fromJson(Map<String, dynamic> json) => Data(
        profileLinks: json["profile_links"] == null
            ? null
            : ProfileLinks.fromJson(json["profile_links"]),
        isSignUpCompleted: json["is_sign_up_completed"],
      );

  Map<String, dynamic> toJson() => {
        "profile_links": profileLinks?.toJson(),
        "is_sign_up_completed": isSignUpCompleted,
      };
}

class ProfileLinks {
  String? id;
  String? suggestedImprovements;
  String? contactUs;
  String? exportMyData;
  String? help;
  String? parents;
  String? teachers;
  String? managers;
  String? helpOthers;
  String? privacyPolicy;
  String? termsAndConditions;
  String? faq;
  String? voluntaryMembership;
  String? iosSupport;
  DateTime? createdAt;
  DateTime? updatedAt;

  ProfileLinks({
    this.id,
    this.suggestedImprovements,
    this.contactUs,
    this.exportMyData,
    this.help,
    this.parents,
    this.teachers,
    this.managers,
    this.helpOthers,
    this.privacyPolicy,
    this.termsAndConditions,
    this.faq,
    this.voluntaryMembership,
    this.iosSupport,
    this.createdAt,
    this.updatedAt,
  });

  factory ProfileLinks.fromRawJson(String str) =>
      ProfileLinks.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory ProfileLinks.fromJson(Map<String, dynamic> json) => ProfileLinks(
        id: json["id"],
        suggestedImprovements: json["suggested_improvements"],
        contactUs: json["contact_us"],
        exportMyData: json["export_my_data"],
        help: json["help"],
        parents: json["parents"],
        teachers: json["teachers"],
        managers: json["managers"],
        helpOthers: json["help_others"],
        privacyPolicy: json["privacy_policy"],
        termsAndConditions: json["terms_and_conditions"],
        faq: json["faq"],
        voluntaryMembership: json["voluntary_membership"],
        iosSupport: json["ios_support"],
        createdAt: json["created_at"] == null
            ? null
            : DateTime.parse(json["created_at"]),
        updatedAt: json["updated_at"] == null
            ? null
            : DateTime.parse(json["updated_at"]),
      );

  Map<String, dynamic> toJson() => {
        "id": id,
        "suggested_improvements": suggestedImprovements,
        "contact_us": contactUs,
        "export_my_data": exportMyData,
        "help": help,
        "parents": parents,
        "teachers": teachers,
        "managers": managers,
        "help_others": helpOthers,
        "privacy_policy": privacyPolicy,
        "terms_and_conditions": termsAndConditions,
        "faq": faq,
        "voluntary_membership": voluntaryMembership,
        "ios_support": iosSupport,
        "created_at": createdAt?.toIso8601String(),
        "updated_at": updatedAt?.toIso8601String(),
      };
}
