// response/village_user_response.dart
class LogoutResponse {
  LogoutResponse({
    required this.message,
    required this.status,
    this.data,
  });

  final String message;
  final String status;
  final dynamic data;

  factory LogoutResponse.fromJson(Map<String, dynamic> json) => LogoutResponse(
        message: json["message"],
        status: json["status"],
        data: json["data"],
      );

  Map<String, dynamic> toJson() => {
        "message": message,
        "status": status,
        "data": data,
      };
}
