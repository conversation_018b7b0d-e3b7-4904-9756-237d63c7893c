import 'package:dio/dio.dart';
import 'package:firebase_crashlytics/firebase_crashlytics.dart';
import 'package:gotcha_mfg_app/features/profile/data/models/gym_req.dart';
import 'package:gotcha_mfg_app/features/profile/data/models/logout_req.dart';
import 'package:gotcha_mfg_app/features/profile/data/models/logout_response.dart';
import 'package:gotcha_mfg_app/shared/models/result.dart';

import '../models/gym_history_response.dart';
import '../models/profile_detail_reponse.dart';
import '../models/profile_edit_request.dart';
import '../models/profile_response.dart';
import '../models/profile_update_response.dart';

abstract class ProfileRemoteDataSource {
  Future<Result<ProfileResponse>> getProfileLinks();
  Future<Result<ProfileDetailResponse>> getProfileDetail();
  Future<Result<ProfileUpdateResponse>> updateProfile(
      UpdateProfileParams params);
  Future<Result<GymHistoryResponse>> getGymHistory(GymReq request);
  Future<Result<LogoutResponse>> logout(LogoutRequestParams request);
}

class ProfileRemoteDataSourceImpl implements ProfileRemoteDataSource {
  ProfileRemoteDataSourceImpl(this._dio);

  final Dio _dio;

  @override
  Future<Result<ProfileResponse>> getProfileLinks() async {
    try {
      final response = await _dio.get('/app/profile/links');
      if (response.statusCode == 200) {
        final data = response.data;
        final profileResponse = ProfileResponse.fromJson(data);
        return Result.success(profileResponse);
      } else {
        return Result.failure(
            '${response.statusCode} - ${response.data['message']}');
      }
    } catch (e) {
      FirebaseCrashlytics.instance.recordError(e, StackTrace.current);
      return Result.failure(
          'An error occurred while getting profile links: ${e.toString()}');
    }
  }

  @override
  Future<Result<ProfileDetailResponse>> getProfileDetail() async {
    try {
      final response = await _dio.get('/app/profile');
      if (response.statusCode == 200) {
        final data = response.data;
        final profileDetailResponse = ProfileDetailResponse.fromJson(data);
        return Result.success(profileDetailResponse);
      } else {
        return Result.failure(
            '${response.statusCode} - ${response.data['message']}');
      }
    } catch (e) {
      FirebaseCrashlytics.instance.recordError(e, StackTrace.current);
      return Result.failure(
          'An error occurred during getting profile detail: ${e.toString()}');
    }
  }

  @override
  Future<Result<ProfileUpdateResponse>> updateProfile(
      UpdateProfileParams params) async {
    try {
      final response = await _dio.post('/app/profile', data: params.toJson());
      if (response.statusCode == 200) {
        final data = response.data;
        final profileUpdateResponse = ProfileUpdateResponse.fromJson(data);
        return Result.success(profileUpdateResponse);
      } else {
        return Result.failure(
            '${response.statusCode} - ${response.data['message']}');
      }
    } catch (e) {
      FirebaseCrashlytics.instance.recordError(e, StackTrace.current);
      return Result.failure(
          'An error occurred during updating profile: ${e.toString()}');
    }
  }

  @override
  Future<Result<GymHistoryResponse>> getGymHistory(GymReq request) async {
    try {
      final response = await _dio.get(
        '/app/history',
        queryParameters: request.toJson(),
      );
      if (response.statusCode == 200) {
        final data = response.data;
        final gymHistoryResponse = GymHistoryResponse.fromJson(data);
        return Result.success(gymHistoryResponse);
      } else {
        return Result.failure(
            '${response.statusCode} - ${response.data['message']}');
      }
    } catch (e) {
      FirebaseCrashlytics.instance.recordError(e, StackTrace.current);
      return Result.failure(
          'An error occurred during calling getGymHistory: ${e.toString()}');
    }
  }

  @override
  Future<Result<LogoutResponse>> logout(LogoutRequestParams request) async {
    try {
      final response = await _dio.post('/app/user/logout/${request.id}');
      if (response.statusCode == 200) {
        final data = response.data;
        final logoutResponse = LogoutResponse.fromJson(data);
        return Result.success(logoutResponse);
      } else {
        return Result.failure(
            '${response.statusCode} - ${response.data['message']}');
      }
    } catch (e) {
      return Result.failure('An error occurred during logout: ${e.toString()}');
    }
  }
}
