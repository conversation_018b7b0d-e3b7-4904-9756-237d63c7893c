import 'package:gotcha_mfg_app/features/profile/data/models/gym_history_response.dart';
import 'package:gotcha_mfg_app/features/profile/data/models/logout_req.dart';
import 'package:gotcha_mfg_app/features/profile/data/models/logout_response.dart';
import 'package:gotcha_mfg_app/shared/models/result.dart';

import '../models/gym_req.dart';
import '../models/profile_detail_reponse.dart';
import '../models/profile_edit_request.dart';
import '../models/profile_response.dart';
import '../models/profile_update_response.dart';
import '../repositories/profile_repository.dart';
import 'profile_remote_data_source.dart';

class ProfileRepositoryImpl implements ProfileRepository {
  ProfileRepositoryImpl(this._remoteDataSource);

  final ProfileRemoteDataSource _remoteDataSource;

  @override
  Future<Result<ProfileResponse>> getProfileLinks() async {
    return await _remoteDataSource.getProfileLinks();
  }

  @override
  Future<Result<ProfileDetailResponse>> getProfileDetail() async {
    return await _remoteDataSource.getProfileDetail();
  }

  @override
  Future<Result<ProfileUpdateResponse>> updateProfile(
      UpdateProfileParams params) async {
    return await _remoteDataSource.updateProfile(params);
  }

  @override
  Future<Result<GymHistoryResponse>> getGymHistory(GymReq req) async {
    return await _remoteDataSource.getGymHistory(req);
  }

  @override
  Future<Result<LogoutResponse>> logout(LogoutRequestParams request) async {
    return await _remoteDataSource.logout(request);
  }
}
