import 'package:gotcha_mfg_app/features/profile/data/models/gym_req.dart';
import 'package:gotcha_mfg_app/features/profile/data/models/logout_req.dart';
import 'package:gotcha_mfg_app/features/profile/data/models/logout_response.dart';
import 'package:gotcha_mfg_app/shared/models/result.dart';

import '../models/gym_history_response.dart';
import '../models/profile_detail_reponse.dart';
import '../models/profile_edit_request.dart';
import '../models/profile_response.dart';
import '../models/profile_update_response.dart';

abstract class ProfileRepository {
  Future<Result<ProfileResponse>> getProfileLinks();
  Future<Result<ProfileDetailResponse>> getProfileDetail();
  Future<Result<ProfileUpdateResponse>> updateProfile(
      UpdateProfileParams params);
  Future<Result<GymHistoryResponse>> getGymHistory(GymReq request);
  Future<Result<LogoutResponse>> logout(LogoutRequestParams request);
}
