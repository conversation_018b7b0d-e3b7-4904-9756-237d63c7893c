import 'package:intl/intl.dart';

String convertDate(String dateString) {
  DateTime dateTime = DateTime.parse(dateString);
  String formattedDate = DateFormat('d MMM yyyy').format(dateTime);
  String daySuffix = _getDaySuffix(dateTime.day);
  return '${dateTime.day}$daySuffix ${formattedDate.split(' ')[1]} ${formattedDate.split(' ')[2]}';
}

String _getDaySuffix(int day) {
  if (day >= 11 && day <= 13) {
    return 'th';
  }
  switch (day % 10) {
    case 1:
      return 'st';
    case 2:
      return 'nd';
    case 3:
      return 'rd';
    default:
      return 'th';
  }
}
