import 'package:flutter/material.dart';
import 'package:gotcha_mfg_app/config/theme/app_colors.dart';
import 'package:gotcha_mfg_app/core/extensions/app_text_theme_extension.dart';

class CustomTextField extends StatelessWidget {
  final String text;
  final String hinttext;

  const CustomTextField(
      {super.key, required this.text, required this.hinttext});
  @override
  Widget build(BuildContext context) {
    final textTheme = Theme.of(context).textTheme;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          text ?? '',
          style: textTheme.ralewayMedium.copyWith(
            fontSize: 14,
            color: AppColors.navy,
          ),
        ),
        const SizedBox(height: 8.0),
        TextField(
          style: textTheme.ralewayLight.copyWith(
            fontSize: 12,
            color: AppColors.navy,
          ),
          decoration: InputDecoration(
            hintText: hinttext,
            hintStyle: textTheme.ralewayLight.copyWith(
              fontSize: 14,
              color: AppColors.navy,
            ),
            contentPadding: const EdgeInsets.symmetric(
              horizontal: 16.0,
              vertical: 12.0,
            ),
            enabledBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(20.0),
              borderSide: BorderSide(
                color: Colors.grey.shade300,
                width: 1.0,
              ),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(20.0),
              // borderSide: BorderSide(
              //   color: Colors.blue.shade600,
              //   width: 1.5,
              // ),
            ),
            labelStyle: textTheme.ralewayLight.copyWith(
              fontSize: 14,
              color: AppColors.navy,
            ),
            filled: true,
            fillColor: Colors.grey.shade100,
          ),
        ),
      ],
    );
  }
}
