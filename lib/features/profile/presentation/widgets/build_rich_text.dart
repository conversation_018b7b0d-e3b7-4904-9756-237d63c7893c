import 'package:flutter/material.dart';
import 'package:gotcha_mfg_app/core/extensions/app_text_theme_extension.dart';

Widget buildRichText(
    String label, String text, TextTheme textTheme, BuildContext context,
    {bool isItalic = false, bool isCheck = false}) {
  return isCheck
      ? Padding(
          padding: const EdgeInsets.only(bottom: 4.0),
          child: SizedBox(
            width: MediaQuery.of(context).size.width / 2,
            child: Row(crossAxisAlignment: CrossAxisAlignment.start, children: [
              // flex: 2,
              Text(
                label,
                style: textTheme.labels.copyWith(
                  fontStyle: isItalic ? FontStyle.italic : FontStyle.normal,
                ),
                maxLines: 6,
                overflow: TextOverflow.ellipsis,
              ),
              Expanded(
                // flex: 3,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      text.isEmpty ? '--' : text,
                      style: textTheme.labels.copyWith(
                        fontStyle:
                            isItalic ? FontStyle.italic : FontStyle.normal,
                      ),
                      maxLines: 6,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ],
                ),
              )
            ]),
          )
          // ),

          )
      : Padding(
          padding: const EdgeInsets.only(bottom: 4.0),
          child: Row(
            children: [
              Flexible(
                child: RichText(
                  text: TextSpan(
                    children: [
                      TextSpan(
                        text: label,
                        style: textTheme.labels.copyWith(
                          fontStyle:
                              isItalic ? FontStyle.italic : FontStyle.normal,
                        ),
                      ),
                      TextSpan(
                        text: text.isEmpty ? '--' : text,
                        style: textTheme.labels.copyWith(
                          fontStyle:
                              isItalic ? FontStyle.italic : FontStyle.normal,
                        ),
                      ),
                    ],
                  ),
                  maxLines: 6,
                  overflow: TextOverflow.ellipsis,
                ),
              ),
            ],
          ),
        );
}
