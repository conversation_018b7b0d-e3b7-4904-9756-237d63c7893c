import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:gotcha_mfg_app/config/theme/app_colors.dart';
import 'package:gotcha_mfg_app/core/extensions/app_text_theme_extension.dart';
import 'package:gotcha_mfg_app/core/extensions/string_extensions.dart';
import 'package:gotcha_mfg_app/core/utils/app_print.dart';
import 'package:gotcha_mfg_app/shared/widgets/network_image_indicator.dart';

Widget buildCheckInIcon(
  String icon,
  String feeling,
  TextTheme textTheme,
) {
  info('-------feeling: $icon');
  return Container(
    height: 72,
    width: 56,
    // padding: const EdgeInsets.all(8),
    decoration: BoxDecoration(
      color: AppColors.grey,
      borderRadius: BorderRadius.circular(12),
    ),
    child: Column(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        SizedBox(
            width: 24,
            height: 24,
            child: NetworkImageWithIndicatorSmall(
              imageUrl: icon ?? '',
              fit: BoxFit.cover,
            )),
        // Assuming you have an extension or utility to get icon from feeling string
        // and 'icon' property is added to String extension for feeling
        // Image.asset(
        //   feeling
        //       .icon, // e.g., feeling == 'Happy' ? 'assets/happy_icon.png' : ...
        //   width: 22,
        //   color: AppColors.navy,
        // ),
        const Gap(4),
        Text(
          feeling.capitalizeFirstLetter(),
          style: textTheme.ralewayMedium.copyWith(fontSize: 8),
          maxLines: 1,
          overflow: TextOverflow.ellipsis,
        ),
      ],
    ),
  );
}
