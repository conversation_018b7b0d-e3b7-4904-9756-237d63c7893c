// import 'package:flutter/material.dart';
// import 'package:gotcha_mfg_app/core/extensions/app_text_theme_extension.dart';

// class TimePeriodDropdown extends StatefulWidget {
//   final ValueChanged<String?> onChanged; // Callback for value change

//   const TimePeriodDropdown(
//       {super.key, required this.onChanged}); // Constructor with callback

//   @override
//   State<TimePeriodDropdown> createState() => _TimePeriodDropdownState();
// }

// class _TimePeriodDropdownState extends State<TimePeriodDropdown> {
//   final List<String> timePeriods = [
//     '14 days',
//     '30 days',
//     '90 days',
//     '365 days'
//   ];
//   String? selectedTimePeriod;

//   @override
//   Widget build(BuildContext context) {
//     var textTheme = Theme.of(context).textTheme;

//     return DropdownButton<String>(
//       value: selectedTimePeriod,
//       icon: const Icon(Icons.keyboard_arrow_down, color: Colors.white),
//       iconSize: 24,
//       elevation: 16,
//       style: textTheme.ralewayMedium.copyWith(
//         fontSize: 13,
//         color: Colors.black,
//       ),
//       underline: const SizedBox(),
//       onChanged: (newValue) {
//         setState(() {
//           selectedTimePeriod = newValue;
//         });
//         widget.onChanged(newValue); // Call the callback with the new value
//       },
//       items: timePeriods.map<DropdownMenuItem<String>>((String value) {
//         return DropdownMenuItem<String>(
//           value: value == '' ? '14 days' : value,
//           child: Text(
//             value,
//             style: textTheme.ralewayMedium.copyWith(
//               fontSize: 13,
//               color: value == selectedTimePeriod ? Colors.white : Colors.black,
//             ),
//           ),
//         );
//       }).toList(),
//     );
//   }
// }

import 'package:flutter/material.dart';
import 'package:gotcha_mfg_app/config/theme/app_colors.dart';
import 'package:gotcha_mfg_app/core/extensions/app_text_theme_extension.dart';

class TimePeriodDropdown extends StatefulWidget {
  final ValueChanged<String?> onChanged;
  final String? value; // Add the value parameter

  const TimePeriodDropdown(
      {super.key,
      required this.onChanged,
      this.value}); // Update constructor to accept value

  @override
  State<TimePeriodDropdown> createState() => _TimePeriodDropdownState();
}

class _TimePeriodDropdownState extends State<TimePeriodDropdown> {
  final List<String> timePeriods = [
    '14 days',
    '30 days',
    '90 days',
    '365 days'
  ];
  String? selectedTimePeriod; // Remove initial value assignment here
  final GlobalKey _buttonKey = GlobalKey();
  OverlayEntry? _overlayEntry;

  @override
  void initState() {
    super.initState();
    // Set initial selectedTimePeriod from widget.value or default to '14 days'
    selectedTimePeriod = widget.value ?? '14 days';
    if (!timePeriods.contains(selectedTimePeriod)) {
      selectedTimePeriod = '14 days'; // Fallback if value is not in timePeriods
    }
  }

  @override
  Widget build(BuildContext context) {
    final textTheme = Theme.of(context).textTheme;

    return InkWell(
      key: _buttonKey,
      onTap: _showOverlay,
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(30),
          border: Border.all(color: Colors.white),
          color: AppColors.coral,
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              selectedTimePeriod!,
              style: textTheme.ralewayMedium.copyWith(
                fontSize: 13,
                color: Colors.white,
              ),
            ),
            const SizedBox(width: 4),
            const Icon(Icons.keyboard_arrow_down,
                color: Colors.white, size: 20),
          ],
        ),
      ),
    );
  }

  void _showOverlay() {
    final renderBox =
        _buttonKey.currentContext?.findRenderObject() as RenderBox;
    final position = renderBox.localToGlobal(Offset.zero);
    final size = renderBox.size;

    _overlayEntry = OverlayEntry(
      builder: (context) => Stack(
        children: [
          // Modal barrier to close overlay on outside tap
          Positioned.fill(
            child: GestureDetector(
              onTap: _closeOverlay,
              child: Container(color: Colors.transparent),
            ),
          ),
          Positioned(
            left: position.dx,
            top: position.dy + size.height + 4,
            child: Material(
              // elevation: 4,
              borderRadius: BorderRadius.circular(16),
              child: Container(
                width: size.width,
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(16),
                  border: Border.all(
                    color: AppColors.midBlue,
                    width: 1.5,
                  ),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: timePeriods.map((period) {
                    return InkWell(
                      onTap: () => _selectPeriod(period),
                      child: Container(
                        width: double.infinity,
                        padding: const EdgeInsets.symmetric(
                            horizontal: 12, vertical: 10),
                        child: Text(
                          period,
                          style: Theme.of(context)
                              .textTheme
                              .ralewayMedium
                              .copyWith(
                                fontSize: 13,
                                color: period == selectedTimePeriod
                                    ? AppColors.coral
                                    : AppColors.navy,
                              ),
                        ),
                      ),
                    );
                  }).toList(),
                ),
              ),
            ),
          ),
        ],
      ),
    );

    Overlay.of(context).insert(_overlayEntry!);
  }

  void _selectPeriod(String period) {
    setState(() => selectedTimePeriod = period);
    widget.onChanged(period);
    _closeOverlay();
  }

  void _closeOverlay() {
    _overlayEntry?.remove();
    _overlayEntry = null;
  }

  @override
  void dispose() {
    _closeOverlay();
    super.dispose();
  }
}
