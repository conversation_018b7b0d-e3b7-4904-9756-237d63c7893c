import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:readmore/readmore.dart';
import 'package:gotcha_mfg_app/config/theme/app_assets.dart';
import 'package:gotcha_mfg_app/core/extensions/app_text_theme_extension.dart';

import '../../../../config/theme/app_colors.dart';

class ReminderWidget extends StatelessWidget {
  final VoidCallback onClose;
  final VoidCallback onNavigate;
  final TextTheme theme;
  final String text;

  const ReminderWidget({
    super.key,
    required this.onClose,
    required this.onNavigate,
    required this.theme,
    required this.text,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.symmetric(vertical: 16, horizontal: 8),
      decoration: BoxDecoration(
        color: AppColors.lightRed,
        borderRadius: BorderRadius.circular(16),
      ),
      child: Row(
        children: [
          const Gap(6),
          Image.asset(
            AppAssets.notification,
            scale: 4,
          ),
          // Bell Icon
          // const Icon(
          //   Icons.notifications_outlined,
          //   color: AppColors.navy,
          //   size: 24,
          // ),
          const SizedBox(width: 12),

          // Reminder Text
          Expanded(
            child: ReadMoreText(
              text,
              style: theme.labels,
              trimLines: 3,
              colorClickableText: AppColors.navy,
              trimMode: TrimMode.Line,
              trimCollapsedText: ' Read more',
              trimExpandedText: ' Show less',
              moreStyle: theme.labels.copyWith(
                color: AppColors.navy,
                fontWeight: FontWeight.bold,
              ),
              lessStyle: theme.labels.copyWith(
                color: AppColors.navy,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),

          // Close Button
          Padding(
            padding: const EdgeInsets.all(4.0),
            child: GestureDetector(
              onTap: onClose,
              child: const Icon(
                Icons.close,
                size: 20,
              ),
            ),
          ),

          // Navigate Button
          Padding(
            padding: const EdgeInsets.all(4.0),
            child: GestureDetector(
              onTap: onNavigate,
              child: const Icon(
                Icons.chevron_right,
                color: AppColors.navy,
                size: 24,
              ),
            ),
          ),
        ],
      ),
    );
  }
}
