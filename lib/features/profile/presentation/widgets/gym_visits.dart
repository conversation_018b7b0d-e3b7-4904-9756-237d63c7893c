import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:gotcha_mfg_app/config/theme/app_colors.dart';
import 'package:gotcha_mfg_app/core/extensions/app_text_theme_extension.dart';
import 'package:gotcha_mfg_app/core/extensions/url_extensions.dart';
import 'package:gotcha_mfg_app/features/profile/presentation/widgets/build_rich_text.dart';
import 'package:gotcha_mfg_app/features/profile/presentation/widgets/date_convert.dart';
import 'package:gotcha_mfg_app/features/profile/presentation/widgets/image_container.dart';
import 'package:gotcha_mfg_app/shared/widgets/no_data.dart';

import '../../data/models/gym_history_response.dart';
import 'build_checkin_card.dart';
import 'build_exercise_card.dart';

class GymVisits extends StatefulWidget {
  final GymHistoryResponse gym;
  final String? selectedChip;

  const GymVisits({
    super.key,
    required this.gym,
    this.selectedChip,
  });

  @override
  State<GymVisits> createState() => _GymVisitsState();
}

class _GymVisitsState extends State<GymVisits> {
  String _selectedChip = 'All';
  final _chipLabels = ['All', 'Workouts', 'Exercises', 'Check-Ins'];

  final ScrollController _scrollController = ScrollController();

  final Map<String, GlobalKey> _chipKeys = {
    'All': GlobalKey(),
    'Workouts': GlobalKey(),
    'Exercises': GlobalKey(),
    'Check-Ins': GlobalKey(),
  };

  @override
  void initState() {
    super.initState();
    _selectedChip = widget.selectedChip ?? 'All';
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _scrollToSelectedChip();
    });
  }

  @override
  void didUpdateWidget(covariant GymVisits oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (widget.selectedChip != oldWidget.selectedChip &&
        widget.selectedChip != null) {
      _selectedChip = widget.selectedChip!;
      WidgetsBinding.instance.addPostFrameCallback((_) {
        _scrollToSelectedChip();
      });
    }
  }

  void _scrollToSelectedChip() {
    // if (_chipKeys[_selectedChip] != null &&
    //     _chipKeys[_selectedChip]!.currentContext != null) {
    //   final RenderBox selectedChipRenderBox = _chipKeys[_selectedChip]!
    //       .currentContext!
    //       .findRenderObject() as RenderBox;
    //   final selectedChipPosition =
    //       selectedChipRenderBox.localToGlobal(Offset.zero);
    //   final double scrollOffset = selectedChipPosition.dx;

    //   if (_scrollController.hasClients) {
    //     _scrollController.animateTo(
    //       scrollOffset -
    //           24,
    //       duration: const Duration(milliseconds: 300),
    //       curve: Curves.easeInOut,
    //     );
    //   }
    // }
  }

  @override
  Widget build(BuildContext context) {
    final textTheme = Theme.of(context).textTheme;
    return Container(
      color: AppColors.lightBlue,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 8),
            child: Text(
              'Recent gym visits',
              style: textTheme.sectionHeading,
            ),
          ),
          _buildFilterChips(textTheme),
          _buildVisitList(textTheme),
          const Gap(120),
        ],
      ),
    );
  }

  // Widget _buildVisitList(TextTheme textTheme) {
  //   final gymHistory = widget.gym.data?.gymHistory;
  //   if (gymHistory == null ||
  //       (gymHistory.exercises == null &&
  //           gymHistory.checkIns == null &&
  //           gymHistory.workouts == null)) {
  //     return Padding(
  //       padding: const EdgeInsets.symmetric(vertical: 50),
  //       child: NoData(textTheme: textTheme),
  //     );
  //   }

  //   List<Widget> visitWidgets = [];
  //   if (_selectedChip == 'All' || _selectedChip == 'Exercises') {
  //     visitWidgets.addAll((gymHistory.exercises ?? []).map((exercise) {
  //       // exercise.updatedAt;
  //       return buildExerciseCard(exercise, textTheme, context);
  //     }));
  //   }
  //   if (_selectedChip == 'All' || _selectedChip == 'Check-Ins') {
  //     visitWidgets.addAll((gymHistory.checkIns ?? []).map((checkIn) {
  //      // checkIn.createdAt;
  //       return buildCheckInCard(checkIn, textTheme, context);
  //     }));
  //   }
  //   if (_selectedChip == 'All' || _selectedChip == 'Workouts') {
  //     visitWidgets.addAll((gymHistory.workouts ?? []).map((workout) {
  //      // workout.updatedAt;
  //       return _buildWorkoutCard(workout, textTheme);
  //     }));
  //   }

  //   if (visitWidgets.isEmpty) {
  //     return Padding(
  //       padding: const EdgeInsets.fromLTRB(0, 56, 0, 120),
  //       child: NoData(textTheme: textTheme),
  //     );
  //   }

  //   return ListView(
  //     padding: const EdgeInsets.symmetric(horizontal: 18, vertical: 8),
  //     shrinkWrap: true,
  //     physics: const NeverScrollableScrollPhysics(),
  //     children: visitWidgets,
  //   );
  // }

  Widget _buildVisitList(TextTheme textTheme) {
    final gymHistory = widget.gym.data?.gymHistory;
    if (gymHistory == null ||
        (gymHistory.exercises == null &&
            gymHistory.checkIns == null &&
            gymHistory.workouts == null)) {
      return Padding(
        padding: const EdgeInsets.symmetric(vertical: 50),
        child: NoData(textTheme: textTheme),
      );
    }

    // Create a list to hold all items with their timestamps
    List<MapEntry<DateTime, Widget>> allItems = [];

    // Add exercises with their timestamps
    if (_selectedChip == 'All' || _selectedChip == 'Exercises') {
      allItems.addAll((gymHistory.exercises ?? []).map((exercise) {
        return MapEntry(exercise.updatedAt ?? DateTime.now(),
            buildExerciseCard(exercise, textTheme, context));
      }));
    }

    // Add check-ins with their timestamps
    if (_selectedChip == 'All' || _selectedChip == 'Check-Ins') {
      allItems.addAll((gymHistory.checkIns ?? []).map((checkIn) {
        return MapEntry(checkIn.createdAt ?? DateTime.now(),
            buildCheckInCard(checkIn, textTheme, context));
      }));
    }

    // Add workouts with their timestamps
    if (_selectedChip == 'All' || _selectedChip == 'Workouts') {
      allItems.addAll((gymHistory.workouts ?? []).map((workout) {
        return MapEntry(workout.updatedAt ?? DateTime.now(),
            _buildWorkoutCard(workout, textTheme));
      }));
    }

    // Sort all items by timestamp in descending order (newest first)
    allItems.sort((a, b) => b.key.compareTo(a.key));

    // Extract just the widgets after sorting
    List<Widget> visitWidgets = allItems.map((item) => item.value).toList();

    if (visitWidgets.isEmpty) {
      return Padding(
        padding: const EdgeInsets.fromLTRB(0, 72, 0, 120),
        child: NoData(textTheme: textTheme),
      );
    }

    return ListView(
      padding: const EdgeInsets.symmetric(horizontal: 18, vertical: 8),
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      children: visitWidgets,
    );
  }

  Widget _buildFilterChips(TextTheme textTheme) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 0),
      child: SingleChildScrollView(
        controller: _scrollController,
        scrollDirection: Axis.horizontal,
        child: Padding(
          padding: const EdgeInsets.symmetric(horizontal: 24.0),
          child: Row(
            children: _chipLabels
                .map((label) => Padding(
                      padding: const EdgeInsets.only(right: 8),
                      child: _buildFilterChip(label, textTheme,
                          key: _chipKeys[label]),
                    ))
                .toList(),
          ),
        ),
      ),
    );
  }

  Widget _buildFilterChip(String label, TextTheme textTheme, {Key? key}) {
    return FilterChip(
      key: key,
      selected: _selectedChip == label,
      backgroundColor: Colors.white,
      selectedColor: Colors.redAccent,
      label: Text(
        label,
        style: textTheme.labels.copyWith(
          color: _selectedChip == label ? Colors.white : AppColors.navy,
        ),
      ),
      shape: const StadiumBorder(
          side: BorderSide(width: 0, color: Colors.transparent)),
      showCheckmark: false,
      onSelected: (bool selected) {
        setState(() {
          _selectedChip = label;
        });
        _scrollToSelectedChip();
      },
    );
  }

  Widget _buildWorkoutCard(Workout workout, TextTheme textTheme) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8.0),
      child: Card(
        elevation: 0,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(20)),
        color: Colors.white,
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    'Workout',
                    style: textTheme.ralewaySemiBold
                        .copyWith(fontSize: 17, fontWeight: FontWeight.w600),
                  ),
                  Text(
                    convertDate(workout.createdAt.toString()),
                    style: textTheme.ralewayMedium
                        .copyWith(fontSize: 13, color: AppColors.navy),
                  ),
                ],
              ),
              const Gap(12),
              Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  buildImageContainer(workout.thumbnailUrl?.exerciseUrl ?? ''),
                  const Gap(12),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        if (workout.title != null) ...[
                          Text(
                            workout.title!,
                            style: textTheme.ralewayMedium.copyWith(
                                fontSize: 13, fontWeight: FontWeight.w600),
                            maxLines: 6,
                            overflow: TextOverflow.ellipsis,
                          ),
                          const Gap(8),
                        ],

                        if (workout.reflectionQuestions != null &&
                            workout.reflectionQuestions!.isNotEmpty)
                          for (int i = 0;
                              i < workout.reflectionQuestions!.length;
                              i++) ...[
                            if (workout.reflectionQuestions![i].text != null)
                              buildRichText(
                                'Question : ',
                                workout.reflectionQuestions![i].text!,
                                textTheme,
                                context,
                                isItalic: true,
                              ),
                            // if (workout.reflectionQuestions![i].answer != null)
                            buildRichText(
                              'Reflection : ',
                              workout.reflectionQuestions![i].answer ?? '..',
                              textTheme,
                              context,
                            ),
                          ],
                        // if (workout.reflectionQuestions != null)
                        //   _buildRichText('Question: ',
                        //       workout.reflectionQuestions?[0].text??'', textTheme,
                        //       isItalic: true),
                        // if (workout.reflectionQuestions?[0].answer !=
                        //     null) // re-using reflectionQuestionText as reflection is missing in Workout model
                        //   _buildRichText('Reflection: ',workout.reflectionQuestions?[0].answer?? "great", textTheme),
                      ],
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }
}
