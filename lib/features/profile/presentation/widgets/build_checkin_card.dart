import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:gotcha_mfg_app/config/theme/app_colors.dart';
import 'package:gotcha_mfg_app/core/extensions/app_text_theme_extension.dart';
import 'package:gotcha_mfg_app/core/extensions/url_extensions.dart';
import 'package:gotcha_mfg_app/core/utils/app_print.dart';
import 'package:gotcha_mfg_app/features/profile/data/models/gym_history_response.dart';
import 'package:gotcha_mfg_app/features/profile/presentation/widgets/build_rich_text.dart';
import 'package:gotcha_mfg_app/features/profile/presentation/widgets/checkin_icon.dart';
import 'package:gotcha_mfg_app/features/profile/presentation/widgets/date_convert.dart';

Widget buildCheckInCard(
    CheckIn checkIn, TextTheme textTheme, BuildContext context) {
  info('-------check--${checkIn.iconUrl}');
  return Padding(
    padding: const EdgeInsets.symmetric(vertical: 8.0),
    child: Card(
      elevation: 0,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(20)),
      color: Colors.white,
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'Check-in',
                  style: textTheme.ralewaySemiBold.copyWith(fontSize: 17),
                ),
                Text(
                  convertDate(checkIn.createdAt.toString()),
                  style: textTheme.ralewayMedium
                      .copyWith(fontSize: 13, color: AppColors.navy),
                ),
              ],
            ),
            const Gap(12),
            Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                buildCheckInIcon(
                  checkIn.iconUrl?.iconUrl ?? '',
                  checkIn.feeling ?? 'N/A',
                  textTheme,
                ),
                const Gap(12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      if (checkIn.tags?.emotions != null)
                        buildRichText(
                            'Emotions: ',
                            checkIn.tags!.emotions!.join(", "),
                            textTheme,
                            context,
                            isCheck: true),
                      if (checkIn.tags?.factors != null)
                        buildRichText(
                            'Factors: ',
                            checkIn.tags!.factors!.join(", "),
                            textTheme,
                            context,
                            isCheck: true),
                      if (checkIn.tags?.factors != null)
                        buildRichText(
                            'Grateful for: ',
                            checkIn.tags!.gratitude!.join(", "),
                            textTheme,
                            context,
                            isCheck: true),
                    ],
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    ),
  );
}
