// import 'package:flutter/material.dart';
// import 'package:gotcha_mfg_app/config/theme/app_colors.dart';
// import 'package:gotcha_mfg_app/core/extensions/app_text_theme_extension.dart';
// import 'package:gotcha_mfg_app/features/auth/data/models/identity_group_model.dart';

// class ActionChipList extends StatefulWidget {
//   final List<Datum> data;
//   final List<String> selectedIds;
//   final Function(List<String>)? onSelectionChanged;
//   final bool isSelect;

//   const ActionChipList({
//     super.key,
//     required this.data,
//     required this.selectedIds,
//     this.onSelectionChanged,
//     this.isSelect = true, //Provide a default value to prevent the value null
//   });

//   @override
//   _ActionChipListState createState() => _ActionChipListState();
// }

// class _ActionChipListState extends State<ActionChipList> {
//   late List<String>
//       selectedIds; // Change to late, so we init with the widget prop

//   @override
//   void initState() {
//     super.initState();
//     selectedIds = List<String>.from(widget.selectedIds); // Create a *copy*
//   }

//   @override
//   void didUpdateWidget(covariant ActionChipList oldWidget) {
//     super.didUpdateWidget(oldWidget);
//     if (!listEquals(widget.selectedIds, oldWidget.selectedIds)) {
//       selectedIds = List<String>.from(
//           widget.selectedIds); // Update the selected id's with a new copy
//     }
//   }

//   void _toggleSelection(Datum item) {
//     setState(() {
//       if (selectedIds.contains(item.id)) {
//         selectedIds.remove(item.id);
//       } else {
//         selectedIds.add(item.id!);
//       }
//       widget.onSelectionChanged?.call(selectedIds);
//     });
//   }

//   Widget _buildActionChip(Datum item) {
//     final isSelected = selectedIds.contains(item.id);

//     return ActionChip(
//       shape: StadiumBorder(
//         side: BorderSide(
//           color: isSelected ? AppColors.coral : Colors.white,
//           width: 1.5,
//         ),
//       ),
//       onPressed: widget.isSelect ? () => _toggleSelection(item) : null,
//       label: Text(
//         item.name ?? 'N/A',
//         style: Theme.of(context).textTheme.ralewaySemiBold.copyWith(
//               fontSize: 14,
//               color: AppColors.navy,
//             ),
//       ),
//       backgroundColor: isSelected ? AppColors.lightRed : Colors.white,
//     );
//   }

//   @override
//   Widget build(BuildContext context) {
//     return Wrap(
//       spacing: 8.0,
//       runSpacing: 8.0,
//       children: widget.data.map(_buildActionChip).toList(),
//     );
//   }
// }

// bool listEquals<T>(List<T>? a, List<T>? b) {
//   if (a == null && b == null) return true;
//   if (a == null || b == null) return false;
//   if (a.length != b.length) return false;
//   for (int i = 0; i < a.length; i++) {
//     if (a[i] != b[i]) return false;
//   }
//   return true;
// }
import 'package:flutter/material.dart';
import 'package:gotcha_mfg_app/config/theme/app_colors.dart';
import 'package:gotcha_mfg_app/core/extensions/app_text_theme_extension.dart';
import 'package:gotcha_mfg_app/core/utils/app_print.dart';
import 'package:gotcha_mfg_app/features/auth/data/models/identity_group_model.dart';

bool listEquals<T>(List<T>? a, List<T>? b) {
  if (a == null && b == null) return true;
  if (a == null || b == null) return false;
  if (a.length != b.length) return false;
  for (int i = 0; i < a.length; i++) {
    if (a[i] != b[i]) return false;
  }
  return true;
}

class ActionChipList extends StatefulWidget {
  final List<Datum> data;
  final List<String> selectedIds;
  final Function(List<String> selectedIds, String? otherValue)?
      onSelectionChanged;
  final bool isSelect;
  final bool showOtherOption;
  final String? initialOtherValue;
  final String addOtherButtonLabel;
  final String otherTextFieldHint;

  const ActionChipList({
    super.key,
    required this.data,
    required this.selectedIds,
    this.onSelectionChanged,
    this.isSelect = true,
    this.showOtherOption = false,
    this.initialOtherValue,
    this.addOtherButtonLabel = '+ Other',
    this.otherTextFieldHint = 'Type here...',
  });

  @override
  State<ActionChipList> createState() => _ActionChipListState();
}

class _ActionChipListState extends State<ActionChipList> {
  late List<String> selectedIds;
  late TextEditingController _otherTextController;
  bool _isOtherTextFieldVisible = false;
  String? _otherSelectedValue;

  @override
  void initState() {
    super.initState();
    selectedIds = List<String>.from(widget.selectedIds);
    _otherTextController = TextEditingController();
    _otherSelectedValue = widget.initialOtherValue;
  }

  @override
  void didUpdateWidget(covariant ActionChipList oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (!listEquals(widget.selectedIds, oldWidget.selectedIds)) {
      selectedIds = List<String>.from(widget.selectedIds);
    }
    if (widget.initialOtherValue != oldWidget.initialOtherValue &&
        !_isOtherTextFieldVisible) {
      setState(() {
        _otherSelectedValue = widget.initialOtherValue;
      });
    }
  }

  @override
  void dispose() {
    _otherTextController.dispose();
    super.dispose();
  }

  void _triggerSelectionChange() {
    widget.onSelectionChanged?.call(selectedIds, _otherSelectedValue);
  }

  void _toggleSelection(Datum item) {
    if (!widget.isSelect || _isOtherTextFieldVisible) return;

    setState(() {
      if (selectedIds.contains(item.id)) {
        selectedIds.remove(item.id);
      } else {
        if (item.id != null) {
          selectedIds.add(item.id!);
        }
      }
      _triggerSelectionChange();
    });
  }

  void _handleOtherChipTap() {
    if (!widget.isSelect) return;

    // If the other chip already has a value, clear it
    if (_otherSelectedValue != null) {
      setState(() {
        _otherSelectedValue = null;
        _triggerSelectionChange();
      });
    } else {
      // Otherwise, show the text field
      setState(() {
        _isOtherTextFieldVisible = true;
      });
    }
  }

  void _confirmOtherText() {
    final inputText = _otherTextController.text.trim();
    setState(() {
      if (inputText.isNotEmpty) {
        _otherSelectedValue = inputText;
      }
      _isOtherTextFieldVisible = false;
      _otherTextController.clear();
      _triggerSelectionChange();
    });
  }

  Widget _buildActionChip(Datum item) {
    final bool isSelected = selectedIds.contains(item.id);
    return ActionChip(
      shape: StadiumBorder(
        side: BorderSide(
          color: isSelected ? AppColors.coral : Colors.white,
          width: 1.5,
        ),
      ),
      onPressed: widget.isSelect
          ? () {
              if (!_isOtherTextFieldVisible) {
                _toggleSelection(item);
              }
            }
          : null,
      label: Text(
        item.name ?? 'N/A',
        style: Theme.of(context).textTheme.ralewaySemiBold.copyWith(
              fontSize: 14,
              color: AppColors.navy,
            ),
      ),
      backgroundColor: isSelected ? AppColors.lightRed : Colors.white,
    );
  }

  Widget _buildOtherButton() {
    return ActionChip(
      shape: const StadiumBorder(
        side: BorderSide(
          color: AppColors.midBlue,
          width: 1.5,
        ),
      ),
      onPressed: widget.isSelect
          ? () {
              setState(() {
                _isOtherTextFieldVisible = true;
              });
            }
          : null,
      label: Text(
        widget.addOtherButtonLabel,
        style: Theme.of(context).textTheme.ralewayLight.copyWith(
              fontSize: 14,
              color: AppColors.navy.withOpacity(0.7),
            ),
      ),
      backgroundColor: Colors.white,
    );
  }

  Widget _buildOtherValueChip() {
    return ActionChip(
      shape: const StadiumBorder(
        side: BorderSide(
          color: AppColors.coral,
          width: 1.5,
        ),
      ),
      onPressed: widget.isSelect ? _handleOtherChipTap : null,
      label: Text(
        _otherSelectedValue!,
        style: Theme.of(context).textTheme.ralewaySemiBold.copyWith(
              fontSize: 14,
              color: AppColors.navy,
            ),
      ),
      backgroundColor: AppColors.lightRed,
    );
  }

  Widget _buildOtherTextField() {
    var border = OutlineInputBorder(
      borderRadius: BorderRadius.circular(30),
      borderSide: const BorderSide(
        color: AppColors.midBlue,
        width: 1.5,
      ),
    );

    return Container(
      height: 40,
      constraints: const BoxConstraints(maxWidth: 200),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(30),
        color: Colors.white,
      ),
      child: TextField(
        controller: _otherTextController,
        autofocus: true,
        style: Theme.of(context).textTheme.ralewaySemiBold.copyWith(
              fontSize: 14,
              color: AppColors.navy,
            ),
        decoration: InputDecoration(
          hintText: widget.otherTextFieldHint,
          hintStyle: Theme.of(context).textTheme.ralewayLight.copyWith(
                fontSize: 14,
                color: AppColors.midBlue,
              ),
          isDense: true,
          contentPadding:
              const EdgeInsets.only(left: 16, right: 0, top: 11, bottom: 11),
          border: border,
          enabledBorder: border,
          focusedBorder: border.copyWith(
            borderSide: const BorderSide(color: AppColors.coral, width: 1.5),
          ),
          suffixIcon: IconButton(
            padding: EdgeInsets.zero,
            constraints: const BoxConstraints(),
            icon: const Icon(Icons.check, color: AppColors.coral, size: 24),
            onPressed: _confirmOtherText,
          ),
        ),
        onSubmitted: (_) => _confirmOtherText(),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    // Build regular chips for data items
    final regularChips = widget.data.map(_buildActionChip).toList();

    // Determine what "Other" widget to show (if any)
    Widget? otherWidget;
    if (widget.showOtherOption) {
      info(
          'show---$_isOtherTextFieldVisible--1---${widget.showOtherOption}---2----$_otherSelectedValue--');
      if (_isOtherTextFieldVisible) {
        otherWidget = _buildOtherTextField();
      } else if (_otherSelectedValue != null) {
        otherWidget = _buildOtherValueChip();
      } else {
        otherWidget = _buildOtherButton();
      }
    }

    // Combine all chips
    List<Widget> allChips = [...regularChips];
    if (otherWidget != null) {
      allChips.add(otherWidget);
    }

    return Wrap(
      spacing: 8.0,
      runSpacing: 8.0,
      children: allChips,
    );
  }
}
