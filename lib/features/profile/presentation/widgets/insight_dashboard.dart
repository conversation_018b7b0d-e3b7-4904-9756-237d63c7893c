import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:gotcha_mfg_app/core/extensions/app_text_theme_extension.dart';
import 'package:gotcha_mfg_app/core/extensions/string_extensions.dart';
import 'package:gotcha_mfg_app/core/extensions/url_extensions.dart';
import 'package:gotcha_mfg_app/features/profile/presentation/widgets/filter_dropdown.dart';
import 'package:gotcha_mfg_app/shared/widgets/network_image_indicator.dart';

import '../../../../config/theme/app_colors.dart';
import '../../../../shared/widgets/no_data.dart';
import '../../data/models/gym_history_response.dart';

class InsightsDashboard extends StatefulWidget {
  final GymHistoryResponse gymHistory;
  final List<String> timePeriods;
  final String selectedTimePeriod;
  final ValueChanged<String?> onTimePeriodChanged;

  const InsightsDashboard({
    super.key,
    required this.gymHistory,
    required this.timePeriods,
    required this.selectedTimePeriod,
    required this.onTimePeriodChanged,
  });

  @override
  State<InsightsDashboard> createState() => _InsightsDashboardState();
}

class _InsightsDashboardState extends State<InsightsDashboard> {
  @override
  Widget build(BuildContext context) {
    final textTheme = Theme.of(context).textTheme;

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 24),
      decoration: const BoxDecoration(color: AppColors.lightRed),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildHeader(textTheme),
          const Gap(20),
          _buildStatsRow(textTheme),
          const Gap(24),
        ],
      ),
    );
  }

  Widget _buildHeader(TextTheme textTheme) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Text('Insights', style: textTheme.sectionHeading),
        TimePeriodDropdown(
          onChanged: (value) {
            widget.onTimePeriodChanged(value);
          },
          value: widget.selectedTimePeriod,
        ),
      ],
    );
  }

  Widget _buildOverallInsight(TextTheme textTheme) {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white.withOpacity(0.5),
        borderRadius: BorderRadius.circular(15),
      ),
      child: RichText(
        text: TextSpan(
          children: [
            TextSpan(
                text: 'Overall',
                style: textTheme.bodyRegular
                    .copyWith(fontWeight: FontWeight.bold)),
            TextSpan(
              text:
                  ' - it looks like your mental space has been relatively stable and positive recently.',
              style: textTheme.bodyRegular,
            ),
          ],
        ),
        maxLines: 4,
      ),
    );
  }

  Widget _buildStatsRow(TextTheme textTheme) {
    return SizedBox(
      height: 192,
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Expanded(child: _buildCheckInsAndExercises(textTheme)),
          const Gap(16),
          Expanded(child: _buildCommonTags(textTheme)),
        ],
      ),
    );
  }

  Widget _buildCheckInsAndExercises(TextTheme textTheme) {
    return Column(
      children: [
        Expanded(
          child: _buildStatContainer(
            value:
                '${widget.gymHistory.data?.gymHistory?.previousCheckIns ?? ''}',
            label:
                (widget.gymHistory.data?.gymHistory?.previousCheckIns ?? 0) <= 1
                    ? 'Check-in'
                    : 'Check-ins',
            textTheme: textTheme,
            backgroundColor: Colors.white.withOpacity(0.5),
          ),
        ),
        const Gap(16),
        Expanded(
            child: _buildStatContainer(
          value:
              '${widget.gymHistory.data?.gymHistory?.previousExercises ?? ''}',
          label:
              (widget.gymHistory.data?.gymHistory?.previousExercises ?? 0) <= 1
                  ? 'Exercise'
                  : 'Exercises',
          textTheme: textTheme,
          backgroundColor: Colors.white.withOpacity(0.5),
        )),
      ],
    );
  }

  Widget _buildCommonTags(TextTheme textTheme) {
    return Container(
      padding: const EdgeInsets.symmetric(vertical: 20),
      decoration: BoxDecoration(
        color: Colors.white.withOpacity(0.5),
        borderRadius: BorderRadius.circular(16),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16),
            child: Text('Most common check-in tags:', style: textTheme.labels),
          ),
          const Gap(12),
          Padding(
            padding: const EdgeInsets.only(left: 16, right: 4),
            child: _buildTagsList(textTheme),
          ),
        ],
      ),
    );
  }

  // Widget _buildTagsList(TextTheme textTheme) {
  //   final tags = widget.gymHistory.data?.gymHistory?.mostCommonCheckInTags;
  //   if (tags?.isEmpty ?? true) {
  //     return NoData(textTheme: textTheme);
  //   }
  //   return Expanded(
  //     child: Scrollbar(
  //       thumbVisibility: true,
  //       child: SingleChildScrollView(
  //         child: Column(
  //           crossAxisAlignment: CrossAxisAlignment.start,
  //           children: tags!
  //               .map(
  //                 (tag) => Padding(
  //                   padding: const EdgeInsets.only(bottom: 2),
  //                   child: _buildTagRow(
  //                       tag.feeling ?? 'N/A', '${tag.count}x', textTheme),
  //                 ),
  //               )
  //               .toList(),
  //         ),
  //       ),
  //     ),
  //   );
  // }
  Widget _buildTagsList(TextTheme textTheme) {
    final tags = widget.gymHistory.data?.gymHistory?.mostCommonCheckInTags;
    if (tags?.isEmpty ?? true) {
      return NoDataNoIcon(textTheme: textTheme);
    }

    // Create a ScrollController
    final ScrollController scrollController = ScrollController();

    return Scrollbar(
      // Assign the controller to the Scrollbar
      controller: scrollController,
      thumbVisibility: true,
      child: SingleChildScrollView(
        // Assign the same controller to the SingleChildScrollView
        controller: scrollController,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: tags!
              .map(
                (tag) => Padding(
                  padding: const EdgeInsets.only(bottom: 4),
                  child: _buildTagRow(tag.iconUrl?.iconUrl ?? '',
                      tag.feeling ?? 'N/A', '${tag.count}x', textTheme),
                ),
              )
              .toList(),
        ),
      ),
    );
  }

  Widget _buildStatContainer({
    required String value,
    required String label,
    required TextTheme textTheme,
    required Color backgroundColor,
  }) {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: backgroundColor,
        borderRadius: BorderRadius.circular(15),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
        children: [
          Text(value, style: textTheme.gothamBold.copyWith(fontSize: 30)),
          const Gap(8),
          Expanded(child: Text(label, style: textTheme.labels)),
        ],
      ),
    );
  }

  Widget _buildTagRow(
      String icon, String tag, String count, TextTheme textTheme) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      crossAxisAlignment: CrossAxisAlignment.center,
      mainAxisAlignment: MainAxisAlignment.start,
      children: [
        Text(count, style: textTheme.labels),
        const Gap(4),
        Flexible(
          child: Container(
            padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 6),
            decoration: BoxDecoration(
              color: AppColors.lightBlue,
              borderRadius: BorderRadius.circular(16),
            ),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                Flexible(
                  child: Text(
                    tag.capitalizeFirstLetter(),
                    style: textTheme.labels.copyWith(fontSize: 10),
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
                const Gap(4),
                SizedBox(
                    width: 14,
                    child: NetworkImageWithIndicatorSmall(
                      imageUrl: icon,
                      fit: BoxFit.cover,
                    ))
                // Image.asset(
                //   tag.icon,
                //   width: 14,
                //   color: AppColors.navy,
                // ),
              ],
            ),
          ),
        ),
      ],
    );
  }
}
