import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:gotcha_mfg_app/config/theme/app_colors.dart';
import 'package:gotcha_mfg_app/core/extensions/app_text_theme_extension.dart';
import 'package:gotcha_mfg_app/shared/widgets/primary_button.dart';

class SignUpWidget extends StatelessWidget {
  const SignUpWidget({
    super.key,
    required this.title,
    required this.benefits,
    required this.buttonText,
    required this.onSignUpPressed,
    required this.onLoginPressed,
    this.showLoginText = false,
    required this.icon,
  });

  final String title;
  final bool icon;
  final List<String> benefits;
  final String buttonText;
  final VoidCallback? onSignUpPressed;
  final VoidCallback? onLoginPressed;
  final bool showLoginText;

  @override
  Widget build(BuildContext context) {
    final bodyStyle = Theme.of(context).textTheme.agLabels;
    final loginStyle = Theme.of(context).textTheme.ralewayBold.copyWith(
          fontSize: 14,
          color: AppColors.navy,
        );
    final loginSubtitleStyle =
        Theme.of(context).textTheme.ralewayRegular.copyWith(
              fontSize: 14,
              color: AppColors.navy,
            );

    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 24),
      child: Container(
        decoration: BoxDecoration(
          color: icon ? AppColors.midBlue : AppColors.lightRed,
          borderRadius: BorderRadius.circular(16),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Gap(16),
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 20),
              child:
                  Text(title, style: Theme.of(context).textTheme.bodyEmphasis),
            ),
            ...benefits.map(
              (benefit) => _BenefitItem(
                icon: icon,
                benefit: benefit,
                style: bodyStyle,
              ),
            ),
            Padding(
              padding: const EdgeInsets.symmetric(
                horizontal: 24,
                vertical: 20,
              ),
              child: SizedBox(
                width: MediaQuery.sizeOf(context).width,
                child: PrimaryButton(
                  text: buttonText,
                  isEnabled: true,
                  onPressed: onSignUpPressed ?? () {},
                ),
              ),
            ),
            if (showLoginText) ...[
              Center(
                child: GestureDetector(
                  onTap: onLoginPressed ?? () {},
                  child: RichText(
                    text: TextSpan(
                      text: 'Already have an account? ',
                      style: loginSubtitleStyle,
                      children: [
                        TextSpan(
                          text: 'Login',
                          style: loginStyle,
                        ),
                      ],
                    ),
                    maxLines: 2,
                  ),
                ),
              ),
              const Gap(24),
            ],
          ],
        ),
      ),
    );
  }
}

class _BenefitItem extends StatelessWidget {
  const _BenefitItem({
    required this.benefit,
    required this.style,
    required this.icon,
  });

  final String benefit;
  final bool icon;
  final TextStyle style;

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.only(
        left: 16,
        right: 16,
        top: 14,
      ),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          icon
              ? const Padding(
                  padding: EdgeInsets.only(top: 2),
                  child: Icon(
                    size: 16,
                    Icons.check_circle_outline_rounded,
                    color: AppColors.navy,
                  ),
                )
              : const SizedBox(),
          const Gap(5),
          Expanded(
            child: Text(
              benefit,
              style: style,
            ),
          ),
        ],
      ),
    );
  }
}
