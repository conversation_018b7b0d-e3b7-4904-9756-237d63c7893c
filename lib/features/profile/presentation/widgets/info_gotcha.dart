import 'package:flutter/material.dart';
import 'package:gotcha_mfg_app/core/extensions/app_text_theme_extension.dart';

import '../../../../config/theme/app_colors.dart';

class AboutGotcha4LifeCard extends StatelessWidget {
  const AboutGotcha4LifeCard({super.key});

  @override
  Widget build(BuildContext context) {
    var textTheme = Theme.of(context).textTheme;

    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 24),
      decoration: BoxDecoration(
        color:
            AppColors.midBlue.withOpacity(0.6), // Light blue background color
        borderRadius: BorderRadius.circular(16),
      ),
      padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text('About Gotcha4Life', style: textTheme.bodyEmphasis
              //  TextStyle(
              //   fontSize: 28,
              //   fontWeight: FontWeight.bold,
              //   color: Color(0xFF0D1A4A), // Navy blue color
              // ),
              ),
          const SizedBox(height: 16),
          Text(
            'The Mental Fitness Gym is a product developed by the Gotcha4Life Foundation, a health promotion charity on a mission to inspire and enable people to take action to build their mental fitness. Our vision is a suicide-free world, where no one worries alone.',
            style: textTheme.agLabels,
          ),
        ],
      ),
    );
  }
}
