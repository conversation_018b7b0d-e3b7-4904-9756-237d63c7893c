String getDateBeforeDays(int days) {
  // Get the current date
  DateTime currentDate = DateTime.now();

  // Subtract the specified number of days
  DateTime dateBefore = currentDate.subtract(Duration(days: days));

  // Format the date to YYYY-MM-DD
  String formattedDate = '${dateBefore.year.toString().padLeft(4, '0')}-'
      '${dateBefore.month.toString().padLeft(2, '0')}-'
      '${dateBefore.day.toString().padLeft(2, '0')}';

  return formattedDate;
}
