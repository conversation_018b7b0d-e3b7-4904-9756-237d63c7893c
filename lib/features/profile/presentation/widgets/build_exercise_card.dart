import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:gotcha_mfg_app/core/extensions/app_text_theme_extension.dart';
import 'package:gotcha_mfg_app/core/extensions/url_extensions.dart';
import 'package:gotcha_mfg_app/features/profile/data/models/gym_history_response.dart';
import 'package:gotcha_mfg_app/features/profile/presentation/widgets/build_rich_text.dart';
import 'package:gotcha_mfg_app/features/profile/presentation/widgets/date_convert.dart';
import 'package:gotcha_mfg_app/features/profile/presentation/widgets/image_container.dart';

Widget buildExerciseCard(
    Exercise exercise, TextTheme textTheme, BuildContext context) {
  return Padding(
    padding: const EdgeInsets.symmetric(vertical: 8.0),
    child: Card(
      elevation: 0,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(20)),
      color: Colors.white,
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'Exercise',
                  style: textTheme.bodyEmphasis,
                ),
                Text(
                  convertDate(exercise.createdAt.toString()),
                  style: textTheme.labels,
                ),
              ],
            ),
            const Gap(12),
            Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                buildImageContainer(exercise.thumbnailUrl?.exerciseUrl ??
                    'https://images.unsplash.com/photo-1506863530036-1efeddceb993?q=80&w=2844&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D'),
                const Gap(12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      if (exercise.exerciseTitle != null) ...[
                        Text(
                          exercise.exerciseTitle!,
                          style: textTheme.labelsBold,
                          maxLines: 6,
                          overflow: TextOverflow.ellipsis,
                        ),
                        const Gap(8),
                      ],
                      if (exercise.reflectionQuestions != null &&
                          exercise.reflectionQuestions!.isNotEmpty)
                        for (int i = 0;
                            i < exercise.reflectionQuestions!.length;
                            i++) ...[
                          if (exercise.reflectionQuestions![i].text != null)
                            buildRichText(
                              'Question : ',
                              exercise.reflectionQuestions![i].text!,
                              textTheme,
                              context,
                              isItalic: true,
                            ),
                          // if (exercise.reflectionQuestions![i].answer != null)
                          buildRichText(
                              'Reflection : ',
                              exercise.reflectionQuestions![i].answer ?? '..',
                              textTheme,
                              context),
                        ],
                      // if (exercise.reflectionQuestions != null)
                      //   _buildRichText('Question: ',
                      //       exercise.reflectionQuestions?[0].text??'', textTheme,
                      //       isItalic: true),
                      // if (exercise.reflectionQuestions?[0].answer != null)
                      //   _buildRichText(
                      //       'Reflection: ', exercise.reflectionQuestions?[0].answer??'', textTheme),
                    ],
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    ),
  );
}
