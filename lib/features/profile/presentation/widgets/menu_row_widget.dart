import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:gotcha_mfg_app/core/extensions/app_text_theme_extension.dart';

@immutable
class MenuItem {
  const MenuItem({
    this.isAdjusted = false,
    this.isIconAdjust = false,
    required this.icon,
    required this.text,
    required this.trailingIcon,
    this.onTap,
    this.size,
    this.color,
  });

  final String? icon;
  final double? size;
  final String text;
  final Color? color;
  final IconData trailingIcon;
  final VoidCallback? onTap;
  final bool isAdjusted;
  final bool isIconAdjust;
}

class MenuRowWidget extends StatelessWidget {
  const MenuRowWidget({
    super.key,
    required this.items,
    this.padding = const EdgeInsets.symmetric(horizontal: 24),
    this.itemSpacing = 4,
    this.iconSpacing = 8,
    this.borderRadius = 16,
    this.backgroundColor = Colors.white,
    this.borderColor = Colors.white,
    this.iconColor,
    this.textStyle,
  });

  final List<MenuItem> items;
  final EdgeInsetsGeometry padding;
  final double itemSpacing;
  final double iconSpacing;
  final double borderRadius;
  final Color backgroundColor;
  final Color borderColor;
  final Color? iconColor;
  final TextStyle? textStyle;

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: padding,
      child: Container(
        decoration: BoxDecoration(
          color: backgroundColor,
          borderRadius: BorderRadius.circular(borderRadius),
          border: Border.all(color: borderColor),
        ),
        padding: const EdgeInsets.fromLTRB(14, 6, 8, 8),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Gap(itemSpacing),
            ..._buildItems(context),
          ],
        ),
      ),
    );
  }

  List<Widget> _buildItems(BuildContext context) {
    final defaultTextStyle = textStyle ?? Theme.of(context).textTheme.labels;

    return items
        .map((item) => _CustomRowItem(
              isIconAdjusted: item.isIconAdjust,
              isAdjusted: item.isAdjusted,
              item: item,
              spacing: itemSpacing,
              iconSpacing: iconSpacing,
              iconColor: iconColor,
              textStyle: item.color != null
                  ? defaultTextStyle.copyWith(color: item.color)
                  : defaultTextStyle,
            ))
        .toList();
  }
}

class _CustomRowItem extends StatefulWidget {
  const _CustomRowItem(
      {required this.item,
      required this.spacing,
      required this.iconSpacing,
      required this.textStyle,
      this.iconColor,
      required this.isAdjusted,
      required this.isIconAdjusted});

  final MenuItem item;
  final double spacing;
  final double iconSpacing;
  final Color? iconColor;
  final TextStyle textStyle;
  final bool isAdjusted;
  final bool isIconAdjusted;

  @override
  State<_CustomRowItem> createState() => _CustomRowItemState();
}

class _CustomRowItemState extends State<_CustomRowItem> {
  bool _isTapped = false;

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: EdgeInsets.symmetric(vertical: widget.spacing),
      child: Material(
        color: Colors.transparent,
        borderRadius: BorderRadius.circular(8),
        clipBehavior: Clip.hardEdge,
        child: InkWell(
          // Use InkWell for ripple effect
          onTap: () {
            if (widget.item.onTap != null) {
              setState(() {
                _isTapped = true; // Start animation on tap down
              });

              Future.delayed(const Duration(milliseconds: 200), () {
                // Adjust delay as needed
                setState(() {
                  _isTapped = false; // Reset animation
                });
                widget.item.onTap!(); // Execute the actual onTap callback.
              });
            }
          },
          //  borderRadius: BorderRadius.circular(8), // Add border radius if needed
          child: AnimatedOpacity(
            // Wrap with AnimatedOpacity
            opacity:
                _isTapped ? 0.5 : 1.0, // Control opacity based on _isTapped
            duration: const Duration(milliseconds: 150), // Animation duration
            curve: Curves.easeOut, // Animation curve
            child: Padding(
              padding: const EdgeInsets.symmetric(vertical: 4),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Expanded(
                    child: Row(
                      crossAxisAlignment: CrossAxisAlignment.center,
                      children: [
                        widget.item.isAdjusted ? const Gap(2) : const Gap(0),

                        widget.item.isIconAdjust
                            ? Column(children: [
                                widget.item.isAdjusted
                                    ? const Gap(3)
                                    : const Gap(0),
                                widget.item.size == null
                                    ? Image.asset(
                                        widget.item.icon ?? '',
                                        width: 20,
                                        color: widget.iconColor,
                                      )
                                    : Image.asset(
                                        widget.item.icon ?? '',
                                        fit: BoxFit.fill,
                                        width: widget.item.size,
                                        color: widget.iconColor,
                                      ),
                              ])
                            : Column(children: [
                                widget.item.isAdjusted
                                    ? const Gap(3)
                                    : const Gap(0),
                                widget.item.size == null
                                    ? Image.asset(
                                        scale: 4,
                                        widget.item.icon ?? '',
                                        // width: 20,
                                        color: widget.iconColor,
                                      )
                                    : Image.asset(
                                        widget.item.icon ?? '',
                                        scale: 5,
                                        fit: BoxFit.fill,
                                        width: widget.item.size,
                                        color: widget.iconColor,
                                      ),
                              ]),
                        widget.item.size == null
                            ? SizedBox(width: (widget.iconSpacing))
                            : SizedBox(width: widget.iconSpacing + 5),
                        // Expanded(
                        Expanded(
                          child: Text(
                            widget.item.text,
                            style: widget.textStyle,
                          ),
                        ),
                      ],
                    ),
                  ),
                  Icon(
                    widget.item.trailingIcon,
                    color: widget.iconColor,
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }
}
