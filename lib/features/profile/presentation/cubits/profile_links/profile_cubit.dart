import 'package:bloc/bloc.dart';
import 'package:gotcha_mfg_app/core/usecase/usecase.dart';
import 'package:gotcha_mfg_app/features/notification/data/models/notification_get_request.dart';
import 'package:gotcha_mfg_app/features/notification/domain/usecases/get_notification_count_usecase.dart';
import 'package:gotcha_mfg_app/features/notification/domain/usecases/get_notification_usecase.dart';
import 'package:gotcha_mfg_app/features/profile/presentation/cubits/profile/profile_cubit.dart';

import '../../../../auth/domain/usecases/get_identity_usecase.dart';
import '../../../data/models/profile_edit_request.dart';
import '../../../domain/usecases/get_profile_details.dart';
import '../../../domain/usecases/get_profile_links_use_case.dart';
import '../../../domain/usecases/update_profile_usecase.dart';

class ProfileCubit extends Cubit<ProfileState> {
  ProfileCubit(
      this._getProfileLinksUseCase,
      this._getProfileDetailUseCase,
      this._getIdentityGroupsUseCase,
      this._updateProfileUseCase,
      this._getNotificationCountUseCase,
      this._getNotificationUseCase)
      : super(ProfileInitial());

  final GetProfileLinksUseCase _getProfileLinksUseCase;
  final GetProfileDetailUseCase _getProfileDetailUseCase;
  final GetIdentityGroupsUseCase _getIdentityGroupsUseCase;
  final UpdateProfileUseCase _updateProfileUseCase;
  final GetNotificationCountUseCase _getNotificationCountUseCase;
  final GetNotificationUseCase _getNotificationUseCase;

  Future<void> getProfileLinks() async {
    emit(ProfileLoading());
    final result = await _getProfileLinksUseCase.call(NoParams());
    final count = await _getNotificationCountUseCase.call(NoParams());
    final notification = await _getNotificationUseCase
        .call(NotificationGetRequestParams(scheduledTime: ''));
    if (result.isSuccess) {
      emit(ProfileLinksLoaded(
          profileResponse: result.data!,
          get: notification.data!,
          count: count.data!));
    } else {
      emit(ProfileError(message: result.error!));
    }
  }

  /// Get Profile Detail
  Future<void> getProfileDetail() async {
    emit(ProfileLoading());
    final result = await _getProfileDetailUseCase.call(NoParams());
    final identity = await _getIdentityGroupsUseCase.call(NoParams());

    if (result.isSuccess & identity.isSuccess) {
      emit(ProfileDetailsLoaded(
          profileResponse: result.data!, identityResponse: identity.data!));
    } else {
      final error = result.isSuccess ? identity.error! : result.error!;
      emit(ProfileError(message: error));
    }
  }

  Future<void> updateProfile(UpdateProfileParams params) async {
    emit(ProfileLoading());
    final result = await _updateProfileUseCase.call(params);
    if (result.isSuccess) {
      emit(ProfileUpdated(result.data!));
    } else {
      emit(ProfileError(message: result.error!));
    }
  }
}
