part of 'profile_links_cubit.dart';

sealed class ProfileLinksState extends Equatable {
  const ProfileLinksState();

  @override
  List<Object> get props => [];
}

final class ProfileLinksInitial extends ProfileLinksState {}

final class ProfileLinksLoading extends ProfileLinksState {}

final class ProfileLinksLoaded extends ProfileLinksState {
  final ProfileResponse profileResponse;
  final NotificationGetResponse get;
  final NotificationCountResponse count;
  const ProfileLinksLoaded(
      {required this.profileResponse, required this.get, required this.count});
}

final class ProfileLinksError extends ProfileLinksState {
  final String message;
  const ProfileLinksError({required this.message});
}

final class ProfileLinkDetailsLoaded extends ProfileLinksState {
  final ProfileDetailResponse profileResponse;
  final IdentityGroupResponse identityResponse;
  const ProfileLinkDetailsLoaded({
    required this.profileResponse,
    required this.identityResponse,
  });
}

final class ProfileLinksUpdated extends ProfileLinksState {
  final ProfileUpdateResponse updateResponse;
  const ProfileLinksUpdated(this.updateResponse);
}
