import 'package:bloc/bloc.dart';
import 'package:equatable/equatable.dart';
import 'package:gotcha_mfg_app/core/usecase/usecase.dart';
import 'package:gotcha_mfg_app/features/notification/data/models/notification_count.dart';
import 'package:gotcha_mfg_app/features/notification/data/models/notification_get_request.dart';
import 'package:gotcha_mfg_app/features/notification/data/models/notification_get_response.dart';
import 'package:gotcha_mfg_app/features/notification/domain/usecases/get_notification_count_usecase.dart';
import 'package:gotcha_mfg_app/features/notification/domain/usecases/get_notification_usecase.dart';

import '../../../../auth/data/models/identity_group_model.dart';
import '../../../../auth/domain/usecases/get_identity_usecase.dart';
import '../../../data/models/profile_detail_reponse.dart';
import '../../../data/models/profile_edit_request.dart';
import '../../../data/models/profile_response.dart';
import '../../../data/models/profile_update_response.dart';
import '../../../domain/usecases/get_profile_details.dart';
import '../../../domain/usecases/get_profile_links_use_case.dart';
import '../../../domain/usecases/update_profile_usecase.dart';

part 'profile_links_state.dart';

class ProfileLinksCubit extends Cubit<ProfileLinksState> {
  ProfileLinksCubit(
      this._getProfileLinksUseCase,
      this._getProfileDetailUseCase,
      this._getIdentityGroupsUseCase,
      this._updateProfileUseCase,
      this._getNotificationCountUseCase,
      this._getNotificationUseCase)
      : super(ProfileLinksInitial());

  final GetProfileLinksUseCase _getProfileLinksUseCase;
  final GetProfileDetailUseCase _getProfileDetailUseCase;
  final GetIdentityGroupsUseCase _getIdentityGroupsUseCase;
  final UpdateProfileUseCase _updateProfileUseCase;
  final GetNotificationCountUseCase _getNotificationCountUseCase;
  final GetNotificationUseCase _getNotificationUseCase;

  Future<void> getProfileLinks() async {
    emit(ProfileLinksLoading());
    final result = await _getProfileLinksUseCase.call(NoParams());
    final count = await _getNotificationCountUseCase.call(NoParams());
    final notification = await _getNotificationUseCase
        .call(NotificationGetRequestParams(scheduledTime: ''));
    if (result.isSuccess) {
      emit(ProfileLinksLoaded(
          profileResponse: result.data!,
          get: notification.data!,
          count: count.data!));
    } else {
      emit(ProfileLinksError(message: result.error!));
    }
  }

  /// Get Profile Detail
  Future<void> getProfileDetail() async {
    emit(ProfileLinksLoading());
    final result = await _getProfileDetailUseCase.call(NoParams());
    final identity = await _getIdentityGroupsUseCase.call(NoParams());

    if (result.isSuccess & identity.isSuccess) {
      emit(ProfileLinkDetailsLoaded(
          profileResponse: result.data!, identityResponse: identity.data!));
    } else {
      final error = result.isSuccess ? identity.error! : result.error!;
      emit(ProfileLinksError(message: error));
    }
  }

  Future<void> updateProfile(UpdateProfileParams params) async {
    emit(ProfileLinksLoading());
    final result = await _updateProfileUseCase.call(params);
    if (result.isSuccess) {
      emit(ProfileLinksUpdated(result.data!));
    } else {
      emit(ProfileLinksError(message: result.error!));
    }
  }
}
