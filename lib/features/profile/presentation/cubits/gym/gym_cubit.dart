import 'package:bloc/bloc.dart';
import 'package:equatable/equatable.dart';
import 'package:gotcha_mfg_app/features/profile/data/models/gym_req.dart';

import '../../../data/models/gym_history_response.dart';
import '../../../domain/usecases/get_gym_history.dart';

part 'gym_state.dart';

class GymCubit extends Cubit<GymState> {
  final GetGymHistoryUseCase _getGymHistoryUseCase;

  GymCubit(this._getGymHistoryUseCase) : super(GymInitial());

  Future<void> getGymHistory(GymReq req) async {
    emit(GymHistoryLoading());
    final result = await _getGymHistoryUseCase.call(req);
    if (result.isSuccess) {
      emit(GymHistoryLoaded(gymResponse: result.data!));
    } else {
      emit(GymHistoryError(message: result.error!));
    }
  }
}
