part of 'gym_cubit.dart';

sealed class GymState extends Equatable {
  const GymState();

  @override
  List<Object> get props => [];
}

final class GymInitial extends GymState {}

final class GymHistoryLoading extends GymState {}

final class GymHistoryLoaded extends GymState {
  final GymHistoryResponse gymResponse;

  const GymHistoryLoaded({required this.gymResponse});
}

final class GymHistoryError extends GymState {
  final String message;

  const GymHistoryError({required this.message});
}
