part of 'profile_cubit.dart';

sealed class ProfileState extends Equatable {
  const ProfileState();

  @override
  List<Object> get props => [];
}

final class ProfileInitial extends ProfileState {}

final class ProfileLoading extends ProfileState {}

final class ProfileLogoutLoaded extends ProfileState {}

final class ProfileLinksLoaded extends ProfileState {
  final ProfileResponse profileResponse;
  final NotificationGetResponse get;
  final NotificationCountResponse count;

  ProfileLinksLoaded(
      {required this.profileResponse, required this.get, required this.count});
}

final class ProfileError extends ProfileState {
  final String message;
  const ProfileError({required this.message});
}

final class ProfileDetailsLoaded extends ProfileState {
  final ProfileDetailResponse profileResponse;
  final IdentityGroupResponse identityResponse;
  const ProfileDetailsLoaded(
      {required this.profileResponse, required this.identityResponse});
}

final class ProfileUpdated extends ProfileState {
  final ProfileUpdateResponse updateResponse;
  const ProfileUpdated(this.updateResponse);
}
