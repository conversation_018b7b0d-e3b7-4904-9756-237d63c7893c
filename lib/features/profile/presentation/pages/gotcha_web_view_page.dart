import 'package:auto_route/auto_route.dart';
import 'package:easy_debounce/easy_debounce.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:gotcha_mfg_app/config/theme/app_colors.dart';
import 'package:gotcha_mfg_app/shared/widgets/app_header.dart';
import 'package:gotcha_mfg_app/shared/widgets/curved_separator.dart';
import 'package:gotcha_mfg_app/shared/widgets/loading_widget.dart';
import 'package:webview_flutter/webview_flutter.dart';

import '../../../../core/mixpanel_service.dart';
import '../../../../locator.dart';

@RoutePage()
class GotchaWebViewPage extends StatefulWidget {
  const GotchaWebViewPage({
    super.key,
    required this.url,
    required this.title,
  });

  final String? url;
  final String? title;

  @override
  State<GotchaWebViewPage> createState() => _GotchaWebViewPageState();
}

class _GotchaWebViewPageState extends State<GotchaWebViewPage> {
  late final WebViewController controller;
  bool _isLoading = true;
  bool _isHeaderRemoved = false;

  @override
  void initState() {
    super.initState();
    sl<MixpanelService>().trackScreenView(
      'Webview - ${widget.title} Page',
      properties: {'Code': 'screen_view.webview-${widget.title}Page'},
    );

    controller = WebViewController()
      ..setJavaScriptMode(JavaScriptMode.unrestricted)
      ..setNavigationDelegate(
        NavigationDelegate(
          onPageStarted: (_) {
            if (mounted) {
              setState(() {
                _isLoading = true;
                _isHeaderRemoved = false;
              });
            }
          },
          onPageFinished: (_) async {
            await controller.runJavaScript('''
              var header = document.getElementById('header');
              var header1 = document.getElementById('site-header');
              if (header) {
                header.style.display = 'none';
              }
              if (header1) {
                header1.style.display = 'none';
              }
              ''');
            if (mounted) {
              EasyDebounce.debounce(
                  'my-debouncer', const Duration(milliseconds: 500), () {
                setState(() {
                  _isLoading = false;
                  _isHeaderRemoved = true;
                });
              });
            }
          },
        ),
      )
      ..loadRequest(Uri.parse(widget.url ?? 'https://www.gotcha4life.org'));
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        toolbarHeight: 0,
        elevation: 0,
        systemOverlayStyle: const SystemUiOverlayStyle(
          statusBarColor: Colors.white,
          systemNavigationBarIconBrightness: Brightness.dark,
          statusBarBrightness: Brightness.light,
          statusBarIconBrightness: Brightness.dark,
          systemNavigationBarColor: Colors.white,
        ),
      ),
      body: Padding(
        padding: const EdgeInsets.all(8.0),
        child: Column(
          children: [
            AppHeader(
              onBackTap: () => Navigator.pop(context),
              title: widget.title,
            ),
            const CurvedSeparator(
              outerColor: AppColors.navy,
              innerColor: Colors.white,
            ),
            Expanded(
              child: Stack(
                children: [
                  if (_isHeaderRemoved)
                    WebViewWidget(controller: controller)
                  else
                    const Center(
                      child: Loader(),
                    ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
