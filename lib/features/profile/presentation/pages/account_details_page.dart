// import 'package:auto_route/auto_route.dart';
// import 'package:flutter/material.dart';
// import 'package:flutter_bloc/flutter_bloc.dart';
// import 'package:gap/gap.dart';
// import 'package:gotcha_mfg_app/config/router/app_router.gr.dart';
// import 'package:gotcha_mfg_app/config/theme/app_colors.dart';
// import 'package:gotcha_mfg_app/core/extensions/app_text_theme_extension.dart';
// import 'package:gotcha_mfg_app/shared/widgets/app_header.dart';
// import 'package:gotcha_mfg_app/shared/widgets/primary_button.dart';

// import '../../../../core/utils/snackbar_service.dart';
// import '../../../../shared/widgets/loading_widget.dart';
// import '../../../../shared/widgets/retry_widget.dart';
// import '../cubits/profile/profile_cubit.dart';
// import '../widgets/custom_text_field.dart';

// @RoutePage()
// class AccountDetailsPage extends StatefulWidget {
//   const AccountDetailsPage({super.key});

//   @override
//   State<AccountDetailsPage> createState() => _AccountDetailsPageState();
// }

// class _AccountDetailsPageState extends State<AccountDetailsPage> {
//   String? _selectedGender;

//   @override
//   void initState() {
//     // TODO: implement initState
//     super.initState();
//     context.read<ProfileCubit>().getProfileDetail();
//   }

//   @override
//   Widget build(BuildContext context) {
//     final textTheme = Theme.of(context).textTheme;
//     final size = MediaQuery.of(context).size;
//     return BlocConsumer<ProfileCubit, ProfileState>(listener: (context, state) {
//       if (state is ProfileError) {
//         SnackBarService.info(
//           context: context,
//           message: state.message,
//         );
//       }
//     }, builder: (context, state) {
//       var size = MediaQuery.of(context).size;
//       if (state is ProfileLoading) {
//         return SizedBox(
//             height: size.height,
//             child: const LoadingWidget(color: Colors.white));
//       }
//       if (state is ProfileError) {
//         return RetryWidget(
//           onRetry: () {},
//           // onRetry: () => context.read<ProfileCubit>().getProfileLinks(),
//           color: Colors.white,
//         );
//       }

//       if (state is ProfileDetailsLoaded) {
//         final profile = state.profileResponse.data;
//         return Scaffold(
//             body: Padding(
//                 padding: const EdgeInsets.only(
//                    top: isIos ? 4 : 8,
//                   right: 8,
//                   left: 8,
//                   // bottom: (mode == 1 || mode == 2) ? 0 : 32,
//                 ),
//                 child: ListView(children: [
//                   // GestureDetector(
//                   //   onDoubleTap: () {
//                   //     context.pushRoute(const LoginRoute());
//                   //     // Navigator.push(
//                   //     //   context,
//                   //     //   MaterialPageRoute(
//                   //     //     builder: (context) => const LoginPage(),
//                   //     //   ),
//                   //     // );
//                   //   },
//                   //   child: AccountHeader(
//                   //     textTheme: textTheme,
//                   //     onTap: () {},
//                   //   ),
//                   // ),
//                   const AppHeader(
//                     title: 'Account',
//                   ),
//                   Container(
//                       color: AppColors.navy,
//                       padding: const EdgeInsets.only(),
//                       child: Container(
//                         decoration: const BoxDecoration(
//                           borderRadius: BorderRadius.only(
//                             topLeft: Radius.circular(30),
//                             topRight: Radius.circular(30),
//                           ),
//                           color: AppColors.grey,
//                         ),
//                         child: Padding(
//                           padding: const EdgeInsets.all(20.0),
//                           child: Column(
//                             crossAxisAlignment: CrossAxisAlignment.start,
//                             children: [
//                               const Gap(12),
//                               const CustomTextField(
//                                 text: 'First name *',
//                                 hinttext: 'Enter your first name',
//                               ),
//                               const Gap(16),

//                               // CustomTextField(text: 'Gender',hinttext: 'Select your gender',),
//                               Text(
//                                 'Gender' ?? '',
//                                 style: textTheme.ralewayMedium.copyWith(
//                                   fontSize: 14,
//                                   color: AppColors.navy,
//                                 ),
//                               ),
//                               const Gap(16),

//                               // DropdownButton for Male, Female, Other
//                               Container(
//                                 decoration: BoxDecoration(
//                                   borderRadius: BorderRadius.circular(20.0),
//                                   border: Border.all(
//                                     color: Colors.grey.shade300,
//                                     width: 1.0,
//                                   ),
//                                   color: Colors.grey.shade100,
//                                 ),
//                                 child: DropdownButton<String>(
//                                   value: _selectedGender,
//                                   hint: Padding(
//                                     padding: const EdgeInsets.symmetric(
//                                         horizontal: 10.0),
//                                     child: Text(
//                                       'Select Gender',
//                                       style: textTheme.ralewayLight.copyWith(
//                                         fontSize: 14,
//                                         color: AppColors.navy,
//                                       ),
//                                     ),
//                                   ),
//                                   icon: const Padding(
//                                     padding:
//                                         EdgeInsets.symmetric(horizontal: 10.0),
//                                     child: Icon(
//                                       Icons.keyboard_arrow_down,
//                                       color: Colors.black,
//                                       size: 30,
//                                     ),
//                                   ),
//                                   onChanged: (String? newValue) {
//                                     setState(() {
//                                       _selectedGender = newValue;
//                                     });
//                                   },
//                                   items: <String>['Male', 'Female', 'Other']
//                                       .map<DropdownMenuItem<String>>(
//                                           (String value) {
//                                     return DropdownMenuItem<String>(
//                                       value: value,
//                                       child: Padding(
//                                         padding: const EdgeInsets.symmetric(
//                                             horizontal: 16.0),
//                                         child: Text(
//                                           value,
//                                           style:
//                                               textTheme.ralewayLight.copyWith(
//                                             fontSize: 14,
//                                             color: AppColors.navy,
//                                           ),
//                                         ),
//                                       ),
//                                     );
//                                   }).toList(),
//                                   isExpanded: true,
//                                   style: textTheme.ralewayLight.copyWith(
//                                     fontSize: 14,
//                                     color: AppColors.navy,
//                                   ),
//                                   underline: Container(),
//                                 ),
//                               ),
//                               const Gap(16),

//                               const CustomTextField(
//                                 text: 'Age',
//                                 hinttext: 'Enter your age',
//                               ),
//                               const Gap(16),

//                               Text(
//                                 'Identity groups',
//                                 style: textTheme.ralewayMedium.copyWith(
//                                   fontSize: 14,
//                                   color: AppColors.navy,
//                                 ),
//                               ),
//                               const Gap(20),

//                               // Wrap(
//                               //   spacing: 8,
//                               //   children: [
//                               //     ActionChipList(
//                               //       answers: sampleAnswers,
//                               //     ),
//                               //   ],
//                               // ),
//                               const Gap(26),

//                               SizedBox(
//                                 width: size.width,
//                                 child: PrimaryButton(
//                                   text: 'Save',
//                                   isEnabled: true,
//                                   onPressed: () {
//                                     context.pushRoute(const ProfileRoute());

//                                     // Navigator.push(
//                                     //   context,
//                                     //   MaterialPageRoute(
//                                     //     builder: (context) => const ProfilePage(),
//                                     //   ),
//                                     // );
//                                   },
//                                 ),
//                               ),
//                               const Gap(100),
//                             ],
//                           ),
//                         ),
//                       ))
//                 ])));
//       }
//       return const SizedBox();
//     });
//   }
// }
