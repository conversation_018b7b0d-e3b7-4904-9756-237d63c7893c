import 'package:auto_route/auto_route.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:gap/gap.dart';
import 'package:gotcha_mfg_app/config/router/app_router.gr.dart';
import 'package:gotcha_mfg_app/config/theme/app_assets.dart';
import 'package:gotcha_mfg_app/config/theme/app_colors.dart';
import 'package:gotcha_mfg_app/core/extensions/app_text_theme_extension.dart';
import 'package:gotcha_mfg_app/core/storage/token_manager.dart';
import 'package:gotcha_mfg_app/core/utils/app_print.dart';
import 'package:gotcha_mfg_app/core/utils/device_info.dart';
import 'package:gotcha_mfg_app/core/utils/snackbar_service.dart';
import 'package:gotcha_mfg_app/features/auth/presentation/blocs/login/login_state.dart';
import 'package:gotcha_mfg_app/features/notification/data/models/notification_get_request.dart';
import 'package:gotcha_mfg_app/features/notification/data/models/notification_get_response.dart';
import 'package:gotcha_mfg_app/features/notification/data/models/notification_read_request.dart';
import 'package:gotcha_mfg_app/features/notification/presentation/blocs/notification/notification_cubit.dart';
import 'package:gotcha_mfg_app/features/notification/presentation/blocs/notification/notification_state.dart';
import 'package:gotcha_mfg_app/features/profile/data/models/logout_req.dart';
import 'package:gotcha_mfg_app/features/profile/data/models/profile_response.dart';
import 'package:gotcha_mfg_app/features/profile/presentation/cubits/profile_links/profile_links_cubit.dart';
import 'package:gotcha_mfg_app/shared/widgets/app_header.dart';
import 'package:gotcha_mfg_app/shared/widgets/curved_separator.dart';
import 'package:gotcha_mfg_app/shared/widgets/popup.dart';
import 'package:gotcha_mfg_app/shared/widgets/retry_widget.dart';
import 'package:gotcha_mfg_app/shared/widgets/show_info.dart';
import 'package:url_launcher/url_launcher.dart';

import '../../../../core/mixpanel_service.dart';
import '../../../../core/utils/platform_utils.dart';
import '../../../../locator.dart';
import '../../../../shared/widgets/loading_widget.dart';
import '../../../auth/presentation/blocs/login/login_cubit.dart';
import '../cubits/profile/profile_cubit.dart' as prof;
import '../widgets/info_gotcha.dart';
import '../widgets/menu_row_widget.dart';
import '../widgets/notification_reminder.dart';
import '../widgets/sign_up_widget.dart';

@RoutePage()
class ProfilePage extends StatefulWidget {
  const ProfilePage({super.key});

  @override
  State<ProfilePage> createState() => _ProfilePageState();
}

class _ProfilePageState extends State<ProfilePage> {
  bool isLoad = false;
  NotificationGetResponse? notification;

  @override
  void initState() {
    super.initState();
    sl<MixpanelService>().trackScreenView('Profile Page',
        properties: {'Code': 'screen_view.profile_page'});

    context.read<ProfileLinksCubit>().getProfileLinks();
    context
        .read<NotificationCubit>()
        .getNotification(NotificationGetRequestParams(scheduledTime: ''));
  }

  Future<void> _launchUrl(String urlString) async {
    final Uri url = Uri.parse(urlString);
    if (!await launchUrl(
      url,
      mode: LaunchMode.externalApplication,
    )) {
      throw Exception('Could not launch $url');
    }
  }

  @override
  Widget build(BuildContext context) {
    var textTheme = Theme.of(context).textTheme;
    var size = MediaQuery.of(context).size;
    return MultiBlocListener(
      listeners: [
        BlocListener<NotificationCubit, NotificationState>(
            listener: (context, state) {
          if (state is NotificationError) {
            SnackBarService.error(
              context: context,
              message: state.message,
            );
          }
          // if (state is NotificationGetLoaded) {
          //   notification = state.notificationResponse;
          // }
          if (state is NotificationPutLoaded) {
            context.read<NotificationCubit>().getNotification(
                NotificationGetRequestParams(scheduledTime: ''));
            context.read<ProfileLinksCubit>().getProfileLinks();
            context.read<NotificationCubit>().getNotificationCount();
          }
        }),
        BlocListener<prof.ProfileCubit, prof.ProfileState>(
            listener: (context, state) {
          if (state is prof.ProfileUpdated) {
            context.read<NotificationCubit>().getNotification(
                NotificationGetRequestParams(scheduledTime: ''));
            context.read<ProfileLinksCubit>().getProfileLinks();
          }
          if (state is prof.ProfileError) {
            SnackBarService.error(
              context: context,
              message: state.message,
            );
          }
        }),
        BlocListener<AuthCubit, AuthState>(listener: (context, state) {
          if (state is AuthDeleteAccountError) {
            SnackBarService.error(
              context: context,
              message: state.message,
            );
          }
          if (state is AuthDeleteAccountSuccess) {
            sl<TokenManager>().clearAll();
            context.replaceRoute(const SplashRoute());
          }
        }),
      ],
      child: BlocConsumer<ProfileLinksCubit, ProfileLinksState>(
        listener: (context, state) {
          if (state is ProfileLinksError) {
            SnackBarService.error(
              context: context,
              message: state.message,
            );
          }
        },
        builder: (context, state) {
          info('>>>>>>>>>>state----->$state');
          if (state is AuthDeleteAccountLoading) {
            return const LoadingWidget(color: Colors.white);
          }
          var size = MediaQuery.of(context).size;
          if (state is ProfileLinksError) {
            return RetryWidget(
              onRetry: () =>
                  context.read<ProfileLinksCubit>().getProfileLinks(),
              color: Colors.white,
            );
          }

          if (state is ProfileLinksLoading) {
            return const LoadingWidget(color: Colors.white);
          }

          if (state is ProfileLinksLoaded) {
            var isLoggedIn =
                state.profileResponse.data?.isSignUpCompleted ?? false;
            var links =
                state.profileResponse.data?.profileLinks ?? ProfileLinks();
            var count = state.count;
            notification = state.get;

            return Scaffold(
              appBar: AppBar(
                toolbarHeight: 0,
                elevation: 0,
                systemOverlayStyle: const SystemUiOverlayStyle(
                  statusBarColor: Colors.white,
                  systemNavigationBarIconBrightness: Brightness.dark,
                  statusBarBrightness: Brightness.light,
                  statusBarIconBrightness: Brightness.dark,
                  systemNavigationBarColor: Colors.white,
                ),
              ),
              body: Padding(
                padding: EdgeInsets.only(
                  top: isIos ? 4 : 8,
                  left: 8,
                  right: 8,
                  bottom: isIos ? 64 : 48,
                ),
                child: RefreshIndicator(
                  color: AppColors.coral,
                  backgroundColor: Colors.white,
                  onRefresh: () async {
                    // Navigator.of(context).push(
                    //   MaterialPageRoute(
                    //     builder: (context) => const ImageTextPage(
                    //       img: '',
                    //       id: '',
                    //       notification: true,
                    //       isFirst: true,
                    //       isLast: true,
                    //       seriesId: '',
                    //       isOverride: false,
                    //       isRedo: false,
                    //     ),
                    //   ),
                    // );
                    context.read<ProfileLinksCubit>().getProfileLinks();
                  },
                  child: Container(
                    color: AppColors.grey,
                    child: ListView(
                      padding: EdgeInsets.zero,
                      children: [
                        const AppHeader(
                          title: 'Profile',
                        ),
                        const CurvedSeparator(
                          outerColor: AppColors.navy,
                          innerColor: AppColors.grey,
                        ),
                        //Notifications
                        notification?.data?.isNotEmpty ?? false
                            ? Container(
                                color: AppColors.grey,
                                child: Padding(
                                  padding: const EdgeInsets.symmetric(
                                      horizontal: 24),
                                  child: Text(
                                    'Notifications',
                                    style: textTheme.bodyEmphasis,
                                  ),
                                ),
                              )
                            : const SizedBox(),
                        notification?.data?.isNotEmpty ?? false
                            ? const Gap(
                                0,
                                color: AppColors.grey,
                              )
                            : const SizedBox(),

                        notification?.data?.isNotEmpty ?? false
                            ? Container(
                                width: double.infinity,
                                margin: const EdgeInsets.only(bottom: 0),
                                color: AppColors.grey,
                                child: ListView.builder(
                                  physics: const NeverScrollableScrollPhysics(),
                                  padding: const EdgeInsets.only(bottom: 12),
                                  shrinkWrap: true,
                                  itemCount:
                                      (notification?.data?.length ?? 0) > 3
                                          ? 3
                                          : notification?.data?.length ?? 0,
                                  itemBuilder: (context, index) {
                                    return Padding(
                                      padding: const EdgeInsets.symmetric(
                                          horizontal: 24, vertical: 8),
                                      child: ReminderWidget(
                                        onClose: () {
                                          context
                                              .read<NotificationCubit>()
                                              .putNotification(
                                                  NotificationPutRequestParams(
                                                      id: '${notification?.data?[index].id}',
                                                      isRead: true));
                                          // mixpanel
                                          sl<MixpanelService>()
                                              .trackButtonClick(
                                                  'Close Notification',
                                                  properties: {
                                                'Page': 'Profile Page',
                                                'Code':
                                                    'click.profile_page.close_notification'
                                              });
                                        },
                                        onNavigate: () {
                                          // mixpanel
                                          sl<MixpanelService>()
                                              .trackButtonClick(
                                                  'Notification Tapped',
                                                  properties: {
                                                'Page': 'Profile Page',
                                                'Code':
                                                    'click.profile_page.notification_tapped'
                                              });
                                          final notificationType = notification
                                              ?.data?[index].onTapUrl;
                                          switch (notificationType) {
                                            case '/home':
                                              context.pushRoute(
                                                  HomeRoute(index: 0));
                                              break;
                                            case '/feature':
                                              // Since there's no dedicated feature route, we'll show home for now
                                              context.pushRoute(
                                                  HomeRoute(index: 0));
                                              break;
                                            case '/profile':
                                              context.pushRoute(
                                                  HomeRoute(index: 2));
                                              break;
                                            case '/village':
                                              context.router.replaceAll(
                                                [
                                                  HomeRoute(index: 0),
                                                  const VillageHomeRoute(),
                                                ],
                                                updateExistingRoutes: false,
                                              );
                                              break;
                                            default:
                                              // Default to home if type is unknown
                                              context.pushRoute(
                                                  HomeRoute(index: 0));
                                          }
                                          // Mark as read when navigating
                                          // context
                                          //     .read<NotificationCubit>()
                                          //     .putNotification(
                                          //         NotificationPutRequestParams(
                                          //             id: '${notification?.data?[index].id}',
                                          //             isRead: true));
                                        },
                                        theme: textTheme,
                                        text:
                                            '${notification?.data?[index].body}',
                                      ),
                                    );
                                  },
                                ),
                              )
                            : const SizedBox(),
                        // Account Details Section
                        Container(
                          color: AppColors.grey,
                          child: Padding(
                            padding: const EdgeInsets.symmetric(horizontal: 24),
                            child:
                                Text('Account', style: textTheme.bodyEmphasis),
                          ),
                        ),
                        const Gap(
                          8,
                          color: AppColors.grey,
                        ),
                        Container(
                          color: AppColors.grey,
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              if (!isLoggedIn)
                                SignUpWidget(
                                  icon: true,
                                  title: 'Mental Fitness Gym Membership',
                                  benefits: const [
                                    'Get even more personalised recommendations',
                                    'Save your progress across your devices',
                                    "Be the first to know about new workouts, resources, and exclusive member events",
                                    "It’s free and always will be",
                                  ],
                                  buttonText: 'Sign up',
                                  showLoginText: true,
                                  onSignUpPressed: () {
                                    context.pushRoute(const SignUpRoute());
                                    // mixpanel
                                    sl<MixpanelService>().trackButtonClick(
                                        'Sign Up',
                                        properties: {
                                          'Page': 'Profile Page',
                                          'Code': 'click.profile_page.sign_up'
                                        });
                                  },
                                  onLoginPressed: () {
                                    context.pushRoute(const LoginRoute());
                                    // mixpanel
                                    sl<MixpanelService>().trackButtonClick(
                                        'Log In',
                                        properties: {
                                          'Page': 'Profile Page',
                                          'Code': 'click.profile_page.log_in'
                                        });
                                  },
                                ),
                              if (!isLoggedIn)
                                const Gap(
                                  16,
                                  color: AppColors.grey,
                                ),
                            ],
                          ),
                        ),

                        // Profile and Logout Section
                        if (isLoggedIn)
                          Container(
                            color: AppColors.grey,
                            child: MenuRowWidget(
                              items: [
                                MenuItem(
                                    size: 14,
                                    isIconAdjust: true,
                                    icon: AppAssets.profileoptions,
                                    text: 'Profile options',
                                    trailingIcon: Icons.chevron_right,
                                    onTap: () => context
                                            .pushRoute(PersonaliseAccountRoute(
                                          isPersonalise: false,
                                          isInfo: false,
                                        ))),
                                MenuItem(
                                  size: 14,
                                  isIconAdjust: true,
                                  icon: AppAssets.logoutbutton,
                                  text: 'Log out',
                                  trailingIcon: Icons.chevron_right,
                                  color: AppColors.coral,
                                  onTap: () async {
                                    String? deviceId = await getDeviceId();

                                    CustomCupertinoAlertDialog.yesOrNoPopup(
                                      context,
                                      title: 'Logout',
                                      content:
                                          'Are you sure you want to logout?',
                                      yesButtonText: 'Logout',
                                      onYes: () {
                                        context
                                            .read<prof.ProfileCubit>()
                                            .logout(LogoutRequestParams(
                                                id: deviceId ?? ''));
                                        // sl<TokenManager>().clearTokens();
                                        // sl<TokenManager>().clearAll();
                                        // MFGPushNotification.resetToken();
                                        // Navigator.of(context).popUntil((route) => false);
                                        // context
                                        //     .replaceRoute(const SplashRoute());
                                        context.router.replaceAll(
                                          [const SplashRoute()],
                                          updateExistingRoutes: true,
                                        );
                                        // mixpanel
                                        sl<MixpanelService>().trackButtonClick(
                                            'Log Out',
                                            properties: {
                                              'Page': 'Profile Page',
                                              'Code':
                                                  'click.profile_page.log_out'
                                            });
                                        // SnackBarService.info(
                                        //   context: context,
                                        //   message: 'Coming soon',
                                        // );
                                      },
                                      onNo: () {
                                        Navigator.pop(context);
                                      },
                                    );
                                  },
                                ),
                              ],
                            ),
                          ),
                        const Gap(
                          16,
                          color: AppColors.grey,
                        ),
                        // Important Links Section
                        Container(
                          color: AppColors.grey,
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Padding(
                                padding:
                                    const EdgeInsets.symmetric(horizontal: 24),
                                child: Text('Important links',
                                    style: textTheme.bodyEmphasis),
                              ),
                              const Gap(8),
                              MenuRowWidget(
                                items: [
                                  MenuItem(
                                    size: 16,
                                    icon: AppAssets.pastcheckinew,
                                    text: 'Past check-ins',
                                    trailingIcon: Icons.chevron_right,
                                    onTap: () {
                                      context.pushRoute(GymHistoryRoute(
                                          selectedChip: "Check-Ins"));
                                      // mixpanel
                                      sl<MixpanelService>().trackButtonClick(
                                          'Past Check-ins',
                                          properties: {
                                            'Page': 'Profile Page',
                                            'Code':
                                                'click.profile_page.past_check_ins'
                                          });
                                    },
                                  ),
                                  MenuItem(
                                    size: 16,
                                    icon: AppAssets.pastexercisebutton,
                                    text: 'Past exercise reflections',
                                    trailingIcon: Icons.chevron_right,
                                    onTap: () {
                                      context.pushRoute(GymHistoryRoute(
                                          selectedChip: "Exercises"));
                                      // mixpanel
                                      sl<MixpanelService>().trackButtonClick(
                                          'Past Exercise Reflections',
                                          properties: {
                                            'Page': 'Profile Page',
                                            'Code':
                                                'click.profile_page.past_exercise_reflections'
                                          });
                                    },
                                  ),
                                  MenuItem(
                                    size: 14,
                                    onTap: () {
                                      context
                                          .pushRoute(const VillageHomeRoute());
                                      // mixpanel
                                      sl<MixpanelService>().trackButtonClick(
                                          'Your Village',
                                          properties: {
                                            'Page': 'Profile Page',
                                            'Code':
                                                'click.profile_page.your_village'
                                          });
                                    },
                                    icon: AppAssets.yourvillagenew,
                                    text: 'Your village',
                                    trailingIcon: Icons.chevron_right,
                                  ),
                                  MenuItem(
                                    isAdjusted: true,
                                    size: 15,
                                    onTap: () {
                                      context
                                          .pushRoute(const FavouritesRoute());
                                      // mixpanel
                                      sl<MixpanelService>().trackButtonClick(
                                          'Favourites',
                                          properties: {
                                            'Page': 'Profile Page',
                                            'Code':
                                                'click.profile_page.favourites'
                                          });
                                    },
                                    icon: AppAssets.favouritesnew,
                                    text: 'Favourites',
                                    trailingIcon: Icons.chevron_right,
                                  ),
                                  MenuItem(
                                    size: 16,
                                    icon: AppAssets.morehelpnew,
                                    text: 'Get more support',
                                    trailingIcon: Icons.chevron_right,
                                    onTap: () {
                                      context.pushRoute(
                                          const HelpSeekingPathwayRoute());
                                      // mixpanel
                                      sl<MixpanelService>().trackButtonClick(
                                          'Get More Support',
                                          properties: {
                                            'Page': 'Profile Page',
                                            'Code':
                                                'click.profile_page.get_more_support'
                                          });
                                    },
                                  ),
                                ],
                              ),
                            ],
                          ),
                        ),
                        const Gap(
                          16,
                          color: AppColors.grey,
                        ),
                        // Contact Section
                        Container(
                          color: AppColors.grey,
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Padding(
                                padding:
                                    const EdgeInsets.symmetric(horizontal: 24),
                                child: Text('Contact',
                                    style: textTheme.bodyEmphasis),
                              ),
                              const Gap(8),
                              MenuRowWidget(
                                items: [
                                  MenuItem(
                                    size: 16,
                                    icon: AppAssets.suggestimprovementnew,
                                    text: 'Suggest improvement',
                                    trailingIcon: Icons.chevron_right,
                                    onTap: () {
                                      context.pushRoute(const FeedBackRoute());
                                      // mixpanel
                                      sl<MixpanelService>().trackButtonClick(
                                          'Suggest Improvement',
                                          properties: {
                                            'Page': 'Profile Page',
                                            'Code':
                                                'click.profile_page.suggest_improvement'
                                          });
                                    },
                                  ),
                                  MenuItem(
                                    isAdjusted: true,
                                    size: 16,
                                    icon: AppAssets.contactnew,
                                    text: 'Contact us',
                                    trailingIcon: Icons.chevron_right,
                                    onTap: () {
                                      context.pushRoute(GotchaWebViewRoute(
                                        url: links.contactUs,
                                        title: 'Contact us',
                                      ));
                                      // mixpanel
                                      sl<MixpanelService>().trackButtonClick(
                                          'Contact Us',
                                          properties: {
                                            'Page': 'Profile Page',
                                            'Code':
                                                'click.profile_page.contact_us'
                                          });
                                    },
                                  ),
                                  MenuItem(
                                    size: 16,
                                    icon: AppAssets.exportmyaccountnew,
                                    text: 'Export my data',
                                    trailingIcon: Icons.chevron_right,
                                    onTap: () {
                                      context.pushRoute(
                                        GotchaWebViewRoute(
                                          url: links.exportMyData,
                                          title: 'Export my data',
                                        ),
                                      );
                                      // mixpanel
                                      sl<MixpanelService>().trackButtonClick(
                                          'Export My Data',
                                          properties: {
                                            'Page': 'Profile Page',
                                            'Code':
                                                'click.profile_page.export_my_data'
                                          });
                                    },
                                  ),
                                  if (isLoggedIn)
                                    MenuItem(
                                      size: 16,
                                      icon: AppAssets.deletemyaccount,
                                      text: 'Delete my account',
                                      trailingIcon: Icons.chevron_right,
                                      onTap: () {
                                        CustomCupertinoAlertDialog.yesOrNoPopup(
                                          context,
                                          title: 'Delete account',
                                          content:
                                              'Are you sure you want to delete your account?',
                                          yesButtonText: 'Delete',
                                          onYes: () {
                                            context
                                                .read<AuthCubit>()
                                                .deleteAccount();
                                            // mixpanel
                                            sl<MixpanelService>()
                                                .trackButtonClick(
                                                    'Delete Account',
                                                    properties: {
                                                  'Page': 'Profile Page',
                                                  'Code':
                                                      'click.profile_page.delete_account'
                                                });
                                          },
                                          onNo: () {
                                            Navigator.pop(context);
                                          },
                                        );
                                      },
                                    ),
                                  // MenuItem(
                                  //   icon: AppAssets.exporticon,
                                  //   text: 'Help Seeking',
                                  //   trailingIcon: Icons.chevron_right,
                                  //   onTap: () {
                                  //     context.pushRoute(
                                  //         const HelpSeekingPathwayRoute());

                                  //     // context.pushRoute(
                                  //     //   GotchaWebViewRoute(
                                  //     //     url: links?.help,
                                  //     //     title: 'I need more help',
                                  //     //   ),
                                  //     // );
                                  //   },
                                  // ),
                                  MenuItem(
                                    size: 16,
                                    icon: AppAssets.morehelpnew,
                                    text: 'I need more help',
                                    trailingIcon: Icons.chevron_right,
                                    onTap: () {
                                      context.pushRoute(
                                          const HelpSeekingPathwayRoute());
                                      // mixpanel
                                      sl<MixpanelService>().trackButtonClick(
                                          'I Need More Help',
                                          properties: {
                                            'Page': 'Profile Page',
                                            'Code':
                                                'click.profile_page.i_need_more_help'
                                          });
                                    },
                                  ),
                                ],
                              ),
                            ],
                          ),
                        ),
                        const Gap(
                          16,
                          color: AppColors.grey,
                        ),
                        // Resources Section
                        Container(
                          color: AppColors.grey,
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Padding(
                                padding:
                                    const EdgeInsets.symmetric(horizontal: 24),
                                child: Text('Resources',
                                    style: textTheme.bodyEmphasis),
                              ),
                              const Gap(8),
                              MenuRowWidget(
                                items: [
                                  MenuItem(
                                    size: 18,
                                    icon: AppAssets.forparentsandcaregivers,
                                    text: 'For parents and caregivers',
                                    trailingIcon: Icons.chevron_right,
                                    onTap: () {
                                      _launchUrl(links.parents ?? '');
                                      // mixpanel
                                      sl<MixpanelService>().trackButtonClick(
                                          'For Parents and Caregivers',
                                          properties: {
                                            'Page': 'Profile Page',
                                            'Code':
                                                'click.profile_page.for_parents_and_caregivers'
                                          });
                                      // context.pushRoute(
                                      //   GotchaWebViewRoute(
                                      //     url: links.parents,
                                      //     title: 'For Parents',
                                      //   ),
                                      // );
                                    },
                                  ),
                                  MenuItem(
                                    size: 16,
                                    icon: AppAssets.foreducatorsnew,
                                    text: 'For educators',
                                    trailingIcon: Icons.chevron_right,
                                    onTap: () {
                                      _launchUrl(links.teachers ?? '');
                                      // mixpanel
                                      sl<MixpanelService>().trackButtonClick(
                                          'For Educators',
                                          properties: {
                                            'Page': 'Profile Page',
                                            'Code':
                                                'click.profile_page.for_educators'
                                          });
                                      // context.pushRoute(
                                      //   GotchaWebViewRoute(
                                      //     url: links.teachers,
                                      //     title: 'For Teachers',
                                      //   ),
                                      // );
                                    },
                                  ),
                                  MenuItem(
                                    isAdjusted: isIos ? true : false,
                                    size: 16,
                                    icon: AppAssets.helpsomeonenew,
                                    text: 'Help someone you’re worried about',
                                    trailingIcon: Icons.chevron_right,
                                    onTap: () {
                                      _launchUrl(links.helpOthers ?? '');
                                      // mixpanel
                                      sl<MixpanelService>().trackButtonClick(
                                          'Help Someone You’re Worried About',
                                          properties: {
                                            'Page': 'Profile Page',
                                            'Code':
                                                'click.profile_page.help_someone_you_re_worried_about'
                                          });
                                      // context.pushRoute(
                                      //   GotchaWebViewRoute(
                                      //     url: links.teachers,
                                      //     title: 'For Teachers',
                                      //   ),
                                      // );
                                    },
                                  ),
                                ],
                              ),
                            ],
                          ),
                        ),
                        const Gap(
                          16,
                          color: AppColors.grey,
                        ),
                        // About Section
                        Container(
                          color: AppColors.grey,
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Padding(
                                padding:
                                    const EdgeInsets.symmetric(horizontal: 24),
                                child: Text('About',
                                    style: textTheme.bodyEmphasis),
                              ),
                              const Gap(8),
                              MenuRowWidget(
                                items: [
                                  MenuItem(
                                    size: 16,
                                    icon: AppAssets.privacypolicynew,
                                    text: 'Privacy policy',
                                    trailingIcon: Icons.chevron_right,
                                    onTap: () {
                                      context.pushRoute(
                                        GotchaWebViewRoute(
                                          url: links.privacyPolicy,
                                          title: 'Privacy policy',
                                        ),
                                      );
                                      // mixpanel
                                      sl<MixpanelService>().trackButtonClick(
                                          'Privacy Policy',
                                          properties: {
                                            'Page': 'Profile Page',
                                            'Code':
                                                'click.profile_page.privacy_policy'
                                          });
                                    },
                                  ),
                                  MenuItem(
                                    size: 13,
                                    isIconAdjust: true,
                                    icon: AppAssets.termsofusenew,
                                    text: 'Terms of use',
                                    trailingIcon: Icons.chevron_right,
                                    onTap: () {
                                      context.pushRoute(
                                        GotchaWebViewRoute(
                                          url: links.termsAndConditions,
                                          title: 'Terms of use',
                                        ),
                                      );
                                      // mixpanel
                                      sl<MixpanelService>().trackButtonClick(
                                          'Terms of Use',
                                          properties: {
                                            'Page': 'Profile Page',
                                            'Code':
                                                'click.profile_page.terms_of_use'
                                          });
                                    },
                                  ),
                                  MenuItem(
                                    size: 16,
                                    icon: AppAssets.infomessage,
                                    text: 'The science of mental fitness',
                                    trailingIcon: Icons.chevron_right,
                                    onTap: () {
                                      context.pushRoute(
                                        GotchaWebViewRoute(
                                          url: links.faq,
                                          title:
                                              'The science of mental fitness',
                                        ),
                                      );
                                      // mixpanel
                                      sl<MixpanelService>().trackButtonClick(
                                          'The Science of Mental Fitness',
                                          properties: {
                                            'Page': 'Profile Page',
                                            'Code':
                                                'click.profile_page.the_science_of_mental_fitness'
                                          });
                                    },
                                  ),
                                ],
                              ),
                            ],
                          ),
                        ),
                        const Gap(
                          32,
                          color: AppColors.grey,
                        ),
                        const AboutGotcha4LifeCard(),
                        const Gap(
                          32,
                          color: AppColors.grey,
                        ),
                        // Support Us Section
                        Container(
                          color: AppColors.grey,
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              // Padding(
                              //   padding:
                              //       const EdgeInsets.symmetric(horizontal: 24),
                              //   child: Text('Support us',
                              //       style: textTheme.bodyEmphasis),
                              // ),
                              // const Gap(10),
                              SignUpWidget(
                                icon: false,
                                title: 'Support a mentally fit future',
                                benefits: isIos
                                    ? [
                                        'The Mental Fitness Gym is free - and always will be. Your support helps us create and deliver free tools and resources that make mental fitness accessible for everyone.'
                                      ]
                                    : [
                                        'The Mental Fitness Gym is free - and always will be. Your support helps us create and deliver free tools and resources that make mental fitness accessible for everyone.',
                                      ],
                                buttonText: isIos
                                    ? 'More about Gotcha4Life'
                                    : 'More about Gotcha4Life',
                                onLoginPressed: null,
                                onSignUpPressed: () {
                                  context.pushRoute(
                                    GotchaWebViewRoute(
                                      url: isIos
                                          ? links.iosSupport
                                          : links.voluntaryMembership,
                                      title: isIos
                                          ? 'More about Gotcha4Life'
                                          : 'More about Gotcha4Life',
                                    ),
                                  );
                                  // mixpanel
                                  sl<MixpanelService>().trackButtonClick(
                                      'More About Gotcha4Life',
                                      properties: {
                                        'Page': 'Profile Page',
                                        'Code':
                                            'click.profile_page.more_about_gotcha4life'
                                      });
                                },
                              ),
                              const Gap(32),
                              const Padding(
                                padding: EdgeInsets.symmetric(horizontal: 24),
                                child: InfoMessage(
                                    showIcon: false,
                                    color: AppColors.lightBlue,
                                    message:
                                        'Everything in the Mental Fitness Gym is here to help you build mental fitness through practical, evidence-based tools. However, the information we provide isn’t a substitute for medical advice, diagnosis, or treatment. Gotcha4Life doesn’t offer counselling or crisis support services. For more information, we encourage you to explore the resources in the Getting More Support section.'),
                              )
                            ],
                          ),
                        ),
                        Gap(
                          isIos ? 88 : 64,
                          color: AppColors.grey,
                        ),
                      ],
                    ),
                  ),
                ),
              ),
            );
          }
          return const SizedBox();
        },
      ),
    );
  }
}
