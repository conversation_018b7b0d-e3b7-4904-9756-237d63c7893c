import 'package:auto_route/auto_route.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:gotcha_mfg_app/config/theme/app_colors.dart';
import 'package:gotcha_mfg_app/core/utils/platform_utils.dart';
import 'package:gotcha_mfg_app/features/profile/presentation/cubits/gym/gym_cubit.dart';
import 'package:gotcha_mfg_app/shared/widgets/app_header.dart';
import 'package:gotcha_mfg_app/shared/widgets/curved_separator.dart';
import 'package:gotcha_mfg_app/shared/widgets/loading_widget.dart';
import 'package:gotcha_mfg_app/shared/widgets/retry_widget.dart';

import '../../../../core/mixpanel_service.dart';
import '../../../../core/utils/snackbar_service.dart';
import '../../../../locator.dart';
import '../../data/models/gym_req.dart';
import '../widgets/gym_visits.dart';
import '../widgets/insight_dashboard.dart';
import '../widgets/date_calculator.dart';

@RoutePage()
class GymHistoryPage extends StatefulWidget {
  const GymHistoryPage({
    super.key,
    required this.selectedChip,
  });

  final String? selectedChip;

  @override
  State<GymHistoryPage> createState() => _GymHistoryPageState();
}

class _GymHistoryPageState extends State<GymHistoryPage> {
  final List<String> _timePeriods = [
    '14 days',
    '30 days',
    '90 days',
    '365 days'
  ];
  String _selectedTimePeriod = '14 days';

  @override
  void initState() {
    super.initState();
    sl<MixpanelService>().trackScreenView(
      'Gym History Page',
      properties: {'Code': 'screen_view.gym_history_page'},
    );
    _loadData();
  }

  void _loadData({int days = 14}) {
    String startDate = getDateBeforeDays(days);
    context.read<GymCubit>().getGymHistory(GymReq(date: startDate));
  }

  @override
  Widget build(BuildContext context) {
    return BlocConsumer<GymCubit, GymState>(
      listener: (context, state) {
        if (state is GymHistoryError) {
          SnackBarService.error(context: context, message: state.message);
        }
      },
      builder: (context, state) {
        if (state is GymHistoryLoading) {
          return const Scaffold(body: LoadingWidget(color: Colors.white));
        }

        if (state is GymHistoryError) {
          return Scaffold(
            body: RetryWidget(
              onRetry: () => _loadData(),
              color: Colors.white,
            ),
          );
        }

        if (state is GymHistoryLoaded) {
          return Scaffold(
              appBar: AppBar(
                toolbarHeight: 0,
                elevation: 0,
                systemOverlayStyle: const SystemUiOverlayStyle(
                  statusBarColor: Colors.white,
                  systemNavigationBarIconBrightness: Brightness.dark,
                  statusBarBrightness: Brightness.light,
                  statusBarIconBrightness: Brightness.dark,
                  systemNavigationBarColor: AppColors.lightBlue,
                ),
              ),
              body: Padding(
                padding: EdgeInsets.fromLTRB(
                  8,
                  isIos ? 4 : 8,
                  8,
                  0,
                ),
                child: Container(
                  decoration: const BoxDecoration(
                      color: AppColors.lightBlue,
                      borderRadius: BorderRadius.only(
                          topLeft: Radius.circular(30),
                          topRight: Radius.circular(30))),
                  child: ListView(
                    children: [
                      AppHeader(
                        onBackTap: () => Navigator.pop(context),
                        title: 'Gym history',
                      ),
                      const CurvedSeparator(
                        outerColor: AppColors.navy,
                        innerColor: AppColors.lightRed,
                      ),
                      InsightsDashboard(
                        timePeriods: _timePeriods,
                        selectedTimePeriod: _selectedTimePeriod,
                        gymHistory: state.gymResponse,
                        onTimePeriodChanged: (value) {
                          if (value != null) {
                            setState(() {
                              _selectedTimePeriod = value;
                            });
                            _loadData(days: int.parse(value.split(' ')[0]));
                          }
                        },
                      ),
                      const CurvedSeparator(
                        outerColor: AppColors.lightRed,
                        innerColor: AppColors.lightBlue,
                      ),
                      GymVisits(
                        gym: state.gymResponse,
                        selectedChip: widget.selectedChip,
                      ),
                    ],
                  ),
                ),
              ));
        }

        return const SizedBox(); // Placeholder for other states
      },
    );
  }
}
