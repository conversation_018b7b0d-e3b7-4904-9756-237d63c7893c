import 'package:gotcha_mfg_app/features/notification/data/models/notification_count.dart';
import 'package:gotcha_mfg_app/features/notification/data/models/notification_get_response.dart';
import 'package:gotcha_mfg_app/features/notification/data/models/notification_post_response.dart';
import 'package:gotcha_mfg_app/features/notification/data/models/notification_read_response.dart';

abstract class NotificationState {}

class NotificationInitial extends NotificationState {}

class NotificationLoading extends NotificationState {}

class NotificationGetLoaded extends NotificationState {
  NotificationGetLoaded(
    this.notificationResponse,
  );
  final NotificationGetResponse notificationResponse;
}

class NotificationPutLoaded extends NotificationState {
  NotificationPutLoaded(this.notificationPutResponse);

  final NotificationPutResponse notificationPutResponse;
}

class NotificationPostLoaded extends NotificationState {
  NotificationPostLoaded(this.notificationPostResponse);
  final NotificationPostResponse notificationPostResponse;
}

class NotificationCountLoaded extends NotificationState {
  NotificationCountLoaded(this.notificationCountResponse);
  final NotificationCountResponse notificationCountResponse;
}

class NotificationError extends NotificationState {
  final String message;
  NotificationError({required this.message});
}
