import 'package:bloc/bloc.dart';
import 'package:gotcha_mfg_app/core/usecase/usecase.dart';
import 'package:gotcha_mfg_app/features/notification/data/models/notification_get_request.dart';
import 'package:gotcha_mfg_app/features/notification/data/models/notification_post_request.dart';
import 'package:gotcha_mfg_app/features/notification/data/models/notification_read_request.dart';
import 'package:gotcha_mfg_app/features/notification/domain/usecases/get_notification_usecase.dart';
import 'package:gotcha_mfg_app/features/notification/domain/usecases/post_notification.dart';
import 'package:gotcha_mfg_app/features/notification/domain/usecases/put_notification.dart';
import 'package:gotcha_mfg_app/features/notification/presentation/blocs/notification/notification_state.dart';

import '../../../domain/usecases/get_notification_count_usecase.dart';

class NotificationCubit extends Cubit<NotificationState> {
  NotificationCubit(
      this._getNotificationUseCase,
      this._putNotificationUseCase,
      this._postNotificationReminderTimeUseCase,
      this._getNotificationCountUseCase)
      : super(NotificationInitial());

  final GetNotificationUseCase _getNotificationUseCase;
  final PutNotificationUseCase _putNotificationUseCase;
  final PostNotificationReminderTimeUseCase
      _postNotificationReminderTimeUseCase;
  final GetNotificationCountUseCase _getNotificationCountUseCase;

  Future<void> getNotification(NotificationGetRequestParams request) async {
    emit(NotificationLoading());
    final result = await _getNotificationUseCase.call(request);
    if (result.isSuccess) {
      emit(NotificationGetLoaded(result.data!));
    } else {
      emit(NotificationError(
        message: result.error.toString(),
      ));
    }
  }

  Future<void> putNotification(NotificationPutRequestParams request) async {
    emit(NotificationLoading());
    final result = await _putNotificationUseCase.call(request);
    if (result.isSuccess) {
      emit(NotificationPutLoaded(result.data!));
    } else {
      emit(NotificationError(
        message: result.error.toString(),
      ));
    }
  }

  Future<void> postNotificationReminderTime(
      NotificationPostRequestParams request) async {
    emit(NotificationLoading());
    final result = await _postNotificationReminderTimeUseCase.call(request);
    if (result.isSuccess) {
      emit(NotificationPostLoaded(result.data!));
    } else {
      emit(NotificationError(
        message: result.error.toString(),
      ));
    }
  }

  Future<void> getNotificationCount() async {
    emit(NotificationLoading());
    final result = await _getNotificationCountUseCase.call(NoParams());
    if (result.isSuccess) {
      emit(NotificationCountLoaded(result.data!));
    } else {
      emit(NotificationError(
        message: result.error.toString(),
      ));
    }
  }
}
