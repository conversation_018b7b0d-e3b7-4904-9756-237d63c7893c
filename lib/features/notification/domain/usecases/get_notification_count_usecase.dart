import 'package:gotcha_mfg_app/core/usecase/usecase.dart';
import 'package:gotcha_mfg_app/features/notification/data/data_sources/notification_datasource.dart';
import 'package:gotcha_mfg_app/features/notification/data/models/notification_count.dart';
import 'package:gotcha_mfg_app/shared/models/result.dart';

/// UseCase for get notification count
class GetNotificationCountUseCase
    implements UseCase<Result<NotificationCountResponse>, NoParams> {
  /// Constructor
  GetNotificationCountUseCase(this._repository);

  final NotificationRepository _repository;

  @override
  Future<Result<NotificationCountResponse>> call(NoParams params) async {
    return _repository.getNotificationCount();
  }
}
