import 'package:gotcha_mfg_app/core/usecase/usecase.dart';
import 'package:gotcha_mfg_app/features/notification/data/data_sources/notification_datasource.dart';
import 'package:gotcha_mfg_app/features/notification/data/models/notification_get_request.dart';
import 'package:gotcha_mfg_app/features/notification/data/models/notification_get_response.dart';
import 'package:gotcha_mfg_app/shared/models/result.dart';

class GetNotificationUseCase
    implements
        UseCase<Result<NotificationGetResponse>, NotificationGetRequestParams> {
  GetNotificationUseCase(this._notificationRepository);

  final NotificationRepository _notificationRepository;

  @override
  Future<Result<NotificationGetResponse>> call(
      NotificationGetRequestParams params) async {
    return await _notificationRepository.getNotification(params);
  }
}
