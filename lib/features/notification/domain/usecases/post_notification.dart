import 'package:gotcha_mfg_app/core/usecase/usecase.dart';
import 'package:gotcha_mfg_app/features/notification/data/data_sources/notification_datasource.dart';
import 'package:gotcha_mfg_app/features/notification/data/models/notification_post_request.dart';
import 'package:gotcha_mfg_app/features/notification/data/models/notification_post_response.dart';
import 'package:gotcha_mfg_app/shared/models/result.dart';

class PostNotificationReminderTimeUseCase
    implements
        UseCase<Result<NotificationPostResponse>,
            NotificationPostRequestParams> {
  PostNotificationReminderTimeUseCase(this._notificationRepository);

  final NotificationRepository _notificationRepository;

  @override
  Future<Result<NotificationPostResponse>> call(
      NotificationPostRequestParams params) async {
    return await _notificationRepository.postNotificationReminderTime(params);
  }
}
