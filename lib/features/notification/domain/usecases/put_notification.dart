import 'package:gotcha_mfg_app/core/usecase/usecase.dart';
import 'package:gotcha_mfg_app/features/notification/data/data_sources/notification_datasource.dart';
import 'package:gotcha_mfg_app/features/notification/data/models/notification_read_request.dart';
import 'package:gotcha_mfg_app/features/notification/data/models/notification_read_response.dart';
import 'package:gotcha_mfg_app/shared/models/result.dart';

class PutNotificationUseCase
    implements
        UseCase<Result<NotificationPutResponse>, NotificationPutRequestParams> {
  PutNotificationUseCase(this._notificationRepository);

  final NotificationRepository _notificationRepository;

  @override
  Future<Result<NotificationPutResponse>> call(
      NotificationPutRequestParams params) async {
    return await _notificationRepository.putNotification(params);
  }
}
