import 'package:gotcha_mfg_app/features/notification/data/models/notification_get_request.dart';
import 'package:gotcha_mfg_app/features/notification/data/models/notification_get_response.dart';
import 'package:gotcha_mfg_app/features/notification/data/models/notification_post_request.dart';
import 'package:gotcha_mfg_app/features/notification/data/models/notification_post_response.dart';
import 'package:gotcha_mfg_app/features/notification/data/models/notification_read_request.dart';
import 'package:gotcha_mfg_app/features/notification/data/models/notification_read_response.dart';
import 'package:gotcha_mfg_app/shared/models/result.dart';

import '../../data/models/notification_count.dart';

/// Notification remote data source interface
abstract class NotificationRemoteDataSource {
  /// Get Notification
  Future<Result<NotificationGetResponse>> getNotification(
      NotificationGetRequestParams request);

  /// Put Notification
  Future<Result<NotificationPutResponse>> putNotification(
      NotificationPutRequestParams request);

  /// Post Notification Reminder Time
  Future<Result<NotificationPostResponse>> postNotificationReminderTime(
      NotificationPostRequestParams request);

  /// Get Notification Count
  Future<Result<NotificationCountResponse>> getNotificationCount();
}
