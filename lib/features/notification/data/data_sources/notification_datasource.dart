import 'package:dio/dio.dart';
import 'package:gotcha_mfg_app/features/notification/data/models/notification_get_request.dart';
import 'package:gotcha_mfg_app/features/notification/data/models/notification_get_response.dart';
import 'package:gotcha_mfg_app/features/notification/data/models/notification_post_request.dart';
import 'package:gotcha_mfg_app/features/notification/data/models/notification_post_response.dart';
import 'package:gotcha_mfg_app/features/notification/data/models/notification_read_request.dart';
import 'package:gotcha_mfg_app/features/notification/data/models/notification_read_response.dart';
import 'package:gotcha_mfg_app/features/notification/domain/repositories/notification_repository.dart';
import 'package:gotcha_mfg_app/shared/models/result.dart';

import '../models/notification_count.dart';

abstract class NotificationRepository {
  Future<Result<NotificationGetResponse>> getNotification(
      NotificationGetRequestParams request);
  Future<Result<NotificationPutResponse>> putNotification(
      NotificationPutRequestParams request);
  Future<Result<NotificationPostResponse>> postNotificationReminderTime(
      NotificationPostRequestParams request);
  Future<Result<NotificationCountResponse>> getNotificationCount();
}

class NotificationRemoteDataSourceImpl implements NotificationRemoteDataSource {
  NotificationRemoteDataSourceImpl(this._dio);

  final Dio _dio;

  @override
  Future<Result<NotificationGetResponse>> getNotification(
      NotificationGetRequestParams request) async {
    try {
      final response = await _dio.get('/app/notification');
      if (response.statusCode == 200) {
        final data = response.data;
        final notificationResponse = NotificationGetResponse.fromJson(data);
        return Result.success(notificationResponse);
      } else {
        return Result.failure(
            '${response.statusCode} - ${response.data['message']}');
      }
    } catch (e) {
      return Result.failure(
          'An error occurred during getting notification: ${e.toString()}');
    }
  }

  @override
  Future<Result<NotificationPutResponse>> putNotification(
      NotificationPutRequestParams request) async {
    try {
      final response = await _dio.put(
          '/app/notification/${request.id}?is_read=${request.isRead}'); // Assuming toJson() method exists for RequestParams
      if (response.statusCode == 200) {
        final data = response.data;
        final notificationResponse = NotificationPutResponse.fromJson(data);
        return Result.success(notificationResponse);
      } else {
        return Result.failure(
            '${response.statusCode} - ${response.data['message']}');
      }
    } catch (e) {
      return Result.failure(
          'An error occurred during putting notification: ${e.toString()}');
    }
  }

  @override
  Future<Result<NotificationPostResponse>> postNotificationReminderTime(
      NotificationPostRequestParams request) async {
    try {
      final response = await _dio.post('/app/notification/reminder/time',
          data: request
              .toJson()); // Assuming toJson() method exists for RequestParams
      if (response.statusCode == 200) {
        final data = response.data;
        final notificationResponse = NotificationPostResponse.fromJson(data);
        return Result.success(notificationResponse);
      } else {
        return Result.failure(
            '${response.statusCode} - ${response.data['message']}');
      }
    } catch (e) {
      return Result.failure(
          'An error occurred during posting notification reminder time: ${e.toString()}');
    }
  }

  @override
  Future<Result<NotificationCountResponse>> getNotificationCount() async {
    try {
      final response = await _dio.get('/app/notification/count');
      if (response.statusCode == 200) {
        final data = response.data;
        final notificationCountResponse =
            NotificationCountResponse.fromJson(data);

        return Result.success(notificationCountResponse);
      } else {
        return Result.failure(
            '${response.statusCode} - ${response.data['message']}');
      }
    } catch (e) {
      return Result.failure(
          'An error occurred during getting notification count: ${e.toString()}');
    }
  }
}
