import 'package:gotcha_mfg_app/features/notification/data/data_sources/notification_datasource.dart';
import 'package:gotcha_mfg_app/features/notification/data/models/notification_get_request.dart';
import 'package:gotcha_mfg_app/features/notification/data/models/notification_get_response.dart';
import 'package:gotcha_mfg_app/features/notification/data/models/notification_post_request.dart';
import 'package:gotcha_mfg_app/features/notification/data/models/notification_post_response.dart';
import 'package:gotcha_mfg_app/features/notification/data/models/notification_read_request.dart';
import 'package:gotcha_mfg_app/features/notification/data/models/notification_read_response.dart';
import 'package:gotcha_mfg_app/features/notification/domain/repositories/notification_repository.dart';
import 'package:gotcha_mfg_app/shared/models/result.dart';

import '../models/notification_count.dart';

class NotificationRepositoryImpl implements NotificationRepository {
  NotificationRepositoryImpl(this._remoteDataSource);

  final NotificationRemoteDataSource _remoteDataSource;

  @override
  Future<Result<NotificationGetResponse>> getNotification(
      NotificationGetRequestParams request) async {
    return await _remoteDataSource.getNotification(request);
  }

  @override
  Future<Result<NotificationPutResponse>> putNotification(
      NotificationPutRequestParams request) async {
    return await _remoteDataSource.putNotification(request);
  }

  @override
  Future<Result<NotificationPostResponse>> postNotificationReminderTime(
      NotificationPostRequestParams request) async {
    return await _remoteDataSource.postNotificationReminderTime(request);
  }

  @override
  Future<Result<NotificationCountResponse>> getNotificationCount() async {
    return await _remoteDataSource.getNotificationCount();
  }
}
