// response/village_user_response.dart
class NotificationPutResponse {
  NotificationPutResponse({
    required this.message,
    required this.status,
    this.data,
  });

  final String message;
  final String status;
  final dynamic data;

  factory NotificationPutResponse.fromJson(Map<String, dynamic> json) =>
      NotificationPutResponse(
        message: json["message"],
        status: json["status"],
        data: json["data"],
      );

  Map<String, dynamic> toJson() => {
        "message": message,
        "status": status,
        "data": data,
      };
}
