import 'dart:convert';

class NotificationGetResponse {
  final String? message;
  final String? status;
  final List<Datum>? data;

  NotificationGetResponse({
    this.message,
    this.status,
    this.data,
  });

  NotificationGetResponse copyWith({
    String? message,
    String? status,
    List<Datum>? data,
  }) =>
      NotificationGetResponse(
        message: message ?? this.message,
        status: status ?? this.status,
        data: data ?? this.data,
      );

  factory NotificationGetResponse.fromRawJson(String str) =>
      NotificationGetResponse.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory NotificationGetResponse.fromJson(Map<String, dynamic> json) =>
      NotificationGetResponse(
        message: json["message"],
        status: json["status"],
        data: json["data"] == null
            ? []
            : List<Datum>.from(json["data"]!.map((x) => Datum.fromJson(x))),
      );

  Map<String, dynamic> toJson() => {
        "message": message,
        "status": status,
        "data": data == null
            ? []
            : List<dynamic>.from(data!.map((x) => x.toJson())),
      };
}

class Datum {
  final String? id;
  final String? notificationId;
  final String? title;
  final String? body;
  final String? onTapUrl;
  final DateTime? createdAt;
  final DateTime? updatedAt;

  Datum({
    this.id,
    this.notificationId,
    this.title,
    this.body,
    this.onTapUrl,
    this.createdAt,
    this.updatedAt,
  });

  Datum copyWith({
    String? id,
    String? notificationId,
    String? title,
    String? body,
    String? onTapUrl,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) =>
      Datum(
        id: id ?? this.id,
        notificationId: notificationId ?? this.notificationId,
        title: title ?? this.title,
        body: body ?? this.body,
        onTapUrl: onTapUrl ?? this.onTapUrl,
        createdAt: createdAt ?? this.createdAt,
        updatedAt: updatedAt ?? this.updatedAt,
      );

  factory Datum.fromRawJson(String str) => Datum.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory Datum.fromJson(Map<String, dynamic> json) => Datum(
        id: json["id"],
        notificationId: json["notification_id"],
        title: json["title"],
        body: json["body"],
        onTapUrl: json["on_tap_url"],
        createdAt: json["created_at"] == null
            ? null
            : DateTime.parse(json["created_at"]),
        updatedAt: json["updated_at"] == null
            ? null
            : DateTime.parse(json["updated_at"]),
      );

  Map<String, dynamic> toJson() => {
        "id": id,
        "notification_id": notificationId,
        "title": title,
        "body": body,
        "on_tap_url": onTapUrl,
        "created_at": createdAt?.toIso8601String(),
        "updated_at": updatedAt?.toIso8601String(),
      };
}
