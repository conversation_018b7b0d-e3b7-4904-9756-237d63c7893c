import 'dart:convert';

class NotificationCountResponse {
  final String? message;
  final String? status;
  final Data? data;

  NotificationCountResponse({
    this.message,
    this.status,
    this.data,
  });

  NotificationCountResponse copyWith({
    String? message,
    String? status,
    Data? data,
  }) =>
      NotificationCountResponse(
        message: message ?? this.message,
        status: status ?? this.status,
        data: data ?? this.data,
      );

  factory NotificationCountResponse.fromRawJson(String str) =>
      NotificationCountResponse.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory NotificationCountResponse.fromJson(Map<String, dynamic> json) =>
      NotificationCountResponse(
        message: json["message"],
        status: json["status"],
        data: json["data"] == null ? null : Data.fromJson(json["data"]),
      );

  Map<String, dynamic> toJson() => {
        "message": message,
        "status": status,
        "data": data?.toJson(),
      };
}

class Data {
  final int? count;

  Data({
    this.count,
  });

  Data copyWith({
    int? count,
  }) =>
      Data(
        count: count ?? this.count,
      );

  factory Data.fromRawJson(String str) => Data.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory Data.fromJson(Map<String, dynamic> json) => Data(
        count: json["count"],
      );

  Map<String, dynamic> toJson() => {
        "count": count,
      };
}
