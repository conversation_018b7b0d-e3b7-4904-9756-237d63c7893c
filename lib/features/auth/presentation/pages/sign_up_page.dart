// import 'package:auto_route/auto_route.dart';
// import 'package:flutter/gestures.dart';
// import 'package:flutter/material.dart';
// import 'package:flutter/services.dart';
// import 'package:flutter_bloc/flutter_bloc.dart';
// import 'package:flutter_timezone/flutter_timezone.dart';
// import 'package:gap/gap.dart';
// import 'package:gotcha_mfg_app/config/router/app_router.gr.dart';
// import 'package:gotcha_mfg_app/config/theme/app_colors.dart';
// import 'package:gotcha_mfg_app/core/extensions/app_text_theme_extension.dart';
// import 'package:gotcha_mfg_app/core/firebase_push.dart';
// import 'package:gotcha_mfg_app/core/storage/token_manager.dart';
// import 'package:gotcha_mfg_app/core/utils/device_info.dart';
// import 'package:gotcha_mfg_app/features/auth/presentation/blocs/signup/signup_cubit.dart';
// import 'package:gotcha_mfg_app/features/profile/presentation/cubits/profile_links/profile_links_cubit.dart';
// import 'package:gotcha_mfg_app/shared/widgets/app_header.dart';
// import 'package:gotcha_mfg_app/shared/widgets/app_safe_area.dart';
// import 'package:gotcha_mfg_app/shared/widgets/primary_button.dart';

// import '../../../../core/mixpanel_service.dart';
// import '../../../../core/utils/platform_utils.dart';
// import '../../../../core/utils/snackbar_service.dart';
// import '../../../../locator.dart';
// import '../../../../shared/widgets/loading_widget.dart';
// import '../../../notification/presentation/blocs/notification/notification_cubit.dart';
// import '../../data/models/fcm_req_model.dart';
// import '../../data/models/sign_up_request_model.dart';
// import '../blocs/fcm_update/fcm_update_cubit.dart';
// import '../widgets/auth_text_field.dart';
// import '../widgets/login_validator.dart';

// @RoutePage()
// class SignUpPage extends StatefulWidget {
//   const SignUpPage({super.key});
//   @override
//   State<SignUpPage> createState() => _SignUpPageState();
// }

// class _SignUpPageState extends State<SignUpPage> {
//   bool? hasLetter;
//   bool? hasNum;
//   bool? hasLength;
//   TextEditingController emailController = TextEditingController();
//   TextEditingController passwordController = TextEditingController();
//   String? emailErrorText;
//   String? passwordErrorText;
//   bool hasConsented = false;
//   bool isFormValid = false;

//   @override
//   void initState() {
//     super.initState();
//     sl<MixpanelService>().trackScreenView(
//       'Sign Up Page',
//       properties: {'Code': 'screen_view.sign_up_page'},
//     );
//     _validateForm();
//   }

//   void _validateForm() {
//     setState(() {
//       isFormValid = _isEmailValid() && _isPasswordValid() && hasConsented;
//     });
//   }

//   bool _isEmailValid() {
//     if (emailController.text.isEmpty) {
//       emailErrorText = null;
//       return true;
//     }
//     if (!isValidEmail(emailController.text)) {
//       emailErrorText = "Enter a valid email address";
//       return false;
//     } else {
//       emailErrorText = null;
//       return true;
//     }
//   }

//   bool _isPasswordValid() {
//     if (passwordController.text.isEmpty) {
//       passwordErrorText = null;
//       return true;
//     }
//     if (!(hasLetter == true && hasNum == true && hasLength == true)) {
//       passwordErrorText = "Password does not meet requirements";
//       return false;
//     } else {
//       passwordErrorText = null;
//       return true;
//     }
//   }

//   bool isValidEmail(String email) {
//     return RegExp(
//             r"^[a-zA-Z0-9.a-zA-Z0-9.!#$%&'*+-/=?^_`{|}~]+@[a-zA-Z0-9]+\.[a-zA-Z]+")
//         .hasMatch(email);
//   }

//   Future<String?> deviceId() async {
//     // Made return type nullable for safety
//     // Assuming getDeviceId() returns a String or null.
//     return await getDeviceId();
//   }

//   Future<String?> fcmToken() async {
//     // Made return type nullable for safety
//     var token = await MFGPushNotification.messaging.getToken();
//     return token;
//   }

//   Future<String?> uniqueId() async {
//     // Made return type nullable for safety
//     var id = await sl<TokenManager>().getUniqueId();
//     return id;
//   }

//   Future<void> updateToken(BuildContext context) async {
//     try {
//       final token = await fcmToken();
//       final device = await deviceId();
//       final unique = await uniqueId();

//       // Ensure we have a context after the async operations complete
//       if (context.mounted) {
//         // Check context is valid before using it
//         context
//             .read<FcmUpdateCubit>() // Make sure data types match your model
//             .updateFcmToken(FcmRequestModel(
//               // Make sure data types match your model
//               token: token ?? '', // Provide a default if null
//               fcmDeviceId: device ?? '', // Provide a default if null
//             ));
//       } else {
//         print("Context is no longer valid, cannot update token.");
//       }
//     } catch (e) {
//       print("Error updating token: $e");
//     }
//   }

//   @override
//   Widget build(BuildContext context) {
//     final textTheme = Theme.of(context).textTheme;
//     final size = MediaQuery.of(context).size;
//     return BlocConsumer<SignupCubit, SignupState>(listener: (context, state) {
//       if (state is SignUpError) {
//         SnackBarService.error(
//           context: context,
//           message: state.message,
//         );
//       }
//       if (state is SignUpSuccess) {
//         updateToken(context);
//         // SnackBarService.info(
//         //   context: context,
//         //   message: "Signed in successfully.",
//         // );
//         context.read<ProfileLinksCubit>().getProfileLinks();
//         context.read<NotificationCubit>().getNotificationCount();
//         context.replaceRoute(
//           PersonaliseAccountRoute(
//             isPersonalise: true,
//             isInfo: true,
//           ),
//         );
//       }
//     }, builder: (context, state) {
//       var size = MediaQuery.of(context).size;
//       if (state is SignUpLoading) {
//         return SizedBox(
//             height: size.height,
//             child: const LoadingWidget(color: Colors.white));
//       }

//       return Scaffold(
//           resizeToAvoidBottomInset: false,
//           appBar: AppBar(
//             toolbarHeight: 0,
//             elevation: 0,
//             systemOverlayStyle: const SystemUiOverlayStyle(
//               statusBarColor: Colors.white,
//               systemNavigationBarIconBrightness: Brightness.dark,
//               statusBarBrightness: Brightness.light,
//               statusBarIconBrightness: Brightness.dark,
//               systemNavigationBarColor: AppColors.grey,
//             ),
//           ),
//           body: AppSafeArea(
//             child: GestureDetector(
//               onTap: () {
//                 FocusScope.of(context).unfocus();
//               },
//               behavior: HitTestBehavior.translucent,
//               child: Padding(
//                 padding: EdgeInsets.only(
//                   top: isIos ? 4 : 8,
//                   right: 8,
//                   left: 8,
//                 ),
//                 child: Column(
//                   children: [
//                     AppHeader(
//                       title: 'Create account',
//                       onBackTap: () {
//                         Navigator.pop(context);
//                       },
//                     ),
//                     Expanded(
//                       child: Container(
//                         color: AppColors.navy,
//                         child: Container(
//                           decoration: const BoxDecoration(
//                             borderRadius: BorderRadius.only(
//                               topLeft: Radius.circular(30),
//                               topRight: Radius.circular(30),
//                             ),
//                             color: AppColors.grey,
//                           ),
//                           child: Padding(
//                             padding: const EdgeInsets.all(24),
//                             child: Column(
//                               crossAxisAlignment: CrossAxisAlignment.start,
//                               children: [
//                                 const Gap(12),
//                                 AuthTextField(
//                                   keyboardType: TextInputType.emailAddress,
//                                   controller: emailController,
//                                   text: 'Email',
//                                   hinttext: 'Enter your email address',
//                                   errorText: emailErrorText,
//                                   onChanged: (val) {
//                                     final cursorPosition =
//                                         emailController.selection;
//                                     setState(() {
//                                       emailController.text = val;
//                                       _isEmailValid();
//                                       _validateForm();
//                                     });
//                                     emailController.selection = cursorPosition;
//                                   },
//                                 ),
//                                 const Gap(16),
//                                 AuthTextField(
//                                   isPassword: true,
//                                   controller: passwordController,
//                                   onChanged: (val) {
//                                     final cursorPosition =
//                                         passwordController.selection;
//                                     setState(() {
//                                       passwordController.text = val;
//                                       check();
//                                       _isPasswordValid();
//                                       _validateForm();
//                                     });
//                                     passwordController.selection =
//                                         cursorPosition;
//                                   },
//                                   text: 'Password',
//                                   hinttext: 'Enter your password',
//                                   errorText: passwordErrorText,
//                                 ),
//                                 const Gap(16),
//                                 PasswordChecklist(
//                                   hasLetter: hasLetter,
//                                   hasNumber: hasNum,
//                                   hasMinCharacters: hasLength,
//                                   controller: passwordController,
//                                   textTheme: textTheme,
//                                 ),
//                                 const Spacer(),
//                                 Row(
//                                   crossAxisAlignment: CrossAxisAlignment.start,
//                                   children: [
//                                     Checkbox(
//                                       value: hasConsented,
//                                       fillColor: WidgetStatePropertyAll(
//                                           !hasConsented
//                                               ? Colors.white
//                                               : AppColors.coral),
//                                       activeColor: Colors.white,
//                                       checkColor: Colors.white,
//                                       side: const BorderSide(
//                                           color: AppColors.coral),
//                                       onChanged: (val) {
//                                         setState(() {
//                                           hasConsented = !hasConsented;
//                                           _validateForm();
//                                         });
//                                       },
//                                       visualDensity: VisualDensity.compact,
//                                       materialTapTargetSize:
//                                           MaterialTapTargetSize.shrinkWrap,
//                                       shape: RoundedRectangleBorder(
//                                           borderRadius:
//                                               BorderRadius.circular(6)),
//                                     ),
//                                     const Gap(4),
//                                     Expanded(
//                                       child: RichText(
//                                         text: TextSpan(
//                                           children: [
//                                             TextSpan(
//                                               text:
//                                                   "I consent to the collection, storage and use of the personal data I provide (e.g email, name, gym activity) for personalising my experience and improving app functionality as described in our ",
//                                               style: textTheme.labels,
//                                             ),
//                                             TextSpan(
//                                               text: "privacy policy",
//                                               style: textTheme.linkText
//                                                   .copyWith(
//                                                       color: AppColors.coral),
//                                               recognizer: TapGestureRecognizer()
//                                                 ..onTap = () {
//                                                   context.pushRoute(
//                                                     GotchaWebViewRoute(
//                                                       url:
//                                                           "https://www.gotcha4life.org/privacy-policy",
//                                                       title: 'Privacy policy',
//                                                     ),
//                                                   );
//                                                 },
//                                             ),
//                                             TextSpan(
//                                               text: ".*",
//                                               style: textTheme.linkText
//                                                   .copyWith(
//                                                       color: AppColors.coral),
//                                             ),
//                                           ],
//                                         ),
//                                       ),
//                                     ),
//                                   ],
//                                 ),
//                                 const Gap(24),
//                                 SizedBox(
//                                   width: size.width,
//                                   child: PrimaryButton(
//                                     text: 'Sign up',
//                                     isEnabled:
//                                         isFormValid, // Enable/disable button based on form validity
//                                     onPressed: () async {
//                                       if (emailController.text.isEmpty ||
//                                           passwordController.text.isEmpty) {
//                                         SnackBarService.error(
//                                           context: context,
//                                           message:
//                                               "Please fill required fields!",
//                                         );
//                                         return;
//                                       }
//                                       if (!_isEmailValid()) {
//                                         setState(
//                                             () {}); // Rebuild to show email error
//                                         return;
//                                       }
//                                       if (!_isPasswordValid()) {
//                                         setState(
//                                             () {}); // Rebuild to show password error
//                                         return;
//                                       }
//                                       if (!hasConsented) {
//                                         SnackBarService.error(
//                                           context: context,
//                                           message:
//                                               "Please accept the privacy policy.",
//                                         );
//                                         return;
//                                       }
//                                       final String currentTimeZone =
//                                           await FlutterTimezone
//                                               .getLocalTimezone();
//                                       context.read<SignupCubit>().signUp(
//                                             SignUpRequest(
//                                               timezone: currentTimeZone,
//                                               email: emailController.text,
//                                               password: passwordController.text,
//                                             ),
//                                           );
//                                     },
//                                   ),
//                                 ),
//                                 const Gap(24),
//                                 Row(
//                                   mainAxisAlignment: MainAxisAlignment.center,
//                                   children: [
//                                     Text('Already have an account?',
//                                         style: textTheme.labels),
//                                     const Gap(4),
//                                     GestureDetector(
//                                       onTap: () {
//                                         context
//                                             .replaceRoute(const LoginRoute());
//                                       },
//                                       child: Text(
//                                         'Log in',
//                                         style: textTheme.linkText.copyWith(
//                                           color: AppColors.coral,
//                                         ),
//                                       ),
//                                     ),
//                                   ],
//                                 ),
//                                 isIos ? const Gap(24) : const Gap(16),
//                               ],
//                             ),
//                           ),
//                         ),
//                       ),
//                     ),
//                   ],
//                 ),
//               ),
//             ),
//           ));
//     });
//   }

//   check() {
//     setState(() {
//       var value = validateInput(passwordController.text);
//       hasNum = value['number'];
//       hasLetter = value['letter'];
//       hasLength = value['totalCharacters'];
//     });
//   }

//   Map<String, bool> validateInput(String input) {
//     bool hasNumber = input.contains(RegExp(r'\d'));
//     bool hasLetter = input.contains(RegExp(r'[a-zA-Z]'));
//     bool hasMinLength = input.length >= 6;

//     return {
//       'number': hasNumber,
//       'letter': hasLetter,
//       'totalCharacters': hasMinLength,
//     };
//   }
// }
import 'package:auto_route/auto_route.dart';
import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_timezone/flutter_timezone.dart';
import 'package:gap/gap.dart';
import 'package:gotcha_mfg_app/config/router/app_router.gr.dart';
import 'package:gotcha_mfg_app/config/theme/app_colors.dart';
import 'package:gotcha_mfg_app/core/extensions/app_text_theme_extension.dart';
import 'package:gotcha_mfg_app/core/firebase_push.dart';
import 'package:gotcha_mfg_app/core/storage/token_manager.dart';
import 'package:gotcha_mfg_app/core/utils/app_print.dart';
import 'package:gotcha_mfg_app/core/utils/device_info.dart';
import 'package:gotcha_mfg_app/features/auth/presentation/blocs/signup/signup_cubit.dart';
import 'package:gotcha_mfg_app/features/profile/presentation/cubits/profile_links/profile_links_cubit.dart';
import 'package:gotcha_mfg_app/shared/widgets/app_header.dart';
import 'package:gotcha_mfg_app/shared/widgets/app_safe_area.dart';
import 'package:gotcha_mfg_app/shared/widgets/primary_button.dart';

import '../../../../core/mixpanel_service.dart';
import '../../../../core/utils/platform_utils.dart';
import '../../../../core/utils/snackbar_service.dart';
import '../../../../locator.dart';
import '../../../../shared/widgets/loading_widget.dart';
import '../../data/models/fcm_req_model.dart';
import '../../data/models/sign_up_request_model.dart';
import '../blocs/fcm_update/fcm_update_cubit.dart';
import '../widgets/auth_text_field.dart';
import '../widgets/login_validator.dart';

@RoutePage()
class SignUpPage extends StatefulWidget {
  const SignUpPage({super.key});
  @override
  State<SignUpPage> createState() => _SignUpPageState();
}

class _SignUpPageState extends State<SignUpPage> {
  bool? hasLetter;
  bool? hasNum;
  bool? hasLength;
  TextEditingController emailController = TextEditingController();
  TextEditingController passwordController = TextEditingController();
  String? emailErrorText;
  String? passwordErrorText;
  bool hasConsented = false;
  bool isFormValid = false;

  @override
  void initState() {
    super.initState();
    sl<MixpanelService>().trackScreenView(
      'Sign Up Page',
      properties: {'Code': 'screen_view.sign_up_page'},
    );
    _validateForm();
  }

  void _validateForm() {
    setState(() {
      isFormValid = _isEmailValid() && _isPasswordValid() && hasConsented;
    });
  }

  bool _isEmailValid() {
    if (emailController.text.isEmpty) {
      emailErrorText = null;
      return true;
    }
    if (!isValidEmail(emailController.text)) {
      emailErrorText = "Enter a valid email address";
      return false;
    } else {
      emailErrorText = null;
      return true;
    }
  }

  bool _isPasswordValid() {
    if (passwordController.text.isEmpty) {
      passwordErrorText = null;
      return true;
    }
    if (!(hasLetter == true && hasNum == true && hasLength == true)) {
      passwordErrorText = "Password does not meet requirements";
      return false;
    } else {
      passwordErrorText = null;
      return true;
    }
  }

  bool isValidEmail(String email) {
    return RegExp(
            r"^[a-zA-Z0-9.a-zA-Z0-9.!#$%&'*+-/=?^_`{|}~]+@[a-zA-Z0-9]+\.[a-zA-Z]+")
        .hasMatch(email);
  }

  Future<String?> deviceId() async {
    // Made return type nullable for safety
    // Assuming getDeviceId() returns a String or null.
    return await getDeviceId();
  }

  Future<String?> fcmToken() async {
    // Made return type nullable for safety
    var token = await MFGPushNotification.messaging.getToken();
    return token;
  }

  Future<String?> uniqueId() async {
    // Made return type nullable for safety
    var id = await sl<TokenManager>().getUniqueId();
    return id;
  }

  Future<void> updateToken(BuildContext context) async {
    try {
      final token = await fcmToken();
      final device = await deviceId();
      final unique = await uniqueId();

      // Ensure we have a context after the async operations complete
      if (context.mounted) {
        // Check context is valid before using it
        context
            .read<FcmUpdateCubit>() // Make sure data types match your model
            .updateFcmToken(FcmRequestModel(
              // Make sure data types match your model
              token: token ?? '', // Provide a default if null
              fcmDeviceId: device ?? '', // Provide a default if null
            ));
      } else {
        info("Context is no longer valid, cannot update token.");
      }
    } catch (e) {
      info("Error updating token: $e");
    }
  }

  @override
  Widget build(BuildContext context) {
    final textTheme = Theme.of(context).textTheme;
    final size = MediaQuery.of(context).size;
    return BlocConsumer<SignupCubit, SignupState>(listener: (context, state) {
      if (state is SignUpError) {
        SnackBarService.error(
          context: context,
          message: state.message,
        );
      }
      if (state is SignUpSuccess) {
        updateToken(context);
        // SnackBarService.info(
        //   context: context,
        //   message: "Signed in successfully.",
        // );
        context.read<ProfileLinksCubit>().getProfileLinks();
        context.replaceRoute(
            PersonaliseAccountRoute(isPersonalise: true, isInfo: true));
      }
    }, builder: (context, state) {
      var size = MediaQuery.of(context).size;
      if (state is SignUpLoading) {
        return SizedBox(
            height: size.height,
            child: const LoadingWidget(color: Colors.white));
      }

      return Scaffold(
          resizeToAvoidBottomInset: false,
          appBar: AppBar(
            toolbarHeight: 0,
            elevation: 0,
            systemOverlayStyle: const SystemUiOverlayStyle(
              statusBarColor: Colors.white,
              systemNavigationBarIconBrightness: Brightness.dark,
              statusBarBrightness: Brightness.light,
              statusBarIconBrightness: Brightness.dark,
              systemNavigationBarColor: AppColors.grey,
            ),
          ),
          body: AppSafeArea(
            child: GestureDetector(
              onTap: () {
                FocusScope.of(context).unfocus();
              },
              behavior: HitTestBehavior.translucent,
              child: Padding(
                padding: EdgeInsets.only(
                  top: isIos ? 4 : 8,
                  right: 8,
                  left: 8,
                ),
                child: Column(
                  children: [
                    AppHeader(
                      title: 'Create account',
                      onBackTap: () {
                        Navigator.pop(context);
                      },
                    ),
                    Expanded(
                      child: Container(
                        color: AppColors.navy,
                        child: Container(
                          decoration: const BoxDecoration(
                            borderRadius: BorderRadius.only(
                              topLeft: Radius.circular(30),
                              topRight: Radius.circular(30),
                            ),
                            color: AppColors.grey,
                          ),
                          child: Padding(
                            padding: const EdgeInsets.all(24),
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                const Gap(12),
                                AuthTextField(
                                  keyboardType: TextInputType.emailAddress,
                                  controller: emailController,
                                  text: 'Email',
                                  hinttext: 'Enter your email address',
                                  errorText: emailErrorText,
                                  onChanged: (val) {
                                    final cursorPosition =
                                        emailController.selection;
                                    setState(() {
                                      emailController.text = val;
                                      _isEmailValid();
                                      _validateForm();
                                    });
                                    emailController.selection = cursorPosition;
                                  },
                                ),
                                const Gap(16),
                                AuthTextField(
                                  isPassword: true,
                                  controller: passwordController,
                                  onChanged: (val) {
                                    final cursorPosition =
                                        passwordController.selection;
                                    setState(() {
                                      passwordController.text = val;
                                      check();
                                      _isPasswordValid();
                                      _validateForm();
                                    });
                                    passwordController.selection =
                                        cursorPosition;
                                  },
                                  text: 'Password',
                                  hinttext: 'Enter your password',
                                  errorText: passwordErrorText,
                                ),
                                const Gap(16),
                                PasswordChecklist(
                                  hasLetter: hasLetter,
                                  hasNumber: hasNum,
                                  hasMinCharacters: hasLength,
                                  controller: passwordController,
                                  textTheme: textTheme,
                                ),
                                const Spacer(),
                                Row(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Checkbox(
                                      value: hasConsented,
                                      fillColor: WidgetStatePropertyAll(
                                          !hasConsented
                                              ? Colors.white
                                              : AppColors.coral),
                                      activeColor: Colors.white,
                                      checkColor: Colors.white,
                                      side: const BorderSide(
                                          color: AppColors.coral),
                                      onChanged: (val) {
                                        setState(() {
                                          hasConsented = !hasConsented;
                                          _validateForm();
                                        });
                                      },
                                      visualDensity: VisualDensity.compact,
                                      materialTapTargetSize:
                                          MaterialTapTargetSize.shrinkWrap,
                                      shape: RoundedRectangleBorder(
                                          borderRadius:
                                              BorderRadius.circular(6)),
                                    ),
                                    const Gap(4),
                                    Expanded(
                                      child: RichText(
                                        text: TextSpan(
                                          children: [
                                            TextSpan(
                                              text:
                                                  "I consent to the collection, storage and use of the personal data I provide (e.g email, name, gym activity) for personalising my experience and improving app functionality as described in our ",
                                              style: textTheme.labels,
                                            ),
                                            TextSpan(
                                              text: "privacy policy",
                                              style: textTheme.linkText
                                                  .copyWith(
                                                      color: AppColors.coral),
                                              recognizer: TapGestureRecognizer()
                                                ..onTap = () {
                                                  context.pushRoute(
                                                    GotchaWebViewRoute(
                                                      url:
                                                          'https://www.gym.gotcha4life.org/privacy-policy-mfg-app/',
                                                      // "https://www.gotcha4life.org/privacy-policy",
                                                      title: 'Privacy policy',
                                                    ),
                                                  );
                                                },
                                            ),
                                            TextSpan(
                                              text: ".*",
                                              style: textTheme.linkText
                                                  .copyWith(
                                                      color: AppColors.coral),
                                            ),
                                          ],
                                        ),
                                      ),
                                    ),
                                  ],
                                ),
                                const Gap(24),
                                SizedBox(
                                  width: size.width,
                                  child: PrimaryButton(
                                    text: 'Sign up',
                                    isEnabled:
                                        isFormValid, // Enable/disable button based on form validity
                                    onPressed: () async {
                                      if (emailController.text.isEmpty ||
                                          passwordController.text.isEmpty) {
                                        SnackBarService.error(
                                          context: context,
                                          message:
                                              "Please fill required fields!",
                                        );
                                        return;
                                      }
                                      if (!_isEmailValid()) {
                                        setState(
                                            () {}); // Rebuild to show email error
                                        return;
                                      }
                                      if (!_isPasswordValid()) {
                                        setState(
                                            () {}); // Rebuild to show password error
                                        return;
                                      }
                                      if (!hasConsented) {
                                        SnackBarService.error(
                                          context: context,
                                          message:
                                              "Please accept the privacy policy.",
                                        );
                                        return;
                                      }
                                      final String currentTimeZone =
                                          await FlutterTimezone
                                              .getLocalTimezone();
                                      context.read<SignupCubit>().signUp(
                                            SignUpRequest(
                                              timezone: currentTimeZone,
                                              email: emailController.text,
                                              password: passwordController.text,
                                            ),
                                          );
                                    },
                                  ),
                                ),
                                const Gap(24),
                                Row(
                                  mainAxisAlignment: MainAxisAlignment.center,
                                  children: [
                                    Text('Already have an account?',
                                        style: textTheme.labels),
                                    const Gap(4),
                                    GestureDetector(
                                      onTap: () {
                                        context
                                            .replaceRoute(const LoginRoute());
                                        sl<MixpanelService>().trackButtonClick(
                                            'Log In',
                                            properties: {
                                              'Page': 'Sign Up Page',
                                              'Code':
                                                  'click.sign_up_page.log_in'
                                            });
                                      },
                                      child: Text(
                                        'Log in',
                                        style: textTheme.linkText.copyWith(
                                          color: AppColors.coral,
                                        ),
                                      ),
                                    ),
                                  ],
                                ),
                                isIos ? const Gap(24) : const Gap(16),
                              ],
                            ),
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ));
    });
  }

  check() {
    setState(() {
      var value = validateInput(passwordController.text);
      hasNum = value['number'];
      hasLetter = value['letter'];
      hasLength = value['totalCharacters'];
    });
  }

  Map<String, bool> validateInput(String input) {
    bool hasNumber = input.contains(RegExp(r'\d'));
    bool hasLetter = input.contains(RegExp(r'[a-zA-Z]'));
    bool hasMinLength = input.length >= 6;

    return {
      'number': hasNumber,
      'letter': hasLetter,
      'totalCharacters': hasMinLength,
    };
  }
}
