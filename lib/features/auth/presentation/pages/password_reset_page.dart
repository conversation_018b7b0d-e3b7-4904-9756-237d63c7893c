import 'package:auto_route/auto_route.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:gap/gap.dart';
import 'package:gotcha_mfg_app/config/router/app_router.gr.dart';
import 'package:gotcha_mfg_app/config/theme/app_colors.dart';
import 'package:gotcha_mfg_app/core/utils/app_print.dart';
import 'package:gotcha_mfg_app/core/utils/snackbar_service.dart';
import 'package:gotcha_mfg_app/features/auth/data/models/forgot_req.dart';
import 'package:gotcha_mfg_app/features/auth/presentation/blocs/reset/reset_password_cubit.dart';
import 'package:gotcha_mfg_app/features/auth/presentation/blocs/reset/reset_password_state.dart';
import 'package:gotcha_mfg_app/shared/widgets/app_header.dart';
import 'package:gotcha_mfg_app/shared/widgets/loading_widget.dart';
import 'package:gotcha_mfg_app/shared/widgets/primary_button.dart';

import '../../../../core/mixpanel_service.dart';
import '../../../../core/utils/platform_utils.dart';
import '../../../../locator.dart';
import '../widgets/auth_text_field.dart';

@RoutePage()
class ForgotPasswordPage extends StatefulWidget {
  const ForgotPasswordPage({super.key});

  @override
  State<ForgotPasswordPage> createState() => _ForgotPasswordPageState();
}

class _ForgotPasswordPageState extends State<ForgotPasswordPage> {
  final TextEditingController _emailController = TextEditingController();
  String? _emailError;
  bool _isButtonEnabled = false;

  @override
  void dispose() {
    _emailController.dispose();
    super.dispose();
  }

  void _validateForm() {
    setState(() {
      _emailError = _validateEmail(_emailController.text);
      _isButtonEnabled =
          _emailError == null && _emailController.text.isNotEmpty;
    });
  }

  String? _validateEmail(String? value) {
    if (value == null || value.isEmpty) {
      return 'Email is required';
    }
    if (!isValidEmail(value)) {
      return 'Please enter a valid email address';
    }
    return null;
  }

  bool isValidEmail(String email) {
    return RegExp(
            r"^[a-zA-Z0-9.a-zA-Z0-9.!#$%&'*+-/=?^_`{|}~]+@[a-zA-Z0-9]+\.[a-zA-Z]+")
        .hasMatch(email);
  }

  void _sendPasswordResetLink() {
    _validateForm();
    if (_isButtonEnabled) {
      context
          .read<ResetCubit>()
          .forgotPassword(ForgotRequestParams(id: _emailController.text ?? ''));
      // Simulate sending password reset link (replace with actual logic)
      info('Sending password reset link to: ${_emailController.text}');
      // Navigate to Login page after successful submission (optional)

      sl<MixpanelService>().trackButtonClick(
        'Send Password Reset Link',
        properties: {
          'Code': 'click.forgot_password_page.send_password_reset',
          'Page': 'Forgot Password Page',
        },
      );

      // Optionally show a success message to the user
      // SnackBarService.info(
      //   context: context,
      //   message: "Coming soon",
      // );
    } else {
      // Optionally, you can show an error snackbar if validation fails,
      // but error messages are already displayed in the text field.
      info('Validation failed. Cannot send reset link.');
    }
  }

  @override
  void initState() {
    super.initState();
    sl<MixpanelService>().trackScreenView(
      'Forgot Password Page',
      properties: {'Code': 'screen_view.forgot_password_page'},
    );
  }

  @override
  Widget build(BuildContext context) {
    final textTheme = Theme.of(context).textTheme;
    final size = MediaQuery.of(context).size;
    return BlocConsumer<ResetCubit, ResetState>(listener: (context, state) {
      if (state is ResetError) {
        SnackBarService.error(
          context: context,
          message: state.message,
        );
      }
      if (state is ResetForgotPasswordLoaded) {
        SnackBarService.info(
          context: context,
          message: "Password reset link has been sent to your email.",
        );
        context.router.replaceAll(
          [HomeRoute(index: 0)],
          updateExistingRoutes: false,
        );
      }
      // if (state is AuthLoginLoaded) {
      //   sl<MixpanelService>().setUserProperties(
      //       {'user_id': state.loginResponse.data?.userId ?? ''});
      //   saveToken(state.loginResponse.data?.accessToken ?? '',
      //       state.loginResponse.data?.refreshToken ?? '');
      //   // SnackBarService.info(
      //   //   context: context,
      //   //   message: 'Logged in.',
      //   // );
      //   context.read<ProfileLinksCubit>().getProfileLinks();
      //   context.replaceRoute(PersonaliseAccountRoute(isPersonalise: true));
      // }
    }, builder: (context, state) {
      if (state is ResetLoading) {
        return const LoadingWidget(color: Colors.white);
      }
      return Scaffold(
        appBar: AppBar(
          toolbarHeight: 0,
          elevation: 0,
          systemOverlayStyle: const SystemUiOverlayStyle(
            statusBarColor: Colors.white,
            systemNavigationBarIconBrightness: Brightness.dark,
            statusBarBrightness: Brightness.light,
            statusBarIconBrightness: Brightness.dark,
            systemNavigationBarColor: AppColors.grey,
          ),
        ),
        body: GestureDetector(
          // Add GestureDetector here
          onTap: () {
            FocusScope.of(context).unfocus(); // Dismiss keyboard on tap outside
          },
          behavior: HitTestBehavior.translucent,
          child: Padding(
            padding: EdgeInsets.only(
              top: isIos ? 4 : 8,
              right: 8,
              left: 8,
            ),
            child: Column(
              children: [
                AppHeader(
                  title: 'Reset password',
                  onBackTap: () {
                    Navigator.pop(context);
                  },
                ),
                Expanded(
                  child: Container(
                    color: AppColors.navy,
                    child: Container(
                      decoration: const BoxDecoration(
                        borderRadius: BorderRadius.only(
                          topLeft: Radius.circular(30),
                          topRight: Radius.circular(30),
                        ),
                        color: AppColors.grey,
                      ),
                      child: Padding(
                        padding: const EdgeInsets.all(24),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            const Gap(12),
                            AuthTextField(
                              controller: _emailController,
                              keyboardType: TextInputType.emailAddress,
                              text: 'Email',
                              hinttext: 'Enter your email address',
                              errorText: _emailError,
                              onChanged: (value) {
                                _validateForm();
                              },
                            ),
                            const Spacer(),
                          ],
                        ),
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
        floatingActionButtonLocation: FloatingActionButtonLocation.centerFloat,
        floatingActionButton: Padding(
          padding: const EdgeInsets.fromLTRB(32, 0, 32, 8),
          child: SizedBox(
            width: size.width - 64,
            child: PrimaryButton(
              text: 'Send password reset link',
              isEnabled: _isButtonEnabled,
              onPressed: _sendPasswordResetLink,
            ),
          ),
        ),
      );
    });
  }
}
