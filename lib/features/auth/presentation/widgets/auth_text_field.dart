// import 'package:flutter/material.dart';
// import 'package:gotcha_mfg_app/config/theme/app_colors.dart';
// import 'package:gotcha_mfg_app/core/extensions/app_text_theme_extension.dart';

// // class AuthTextField extends StatefulWidget {
// //   final String text;
// //   final String hinttext;
// //   final TextEditingController controller;
// //   final ValueChanged<String>? onChanged;
// //   final bool? isPassword;
// //   final String? errorText;
// //   final bool isEditable; // New parameter

// //   const AuthTextField({
// //     required this.controller,
// //     super.key,
// //     required this.text,
// //     required this.hinttext,
// //     this.onChanged,
// //     this.isPassword = false,
// //     this.errorText,
// //     this.isEditable = true, // Default to true
// //   });

// //   @override
// //   State<AuthTextField> createState() => _AuthTextFieldState();
// // }

// // class _AuthTextFieldState extends State<AuthTextField> {
// //   bool _obscureText = true;

// //   @override
// //   Widget build(BuildContext context) {
// //     final textTheme = Theme.of(context).textTheme;

// //     return Column(
// //       crossAxisAlignment: CrossAxisAlignment.start,
// //       children: [
// //         Text(
// //           widget.text,
// //           style: textTheme.ralewayRegular.copyWith(
// //             fontSize: 17,
// //             color: AppColors.navy,
// //           ),
// //         ),
// //         const SizedBox(height: 8.0),
// //         TextField(
// //           controller: widget.controller,
// //           onChanged: widget.onChanged,
// //           obscureText: widget.isPassword == true ? _obscureText : false,
// //           style: textTheme.ralewayMedium.copyWith(
// //             fontSize: 13,
// //             color: AppColors.navy,
// //           ),
// //           enabled: widget.isEditable, // Set editable state
// //           decoration: InputDecoration(
// //             hintText: widget.hinttext,
// //             hintStyle: textTheme.ralewayLight.copyWith(
// //               fontSize: 13,
// //               color: AppColors.navy,
// //             ),
// //             errorText: widget.errorText,
// //             errorStyle: textTheme.ralewayMedium.copyWith(
// //               fontSize: 12,
// //               color: Colors.red,
// //             ),
// //             contentPadding: const EdgeInsets.symmetric(
// //               horizontal: 16.0,
// //               vertical: 12.0,
// //             ),
// //             enabledBorder: OutlineInputBorder(
// //               borderRadius: BorderRadius.circular(12),
// //               borderSide: const BorderSide(
// //                 color: AppColors.midBlue,
// //                 width: 1.0,
// //               ),
// //             ),
// //             focusedBorder: OutlineInputBorder(
// //               borderRadius: BorderRadius.circular(12),
// //               borderSide: const BorderSide(
// //                 color: AppColors.midBlue,
// //                 width: 1.0,
// //               ),
// //             ),
// //             errorBorder: OutlineInputBorder(
// //               borderRadius: BorderRadius.circular(12),
// //               borderSide: const BorderSide(
// //                 color: Colors.red,
// //                 width: 1.0,
// //               ),
// //             ),
// //             focusedErrorBorder: OutlineInputBorder(
// //               borderRadius: BorderRadius.circular(12),
// //               borderSide: const BorderSide(
// //                 color: Colors.red,
// //                 width: 1.0,
// //               ),
// //             ),
// //             suffixIcon: widget.isPassword == true
// //                 ? IconButton(
// //                     icon: Icon(
// //                       _obscureText ? Icons.visibility : Icons.visibility_off,
// //                       color: Colors.grey,
// //                       size: 18,
// //                     ),
// //                     onPressed: () {
// //                       setState(() {
// //                         _obscureText = !_obscureText;
// //                       });
// //                     },
// //                   )
// //                 : null,
// //             filled: true,
// //             fillColor: Colors.white,
// //           ),
// //         ),
// //       ],
// //     );
// //   }
// // }
// class AuthTextField extends StatefulWidget {
//   final String text;
//   final String hinttext;
//   final TextEditingController controller;
//   final ValueChanged<String>? onChanged;
//   final bool? isPassword;
//   final String? errorText;
//   final bool isEditable;
//   final TextInputType? keyboardType;

//   const AuthTextField({
//     required this.controller,
//     super.key,
//     required this.text,
//     required this.hinttext,
//     this.onChanged,
//     this.isPassword = false,
//     this.errorText,
//     this.isEditable = true,
//     this.keyboardType,
//   });

//   @override
//   State<AuthTextField> createState() => _AuthTextFieldState();
// }

// class _AuthTextFieldState extends State<AuthTextField> {
//   bool _obscureText = true;

//   @override
//   Widget build(BuildContext context) {
//     final textTheme = Theme.of(context).textTheme;

//     return Column(
//       crossAxisAlignment: CrossAxisAlignment.start,
//       children: [
//         Text(widget.text, style: textTheme.bodyRegular),
//         const SizedBox(height: 8.0),
//         TextField(
//           controller: widget.controller,
//           onChanged: widget.onChanged,
//           obscureText: widget.isPassword == true ? _obscureText : false,
//           keyboardType: widget.keyboardType, // Add keyboard type
//           style: textTheme.placeholder.copyWith(),
//           enabled: widget.isEditable,
//           decoration: InputDecoration(
//             hintText: widget.hinttext,
//             hintStyle: textTheme.ralewayLight.copyWith(
//               fontSize: 13,
//               color: AppColors.navy,
//             ),
//             errorText: widget.errorText,
//             errorStyle: textTheme.ralewayMedium.copyWith(
//               fontSize: 12,
//               color: Colors.red,
//             ),
//             contentPadding: const EdgeInsets.symmetric(
//               horizontal: 16.0,
//               vertical: 12.0,
//             ),
//             enabledBorder: OutlineInputBorder(
//               borderRadius: BorderRadius.circular(12),
//               borderSide: const BorderSide(
//                 color: AppColors.lightBlue,
//                 width: 1.5,
//               ),
//             ),
//             focusedBorder: OutlineInputBorder(
//               borderRadius: BorderRadius.circular(12),
//               borderSide: const BorderSide(
//                 color: AppColors.midBlue,
//                 width: 1.0,
//               ),
//             ),
//             errorBorder: OutlineInputBorder(
//               borderRadius: BorderRadius.circular(12),
//               borderSide: const BorderSide(
//                 color: Colors.red,
//                 width: 1.0,
//               ),
//             ),
//             focusedErrorBorder: OutlineInputBorder(
//               borderRadius: BorderRadius.circular(12),
//               borderSide: const BorderSide(
//                 color: Colors.red,
//                 width: 1.0,
//               ),
//             ),
//             suffixIcon: widget.isPassword == true
//                 ? IconButton(
//                     icon: Icon(
//                       _obscureText ? Icons.visibility : Icons.visibility_off,
//                       color: Colors.grey,
//                       size: 18,
//                     ),
//                     onPressed: () {
//                       setState(() {
//                         _obscureText = !_obscureText;
//                       });
//                     },
//                   )
//                 : null,
//             filled: true,
//             fillColor: Colors.white,
//           ),
//         ),
//       ],
//     );
//   }
// }

import 'package:flutter/material.dart';
import 'package:gotcha_mfg_app/config/theme/app_colors.dart';
import 'package:gotcha_mfg_app/core/extensions/app_text_theme_extension.dart';

class AuthTextField extends StatefulWidget {
  final String text;
  final String hinttext;
  final TextEditingController controller;
  final ValueChanged<String>? onChanged;
  final bool? isPassword;
  final String? errorText;
  final bool isEditable;
  final TextInputType? keyboardType;

  const AuthTextField({
    required this.controller,
    super.key,
    required this.text,
    required this.hinttext,
    this.onChanged,
    this.isPassword = false,
    this.errorText,
    this.isEditable = true,
    this.keyboardType,
  });

  @override
  State<AuthTextField> createState() => _AuthTextFieldState();
}

class _AuthTextFieldState extends State<AuthTextField> {
  bool _obscureText = true;
  bool _hasText = false; // Track if text has been entered
  @override
  void initState() {
    super.initState();
    _hasText = widget.controller.text.isNotEmpty;
  }

  @override
  Widget build(BuildContext context) {
    final textTheme = Theme.of(context).textTheme;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(widget.text, style: textTheme.bodyRegular),
        const SizedBox(height: 8.0),
        TextField(
          controller: widget.controller,
          onChanged: (value) {
            setState(() {
              _hasText = value.isNotEmpty;
            });
            if (widget.onChanged != null) {
              widget.onChanged!(value);
            }
          },
          obscureText: widget.isPassword == true ? _obscureText : false,
          keyboardType: widget.keyboardType,
          style: textTheme.placeholder.copyWith(
            color: _hasText ? AppColors.navy : AppColors.midBlue,
          ),
          enabled: widget.isEditable,
          decoration: InputDecoration(
            hintText: widget.hinttext,
            hintStyle: textTheme.ralewayLight.copyWith(
              fontSize: 13,
              color: AppColors.midBlue,
            ),
            errorText: widget.errorText,
            errorStyle: textTheme.ralewayMedium.copyWith(
              fontSize: 12,
              color: Colors.red,
            ),
            contentPadding: const EdgeInsets.symmetric(
              horizontal: 16.0,
              vertical: 12.0,
            ),
            enabledBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide: const BorderSide(
                color: AppColors.lightBlue,
                width: 1.5,
              ),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide: const BorderSide(
                color: AppColors.lightBlue,
                width: 1.0,
              ),
            ),
            errorBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide: const BorderSide(
                color: Colors.red,
                width: 1.0,
              ),
            ),
            focusedErrorBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide: const BorderSide(
                color: Colors.red,
                width: 1.0,
              ),
            ),
            suffixIcon: widget.isPassword == true
                ? IconButton(
                    icon: Icon(
                      _obscureText ? Icons.visibility_off : Icons.visibility,
                      color: Colors.grey,
                      size: 18,
                    ),
                    onPressed: () {
                      setState(() {
                        _obscureText = !_obscureText;
                      });
                    },
                  )
                : null,
            filled: true,
            fillColor: Colors.white,
          ),
        ),
      ],
    );
  }
}
