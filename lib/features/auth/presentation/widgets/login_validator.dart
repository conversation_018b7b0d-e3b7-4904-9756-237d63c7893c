import 'package:flutter/material.dart';
import 'package:gotcha_mfg_app/core/extensions/app_text_theme_extension.dart';

import '../../../../../config/theme/app_colors.dart';

class PasswordChecklist extends StatefulWidget {
  final TextTheme textTheme;
  final TextEditingController controller;
  final bool? hasNumber;
  final bool? hasLetter;
  final bool? hasMinCharacters;

  const PasswordChecklist({
    super.key,
    required this.textTheme,
    required this.controller,
    required this.hasNumber,
    required this.hasLetter,
    required this.hasMinCharacters,
  });

  @override
  State<PasswordChecklist> createState() => _PasswordChecklistState();
}

class _PasswordChecklistState extends State<PasswordChecklist> {
  Widget _buildChecklistItem(String text, bool? isChecked) {
    return Row(
      children: [
        isChecked == true
            ? const Icon(
                Icons.check_circle,
                color: AppColors.navy,
                size: 20,
              )
            : const Icon(
                Icons.check_circle_outline,
                color: AppColors.midBlue,
                size: 20,
              ),
        const SizedBox(width: 8),
        Text(
          text,
          style: widget.textTheme.labels.copyWith(
            decorationColor:
                isChecked == true ? AppColors.navy : AppColors.midBlue,
            color: isChecked == true ? AppColors.navy : AppColors.midBlue,
          ),
        ),
      ],
    );
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildChecklistItem("1 letter", widget.hasLetter),
        const SizedBox(height: 8),
        _buildChecklistItem("1 number", widget.hasNumber),
        const SizedBox(height: 8),
        _buildChecklistItem("6 characters", widget.hasMinCharacters),
      ],
    );
  }
}
