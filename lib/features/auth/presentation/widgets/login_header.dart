import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:gotcha_mfg_app/config/theme/app_colors.dart';
import 'package:gotcha_mfg_app/core/extensions/app_text_theme_extension.dart';

class LoginHeader extends StatelessWidget {
  const LoginHeader(
      {required this.textTheme,
      required this.onTap,
      super.key,
      required this.title,
      required this.subTitle,
      required this.backEnabled,
      required this.subEnabled});

  final TextTheme textTheme;
  final VoidCallback onTap;
  final String title;
  final String subTitle;
  final bool backEnabled;
  final bool subEnabled;

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.symmetric(
        horizontal: 20,
        vertical: 24,
      ),
      decoration: const BoxDecoration(
        color: AppColors.navy,
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(30),
          topRight: Radius.circular(30),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          backEnabled
              ? Row(
                  children: [
                    const Icon(Icons.arrow_back, color: Colors.white),
                    const Gap(10),
                    Text(
                      title,
                      style: textTheme.gothamBold.copyWith(
                        fontSize: 20,
                        color: Colors.white,
                      ),
                    ),
                  ],
                )
              : Text(
                  title,
                  style: textTheme.gothamBold.copyWith(
                    fontSize: 20,
                    color: Colors.white,
                  ),
                ),
          subEnabled
              ? Text(
                  subTitle,
                  style: textTheme.ralewayRegular.copyWith(
                    color: Colors.white,
                    fontSize: 12,
                  ),
                )
              : const SizedBox(),
        ],
      ),
    );
  }
}
