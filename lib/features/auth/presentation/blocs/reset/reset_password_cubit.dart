// feature_cubit.dart
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:gotcha_mfg_app/features/auth/data/models/forgot_req.dart';
import 'package:gotcha_mfg_app/features/auth/domain/usecases/forgot_password_usecase.dart';
import 'package:gotcha_mfg_app/features/auth/presentation/blocs/reset/reset_password_state.dart';

/// Auth Cubit
class ResetCubit extends Cubit<ResetState> {
  /// Constructor
  ResetCubit(
    this._forgotPasswordUseCase,
  ) : super(ResetLoaded());

  final ForgotPasswordUseCase _forgotPasswordUseCase;

  /// Forgot Password
  Future<void> forgotPassword(ForgotRequestParams request) async {
    emit(ResetLoading());
    final result = await _forgotPasswordUseCase.call(request);
    if (result.isSuccess) {
      emit(ResetForgotPasswordLoaded(result.data!));
      emit(ResetLoaded());
    } else {
      emit(ResetError(result.data?.message ?? 'Reset password failed.'));
      emit(ResetLoaded());
    }
  }
}
