// feature_state.dart

import 'package:gotcha_mfg_app/features/auth/data/models/common_response.dart';

abstract class ResetState {}

// class AuthInitial extends ResetState {}

class ResetLoading extends ResetState {}

class ResetLoaded extends ResetState {}

class ResetForgotPasswordLoaded extends ResetState {
  ResetForgotPasswordLoaded(this.forgotResponse);
  final CommonResponse forgotResponse;
}

class ResetError extends ResetState {
  ResetError(this.message);
  final String message;
}
