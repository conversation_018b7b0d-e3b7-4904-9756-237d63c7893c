import 'package:equatable/equatable.dart';
import 'package:gotcha_mfg_app/features/auth/data/models/common_response.dart';

import '../../../data/models/login_model.dart';

sealed class AuthState extends Equatable {
  const AuthState();

  @override
  List<Object> get props => [];
}

final class AuthInitial extends AuthState {}

final class AuthLoginLoaded extends AuthState {
  final LoginResponse loginResponse;
  const AuthLoginLoaded(this.loginResponse);
}

final class AuthLoginError extends AuthState {
  final String message;
  const AuthLoginError(this.message);
}

final class AuthLoading extends AuthState {}

final class AuthDeleteAccountLoading extends AuthState {}

final class AuthDeleteAccountSuccess extends AuthState {
  final CommonResponse commonResponse;
  const AuthDeleteAccountSuccess(this.commonResponse);
}

final class AuthDeleteAccountError extends AuthState {
  final String message;
  const AuthDeleteAccountError(this.message);
}
