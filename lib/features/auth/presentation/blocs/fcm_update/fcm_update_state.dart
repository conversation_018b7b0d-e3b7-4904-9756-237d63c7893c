part of 'fcm_update_cubit.dart';

sealed class FcmUpdateState extends Equatable {
  const FcmUpdateState();

  @override
  List<Object> get props => [];
}

final class FcmUpdateInitial extends FcmUpdateState {}

final class FcmUpdateLoading extends FcmUpdateState {}

final class FcmUpdateSuc<PERSON> extends FcmUpdateState {}

final class FcmUpdateError extends FcmUpdateState {
  final String message;

  const FcmUpdateError(this.message);

  @override
  List<Object> get props => [message];
}
