import 'package:bloc/bloc.dart';
import 'package:equatable/equatable.dart';
import 'package:gotcha_mfg_app/features/auth/data/models/fcm_req_model.dart';

import '../../../domain/usecases/update_fcm_usecase.dart';

part 'fcm_update_state.dart';

class FcmUpdateCubit extends Cubit<FcmUpdateState> {
  FcmUpdateCubit(this._updateFcmTokenUseCase) : super(FcmUpdateInitial());
  final UpdateFcmTokenUseCase _updateFcmTokenUseCase;

  /// Update FCM Token
  Future<void> updateFcmToken(FcmRequestModel request) async {
    final result = await _updateFcmTokenUseCase.call(request);
    if (result.isSuccess) {
    } else {}
  }
}
