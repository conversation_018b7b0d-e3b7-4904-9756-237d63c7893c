import 'package:bloc/bloc.dart';
import 'package:equatable/equatable.dart';

import '../../../data/models/sign_up_model.dart';
import '../../../data/models/sign_up_request_model.dart';
import '../../../domain/usecases/sign_up_usecase.dart';

part 'signup_state.dart';

class SignupCubit extends Cubit<SignupState> {
  final SignUpUseCase _signUpUseCase;

  SignupCubit(this._signUpUseCase) : super(SignUpInitial());
  Future<void> signUp(SignUpRequest request) async {
    emit(SignUpLoading());
    final result = await _signUpUseCase.call(request);
    if (result.isSuccess) {
      emit(SignUpSuccess(result.data!));
    } else {
      emit(SignUpError(result.error!));
    }
  }
}
