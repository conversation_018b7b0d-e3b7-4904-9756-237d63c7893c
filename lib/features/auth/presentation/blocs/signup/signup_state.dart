part of 'signup_cubit.dart';

sealed class SignupState extends Equatable {
  const SignupState();

  @override
  List<Object> get props => [];
}

final class SignUpInitial extends SignupState {}

final class SignUpSuccess extends SignupState {
  final SignUpResponse signResponse;
  const SignUpSuccess(this.signResponse);
}

final class SignUpError extends SignupState {
  final String message;
  const SignUpError(this.message);
}

final class SignUpLoading extends SignupState {}
