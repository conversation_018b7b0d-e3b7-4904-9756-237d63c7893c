import 'dart:convert';

class LoginResponse {
  final String? message;
  final String? status;
  final Data? data;

  LoginResponse({
    this.message,
    this.status,
    this.data,
  });

  LoginResponse copyWith({
    String? message,
    String? status,
    Data? data,
  }) =>
      LoginResponse(
        message: message ?? this.message,
        status: status ?? this.status,
        data: data ?? this.data,
      );

  factory LoginResponse.fromRawJson(String str) =>
      LoginResponse.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory LoginResponse.fromJson(Map<String, dynamic> json) => LoginResponse(
        message: json["message"],
        status: json["status"],
        data: json["data"] == null ? null : Data.fromJson(json["data"]),
      );

  Map<String, dynamic> toJson() => {
        "message": message,
        "status": status,
        "data": data?.toJson(),
      };
}

class Data {
  final String? accessToken;
  final String? refreshToken;
  final String? userId;
  final String? deviceId;

  Data({
    this.accessToken,
    this.refreshToken,
    this.userId,
    this.deviceId,
  });

  Data copyWith({
    String? accessToken,
    String? refreshToken,
    String? userId,
    String? deviceId,
  }) =>
      Data(
        accessToken: accessToken ?? this.accessToken,
        refreshToken: refreshToken ?? this.refreshToken,
        userId: userId ?? this.userId,
        deviceId: deviceId ?? this.deviceId,
      );

  factory Data.fromRawJson(String str) => Data.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory Data.fromJson(Map<String, dynamic> json) => Data(
        accessToken: json["access_token"],
        refreshToken: json["refresh_token"],
        userId: json["user_id"],
        deviceId: json["device_id"],
      );

  Map<String, dynamic> toJson() => {
        "access_token": accessToken,
        "refresh_token": refreshToken,
        "user_id": userId,
        "device_id": deviceId,
      };
}
