/// Sign in request model
class SignUpRequest {
  /// Constructor
  SignUpRequest({
    required this.timezone,
    required this.email,
    required this.password,
  });

  /// Email
  final String email;

  /// Password
  final String password;
  final String timezone;

  /// Convert to JSON
  Map<String, dynamic> to<PERSON><PERSON>() {
    return {
      'email': email,
      'password': password,
      "time_zone": timezone,
    };
  }
}
