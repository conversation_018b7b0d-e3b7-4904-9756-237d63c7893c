class LoginRequest {
  /// Constructor
  LoginRequest({
    required this.username,
    required this.password,
    required this.timezone,
  });

  /// Email
  final String username;

  /// Password
  final String password;

  /// Timezone
  final String timezone;

  /// Convert to JSON
  Map<String, dynamic> to<PERSON><PERSON>() {
    return {
      'username': username,
      'password': password,
      "time_zone": timezone,
    };
  }
}
