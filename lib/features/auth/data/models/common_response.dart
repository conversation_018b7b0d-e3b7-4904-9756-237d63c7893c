// response/village_user_response.dart
class CommonResponse {
  CommonResponse({
    required this.message,
    required this.status,
    this.data,
  });

  final String message;
  final String status;
  final dynamic data;

  factory CommonResponse.fromJson(Map<String, dynamic> json) => CommonResponse(
        message: json["message"],
        status: json["status"],
        data: json["data"],
      );

  Map<String, dynamic> toJson() => {
        "message": message,
        "status": status,
        "data": data,
      };
}
