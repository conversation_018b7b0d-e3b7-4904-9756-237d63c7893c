import 'dart:convert';

class SignUpResponse {
  final String? message;
  final String? status;

  SignUpResponse({
    this.message,
    this.status,
  });

  SignUpResponse copyWith({
    String? message,
    String? status,
  }) =>
      SignUpResponse(
        message: message ?? this.message,
        status: status ?? this.status,
      );

  factory SignUpResponse.fromRawJson(String str) =>
      SignUpResponse.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory SignUpResponse.fromJson(Map<String, dynamic> json) => SignUpResponse(
        message: json["message"],
        status: json["status"],
      );

  Map<String, dynamic> toJson() => {
        "message": message,
        "status": status,
      };
}
