class FcmRequestModel {
  final String fcmDeviceId;
  final String token;

  FcmRequestModel({
    required this.fcmDeviceId,
    required this.token,
  });

  // Convert model to JSON
  Map<String, dynamic> toJson() {
    return {
      'fcm_device_id': fcmDeviceId,
      'token': token,
    };
  }

  // Create model from JSON
  factory FcmRequestModel.fromJson(Map<String, dynamic> json) {
    return FcmRequestModel(
      fcmDeviceId: json['fcm_device_id'] ?? '',
      token: json['token'] ?? '',
    );
  }

  // Copy with method for immutability
  FcmRequestModel copyWith({
    String? fcmDeviceId,
    String? token,
  }) {
    return FcmRequestModel(
      fcmDeviceId: fcmDeviceId ?? this.fcmDeviceId,
      token: token ?? this.token,
    );
  }

  @override
  String toString() {
    return 'FcmRequestModel(fcmDeviceId: $fcmDeviceId, token: $token)';
  }
}
