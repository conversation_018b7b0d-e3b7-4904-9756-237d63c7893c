class ForgotRequestParams {
  final String? id;

  ForgotRequestParams({required this.id});

  // Factory method to create a ForgotRequestParams instance from JSON
  factory ForgotRequestParams.fromJson(Map<String, dynamic> json) {
    return ForgotRequestParams(
      id: json['id'],
    );
  }

  // Method to convert the ForgotRequestParams instance to JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
    };
  }
}
