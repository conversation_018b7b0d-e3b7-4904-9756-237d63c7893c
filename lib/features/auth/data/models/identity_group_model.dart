import 'dart:convert';

class IdentityGroupResponse {
  final String? message;
  final String? status;
  final List<Datum>? data;

  IdentityGroupResponse({
    this.message,
    this.status,
    this.data,
  });

  IdentityGroupResponse copyWith({
    String? message,
    String? status,
    List<Datum>? data,
  }) =>
      IdentityGroupResponse(
        message: message ?? this.message,
        status: status ?? this.status,
        data: data ?? this.data,
      );

  factory IdentityGroupResponse.fromRawJson(String str) =>
      IdentityGroupResponse.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory IdentityGroupResponse.fromJson(Map<String, dynamic> json) =>
      IdentityGroupResponse(
        message: json["message"],
        status: json["status"],
        data: json["data"] == null
            ? []
            : List<Datum>.from(json["data"]!.map((x) => Datum.fromJson(x))),
      );

  Map<String, dynamic> toJson() => {
        "message": message,
        "status": status,
        "data": data == null
            ? []
            : List<dynamic>.from(data!.map((x) => x.toJson())),
      };
}

class Datum {
  final String? id;
  final String? name;
  final String? type;
  final int? point;
  final String? description;
  final DateTime? createdAt;
  final DateTime? updatedAt;

  Datum({
    this.id,
    this.name,
    this.type,
    this.point,
    this.description,
    this.createdAt,
    this.updatedAt,
  });

  Datum copyWith({
    String? id,
    String? name,
    String? type,
    int? point,
    String? description,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) =>
      Datum(
        id: id ?? this.id,
        name: name ?? this.name,
        type: type ?? this.type,
        point: point ?? this.point,
        description: description ?? this.description,
        createdAt: createdAt ?? this.createdAt,
        updatedAt: updatedAt ?? this.updatedAt,
      );

  factory Datum.fromRawJson(String str) => Datum.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory Datum.fromJson(Map<String, dynamic> json) => Datum(
        id: json["id"],
        name: json["name"],
        type: json["type"],
        point: json["point"],
        description: json["description"],
        createdAt: json["created_at"] == null
            ? null
            : DateTime.parse(json["created_at"]),
        updatedAt: json["updated_at"] == null
            ? null
            : DateTime.parse(json["updated_at"]),
      );

  Map<String, dynamic> toJson() => {
        "id": id,
        "name": name,
        "type": type,
        "point": point,
        "description": description,
        "created_at": createdAt?.toIso8601String(),
        "updated_at": updatedAt?.toIso8601String(),
      };
}
