import 'package:dio/dio.dart';
import 'package:firebase_crashlytics/firebase_crashlytics.dart';
import 'package:gotcha_mfg_app/features/auth/data/models/common_response.dart';
import 'package:gotcha_mfg_app/features/auth/data/models/fcm_req_model.dart';
import 'package:gotcha_mfg_app/features/auth/data/models/forgot_req.dart';
import 'package:gotcha_mfg_app/locator.dart';

import '../../../../core/storage/token_manager.dart';
import '../../../../shared/models/result.dart';
import '../models/identity_group_model.dart';
import '../models/login_model.dart';
import '../models/login_request_model.dart';
import '../models/sign_up_model.dart';
import '../models/sign_up_request_model.dart';

/// Interface for auth remote data source
abstract class AuthRemoteDataSource {
  /// Login
  Future<Result<LoginResponse>> login(LoginRequest request);
  Future<Result<SignUpResponse>> signUp(SignUpRequest request);
  Future<Result<IdentityGroupResponse>> getIdentityGroups();
  Future<Result<CommonResponse>> forgotPassword(ForgotRequestParams request);
  Future<Result<CommonResponse>> deleteAccount();
  Future<Result<CommonResponse>> updateFcmToken(FcmRequestModel request);
}

/// Auth remote data source implementation
class AuthRemoteDataSourceImpl implements AuthRemoteDataSource {
  /// Constructor
  AuthRemoteDataSourceImpl(this._dio);

  final Dio _dio;
  @override
  Future<Result<SignUpResponse>> signUp(SignUpRequest request) async {
    try {
      final response = await _dio.post('/app/user', data: request,
          options: Options(validateStatus: (status) {
        return status! < 500;
      }));
      if (response.statusCode == 200) {
        final data = response.data;
        final signInResponse = SignUpResponse.fromJson(data);

        return Result.success(signInResponse);
      } else {
        return Result.failure(
            '${response.statusCode} - ${response.data['message']}');
      }
    } catch (e) {
      FirebaseCrashlytics.instance.recordError(e, StackTrace.current);
      return Result.failure(
          'An error occurred during sign up: ${e.toString()}');
    }
  }

  @override
  Future<Result<LoginResponse>> login(LoginRequest request) async {
    try {
      final response =
          await _dio.post('/app/auth/sign-in', data: request, options: Options(
        validateStatus: (status) {
          return status! < 500;
        },
      ));
      if (response.statusCode == 200) {
        final data = response.data;
        final loginResponse = LoginResponse.fromJson(data);

        return Result.success(loginResponse);
      } else {
        return Result.failure(
            '${response.statusCode} - ${response.data['message']}');
      }
    } catch (e) {
      FirebaseCrashlytics.instance.recordError(e, StackTrace.current);

      return Result.failure('An error occurred during login: ${e.toString()}');
    }
  }

  @override
  Future<Result<IdentityGroupResponse>> getIdentityGroups() async {
    try {
      final response = await _dio.get('/app/user/demographics');
      if (response.statusCode == 200) {
        final data = response.data;
        final identityGroupResponse = IdentityGroupResponse.fromJson(data);
        return Result.success(identityGroupResponse);
      } else {
        return Result.failure(
            '${response.statusCode} - ${response.data['message']}');
      }
    } catch (e) {
      FirebaseCrashlytics.instance.recordError(e, StackTrace.current);
      return Result.failure(
          'An error occurred during getting identity groups: ${e.toString()}');
    }
  }

  @override
  Future<Result<CommonResponse>> forgotPassword(
      ForgotRequestParams request) async {
    try {
      final response = await _dio.get(
        '/app/user/forgot-password/${request.id}',
      );
      if (response.statusCode == 200) {
        final data = response.data;
        final forgotResponse = CommonResponse.fromJson(data);
        return Result.success(forgotResponse);
      } else {
        return Result.failure(
            '${response.statusCode} - ${response.data['message']}');
      }
    } catch (e) {
      return Result.failure(
          'An error occurred during forgot password: ${e.toString()}');
    }
  }

  @override
  Future<Result<CommonResponse>> deleteAccount() async {
    var uniqueId = await sl<TokenManager>().getAccessToken();

    try {
      final response = await _dio.delete(
        '/app/user',
        options: Options(
          headers: {
            'Authorization': 'Bearer $uniqueId', // Add your auth token here
          },
        ),
      );
      if (response.statusCode == 200) {
        final data = response.data;
        final logoutResponse = CommonResponse.fromJson(data);

        return Result.success(logoutResponse);
      } else {
        return Result.failure(
            '${response.statusCode} - ${response.data['message']}');
      }
    } catch (e) {
      return Result.failure(
          'An error occurred during calling logout: ${e.toString()}');
    }
  }

  @override
  Future<Result<CommonResponse>> updateFcmToken(FcmRequestModel request) async {
    try {
      final response = await _dio.put(
        '/app/notification/token/fcm',
        data: request.toJson(),
      );

      if (response.statusCode == 200) {
        final data = response.data;
        final model = CommonResponse.fromJson(data);

        return Result.success(model);
      } else {
        return Result.failure(
            '${response.statusCode} - ${response.data['message']}');
      }
    } catch (e) {
      return Result.failure(
          'An error occurred during calling updateFcmToken: ${e.toString()}');
    }
  }
}
