import 'package:gotcha_mfg_app/features/auth/data/models/fcm_req_model.dart';
import 'package:gotcha_mfg_app/features/auth/data/models/forgot_req.dart';

import '../../../../shared/models/result.dart';
import '../../domain/repositories/auth_repository.dart';
import '../data_sources/auth_remote_data_source.dart';
import '../models/common_response.dart';
import '../models/identity_group_model.dart';
import '../models/login_model.dart';
import '../models/login_request_model.dart';
import '../models/sign_up_model.dart';
import '../models/sign_up_request_model.dart';

class AuthRepositoryImpl implements AuthRepository {
  AuthRepositoryImpl(this._remoteDataSource);

  final AuthRemoteDataSource _remoteDataSource;

  @override
  Future<Result<LoginResponse>> login(LoginRequest request) async {
    return await _remoteDataSource.login(request);
  }

  @override
  Future<Result<SignUpResponse>> signIn(SignUpRequest request) async {
    return await _remoteDataSource.signUp(request);
  }

  @override
  Future<Result<IdentityGroupResponse>> getIdentityGroups() async {
    return await _remoteDataSource.getIdentityGroups();
  }

  @override
  Future<Result<CommonResponse>> forgotPassword(
      ForgotRequestParams request) async {
    return await _remoteDataSource.forgotPassword(request);
  }

  @override
  Future<Result<CommonResponse>> deleteAccount() async {
    return await _remoteDataSource.deleteAccount();
  }

  @override
  Future<Result<CommonResponse>> updateFcmToken(FcmRequestModel request) async {
    return await _remoteDataSource.updateFcmToken(request);
  }
}
