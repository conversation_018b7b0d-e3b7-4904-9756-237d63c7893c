import 'package:gotcha_mfg_app/core/usecase/usecase.dart';
import 'package:gotcha_mfg_app/features/auth/data/models/common_response.dart';
import 'package:gotcha_mfg_app/features/auth/data/models/fcm_req_model.dart';
import 'package:gotcha_mfg_app/shared/models/result.dart';

import '../repositories/auth_repository.dart';

/// UseCase for Update FCM Token
class UpdateFcmTokenUseCase
    implements UseCase<Result<CommonResponse>, FcmRequestModel> {
  /// Constructor
  UpdateFcmTokenUseCase(this._repository);

  final AuthRepository _repository;

  @override
  Future<Result<CommonResponse>> call(FcmRequestModel request) async {
    return _repository.updateFcmToken(request);
  }
}
