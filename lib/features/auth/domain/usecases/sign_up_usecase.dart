import '../../../../core/usecase/usecase.dart';
import '../../../../shared/models/result.dart';
import '../../data/models/sign_up_model.dart';
import '../../data/models/sign_up_request_model.dart';
import '../repositories/auth_repository.dart';

class SignUpUseCase implements UseCase<Result<SignUpResponse>, SignUpRequest> {
  /// Constructor
  SignUpUseCase(this._repository);

  final AuthRepository _repository;

  @override
  Future<Result<SignUpResponse>> call(SignUpRequest request) async {
    return _repository.signIn(request);
  }
}
