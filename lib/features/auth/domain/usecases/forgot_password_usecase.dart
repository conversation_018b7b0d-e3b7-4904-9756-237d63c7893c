// api_call_name_use_case.dart
import 'package:gotcha_mfg_app/core/usecase/usecase.dart';
import 'package:gotcha_mfg_app/features/auth/domain/repositories/auth_repository.dart';
import 'package:gotcha_mfg_app/shared/models/result.dart'; // Adjust import path

import '../../data/models/common_response.dart';
import '../../data/models/forgot_req.dart';

/// UseCase for forgotPassword
class ForgotPasswordUseCase
    implements UseCase<Result<CommonResponse>, ForgotRequestParams> {
  /// Constructor
  ForgotPasswordUseCase(this._repository);

  final AuthRepository _repository;

  @override
  Future<Result<CommonResponse>> call(ForgotRequestParams request) async {
    return _repository.forgotPassword(request);
  }
}
