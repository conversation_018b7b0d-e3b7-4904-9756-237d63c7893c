import '../../../../core/usecase/usecase.dart';
import '../../../../shared/models/result.dart';
import '../../data/models/identity_group_model.dart';
import '../repositories/auth_repository.dart';

class GetIdentityGroupsUseCase
    implements UseCase<Result<IdentityGroupResponse>, NoParams> {
  /// Constructor
  GetIdentityGroupsUseCase(this._repository);

  final AuthRepository _repository;

  @override
  Future<Result<IdentityGroupResponse>> call(NoParams noParams) async {
    return _repository.getIdentityGroups();
  }
}
