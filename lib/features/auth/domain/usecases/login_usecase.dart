import '../../../../core/usecase/usecase.dart';
import '../../../../shared/models/result.dart';
import '../../data/models/login_model.dart';
import '../../data/models/login_request_model.dart';
import '../repositories/auth_repository.dart';

class LoginUseCase implements UseCase<Result<LoginResponse>, LoginRequest> {
  /// Constructor
  LoginUseCase(this._repository);

  final AuthRepository _repository;

  @override
  Future<Result<LoginResponse>> call(LoginRequest request) async {
    return _repository.login(request);
  }
}
