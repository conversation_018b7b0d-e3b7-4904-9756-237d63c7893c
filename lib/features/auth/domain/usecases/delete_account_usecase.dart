import 'package:gotcha_mfg_app/core/usecase/usecase.dart';
import 'package:gotcha_mfg_app/features/auth/data/models/common_response.dart';
import 'package:gotcha_mfg_app/features/auth/domain/repositories/auth_repository.dart';
import 'package:gotcha_mfg_app/shared/models/result.dart';

class DeleteUsecase implements UseCase<Result<CommonResponse>, NoParams> {
  /// Constructor
  DeleteUsecase(this._repository);

  final AuthRepository _repository;

  @override
  Future<Result<CommonResponse>> call(NoParams params) async {
    return _repository.deleteAccount();
  }
}
