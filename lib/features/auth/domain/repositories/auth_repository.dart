import 'package:gotcha_mfg_app/features/auth/data/models/common_response.dart';
import 'package:gotcha_mfg_app/features/auth/data/models/fcm_req_model.dart';
import 'package:gotcha_mfg_app/features/auth/data/models/forgot_req.dart';
import 'package:gotcha_mfg_app/features/auth/data/models/sign_up_request_model.dart';

import '../../../../shared/models/result.dart';
import '../../data/models/identity_group_model.dart';
import '../../data/models/login_model.dart';
import '../../data/models/login_request_model.dart';
import '../../data/models/sign_up_model.dart';

abstract class AuthRepository {
  Future<Result<LoginResponse>> login(LoginRequest request);
  Future<Result<SignUpResponse>> signIn(SignUpRequest request);
  Future<Result<IdentityGroupResponse>> getIdentityGroups();
  Future<Result<CommonResponse>> forgotPassword(ForgotRequestParams request);
  Future<Result<CommonResponse>> deleteAccount();
  Future<Result<CommonResponse>> updateFcmToken(FcmRequestModel request);
}
