import 'package:gotcha_mfg_app/features/help_seeking/data/models/help_seeking_response.dart';
import 'package:gotcha_mfg_app/features/help_seeking/domain/repositories/help_seeking_repository.dart';

import '../../../../core/usecase/usecase.dart';
import '../../../../shared/models/result.dart';

/// UseCase for get help seeking pathways
class GetHelpSeekingPathwaysUseCase
    implements UseCase<Result<HelpSeekingResponse>, void> {
  /// Constructor
  GetHelpSeekingPathwaysUseCase(this._repository);
  final HelpSeekingRepository _repository;

  @override
  Future<Result<HelpSeekingResponse>> call(void params) async {
    return _repository.getHelpSeekingPathways();
  }
}
