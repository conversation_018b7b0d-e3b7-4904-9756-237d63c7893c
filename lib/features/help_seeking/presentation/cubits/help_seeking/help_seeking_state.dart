import 'package:equatable/equatable.dart';
import 'package:gotcha_mfg_app/features/help_seeking/data/models/help_seeking_response.dart';

abstract class HelpSeekingState extends Equatable {
  @override
  List<Object?> get props => [];
}

class HelpSeekingInitial extends HelpSeekingState {}

class HelpSeekingLoading extends HelpSeekingState {}

class HelpSeekingPathwaysLoaded extends HelpSeekingState {
  HelpSeekingPathwaysLoaded(this.helpSeekingResponse);
  final HelpSeekingResponse helpSeekingResponse;
  @override
  List<Object?> get props => [helpSeekingResponse];
}

class HelpSeekingPathwaysError extends HelpSeekingState {
  final String? message;
  HelpSeekingPathwaysError(this.message);
  @override
  List<Object?> get props => [message];
}
