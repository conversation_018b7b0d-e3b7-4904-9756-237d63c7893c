import 'package:flutter_bloc/flutter_bloc.dart';

import '../../../domain/usecases/help_seeking_usecases.dart';
import 'help_seeking_state.dart';

/// Help Seeking Cubit
class HelpSeekingCubit extends Cubit<HelpSeekingState> {
  /// Constructor
  HelpSeekingCubit(this._getHelpSeekingPathwaysUseCase)
      : super(HelpSeekingInitial());

  final GetHelpSeekingPathwaysUseCase _getHelpSeekingPathwaysUseCase;

  /// Get Help Seeking Pathways
  Future<void> getHelpSeekingPathways() async {
    emit(HelpSeekingLoading());
    final result = await _getHelpSeekingPathwaysUseCase.call(null);
    if (result.isSuccess) {
      emit(HelpSeekingPathwaysLoaded(result.data!));
    } else {
      emit(HelpSeekingPathwaysError(result.error!));
    }
  }
}
