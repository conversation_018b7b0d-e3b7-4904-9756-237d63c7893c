import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:gotcha_mfg_app/core/extensions/app_text_theme_extension.dart';
import 'package:gotcha_mfg_app/core/utils/url_launcher.dart';
import 'package:gotcha_mfg_app/shared/widgets/network_image_indicator.dart';

import '../../../../config/theme/app_colors.dart';
import '../../../../core/mixpanel_service.dart';
import '../../../../locator.dart';
import '../../data/models/help_seeking_response.dart';

class UrgentHelpItem extends StatelessWidget {
  final String title;
  final String subtitle;
  final String imageUrl;
  final String link;
  final VoidCallback onTap;

  const UrgentHelpItem({
    super.key,
    required this.title,
    required this.subtitle,
    required this.imageUrl,
    required this.link,
    required this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    final textTheme = Theme.of(context).textTheme;
    return GestureDetector(
      onTap: onTap,
      child: Padding(
        padding: const EdgeInsets.symmetric(vertical: 8.0),
        child: Container(
          decoration: BoxDecoration(
            color: Colors.white.withOpacity(.5),
            borderRadius: BorderRadius.circular(24),
          ),
          child: Padding(
            padding: const EdgeInsets.all(18.0),
            child: Row(
              children: [
                Container(
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(12),
                  ),
                  width: 48,
                  height: 48,
                  child: ClipRRect(
                    borderRadius: BorderRadius.circular(12),
                    child: NetworkImageWithIndicator(
                      imageUrl: imageUrl,
                      fit: BoxFit.contain,
                    ),
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(title,
                          maxLines: 2,
                          overflow: TextOverflow.ellipsis,
                          style: textTheme.labelsBold),
                      const SizedBox(height: 4),
                      Text(subtitle,
                          maxLines: 6,
                          overflow: TextOverflow.ellipsis,
                          style: textTheme.labels),
                      if (link.isNotEmpty) const SizedBox(height: 4),
                      if (link.isNotEmpty)
                        Text(
                          link,
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                          style: textTheme.ralewayMedium.copyWith(
                            fontSize: 13,
                            color: AppColors.navy,
                            decoration: TextDecoration.underline,
                          ),
                        ),
                    ],
                  ),
                ),
                const Gap(8),
                const Icon(Icons.arrow_forward_ios_rounded, size: 16),
              ],
            ),
          ),
        ),
      ),
    );
  }
}

class UrgentHelpWidget extends StatelessWidget {
  final List<TrustedPlace>? urgenthelp;
  const UrgentHelpWidget({super.key, required this.urgenthelp});

  @override
  Widget build(BuildContext context) {
    final textTheme = Theme.of(context).textTheme;
    return Container(
      color: AppColors.grey,
      padding: const EdgeInsets.only(),
      child: Container(
        decoration: const BoxDecoration(
          borderRadius: BorderRadius.only(
            topLeft: Radius.circular(30),
            topRight: Radius.circular(30),
          ),
          color: AppColors.lightRed,
        ),
        child: Padding(
          padding: const EdgeInsets.symmetric(horizontal: 24.0, vertical: 16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const Gap(24),
              Text('Need help right now?', style: textTheme.sectionHeading),
              const Gap(8),
              ...?urgenthelp?.map(
                (place) => UrgentHelpItem(
                  title: place.title ?? 'N/A',
                  subtitle: place.subtitle ?? 'N/A',
                  onTap: () {
                    UrlLauncher.launchPhone(
                      place.linkOrContactNo ?? '',
                      context: context,
                    );
                    // mixpanel
                    sl<MixpanelService>()
                        .trackButtonClick('Urgent Help Tapped', properties: {
                      'Page': 'Help Seeking Pathway Page',
                      'Code':
                          'click.help_seeking_pathway_page.urgent_help_tapped',
                      'Detail': place.title ?? 'N/A',
                    });
                  },
                  imageUrl: place.iconLink ??
                      'https://images.squarespace-cdn.com/content/v1/600a06cbd2a8133c3598b77e/0091dd0c-3f06-4d54-99f6-d48327e771d1/logo.png?format=1500w', // Using provided URL
                  link: place.linkOrContactNo ?? '',
                ),
              ),
              const Gap(32)
            ],
          ),
        ),
      ),
    );
  }
}
