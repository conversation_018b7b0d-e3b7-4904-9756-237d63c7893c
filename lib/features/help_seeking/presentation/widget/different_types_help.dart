import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:gotcha_mfg_app/config/theme/app_assets.dart';
import 'package:gotcha_mfg_app/config/theme/app_colors.dart';
import 'package:gotcha_mfg_app/core/extensions/app_text_theme_extension.dart';

class DifferentTypesOfHelpWidget extends StatelessWidget {
  const DifferentTypesOfHelpWidget({super.key});

  @override
  Widget build(BuildContext context) {
    final textTheme = Theme.of(context).textTheme;
    return Container(
      color: Colors.white,
      padding: const EdgeInsets.only(),
      child: Container(
        decoration: const BoxDecoration(
          borderRadius: BorderRadius.only(
            topLeft: Radius.circular(30),
            topRight: Radius.circular(30),
          ),
          color: AppColors.grey,
        ),
        child: Padding(
          padding: const EdgeInsets.symmetric(horizontal: 24.0, vertical: 16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const Gap(16),
              Text('Different ways to get support',
                  style: textTheme.sectionHeading),
              const Gap(12),
              const SizedBox(width: 12),
              _buildTypeItem(
                title: 'GPs',
                subtitle:
                    'Your GP can connect you with mental health professionals, and help you find support that fits your needs.',
                textTheme: textTheme,
              ),
              _buildTypeItem(
                title: 'Psychologists',
                subtitle:
                    'A psychologist can help with both everyday stress and the treatment of mental health conditions, giving you strategies that support your long-term wellbeing.',
                textTheme: textTheme,
              ),
              _buildTypeItem(
                title: 'Personal coaching',
                subtitle:
                    'A coach, psychotherapist, or counsellor can guide you through challenges, help you set goals, and provide practical tools.',
                textTheme: textTheme,
              ),
              _buildTypeItem(
                title: 'Peer support groups',
                subtitle:
                    'Sometimes, connecting with others who “get it” can provide relief. Peer support groups offer a community where people share similar experiences, helping each other find strength and encouragement.',
                textTheme: textTheme,
              ),
              _buildTypeItem(
                title: 'Hotlines and online supports',
                subtitle:
                    'When you need help in the moment, hotlines and online support are available 24/7 to connect you with trained professionals.',
                textTheme: textTheme,
              ),
              const Gap(22)
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildTypeItem({
    required String title,
    required String subtitle,
    required TextTheme textTheme,
  }) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 12.0),
      child: Container(
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(24),
          color: Colors.white,
        ),
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              SizedBox(
                height: 22,
                child: Image.asset(
                  AppAssets.question,
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(title, style: textTheme.linkText),
                    const SizedBox(height: 8),
                    Text(subtitle, style: textTheme.labels),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
