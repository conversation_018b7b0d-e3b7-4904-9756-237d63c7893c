import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:gotcha_mfg_app/core/extensions/app_text_theme_extension.dart';

import '../../../../config/theme/app_assets.dart';
import '../../../../config/theme/app_colors.dart';

class WhyAskForHelpWidget extends StatelessWidget {
  const WhyAskForHelpWidget({super.key});

  @override
  Widget build(BuildContext context) {
    final textTheme = Theme.of(context).textTheme;
    final size = MediaQuery.sizeOf(context);
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: AppColors.lightRed,
        borderRadius: BorderRadius.circular(12),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              SizedBox(
                height: 16,
                child: Image.asset(
                  AppAssets.question,
                ),
              ),
              const Gap(4),
              Text('Why ask for help?', style: textTheme.linkText),
            ],
          ),
          const SizedBox(height: 2),
          _buildReasonItem(
              'You feel overwhelmed by your emotions or daily stresses.',
              context),
          const SizedBox(height: 2),
          _buildReasonItem(
              'Your usual routines and tools aren\'t bringing relief or helping you feel like yourself.',
              context),
          const SizedBox(height: 2),
          _buildReasonItem(
              'You\'re experiencing ongoing low mood, worry, or lack of motivation that doesn\'t go away.',
              context),
          const SizedBox(height: 2),
          _buildReasonItem(
              'You\'re facing big life changes or challenges that test your normal coping mechanisms.',
              context),
        ],
      ),
    );
  }

  Widget _buildReasonItem(String text, BuildContext context) {
    final textTheme = Theme.of(context).textTheme;
    final size = MediaQuery.sizeOf(context);
    return Padding(
      padding: const EdgeInsets.only(left: 24.0),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Padding(
            padding: EdgeInsets.symmetric(vertical: 6.0),
            child: Icon(
              Icons.circle,
              color: Color(0xFF1A1F36),
              size: 6,
            ),
          ),
          const SizedBox(width: 6),
          Expanded(
            child: Text(text, style: textTheme.labels),
          ),
        ],
      ),
    );
  }
}
