// import 'package:auto_route/auto_route.dart';
// import 'package:flutter/material.dart';
// import 'package:flutter/services.dart';
// import 'package:flutter_bloc/flutter_bloc.dart';
// import 'package:gap/gap.dart';
// import 'package:gotcha_mfg_app/config/router/app_router.gr.dart';
// import 'package:gotcha_mfg_app/core/extensions/app_text_theme_extension.dart';
// import 'package:gotcha_mfg_app/features/help_seeking/presentation/cubits/help_seeking/help_seeking_cubit.dart';
// import 'package:gotcha_mfg_app/features/help_seeking/presentation/cubits/help_seeking/help_seeking_state.dart';
// import 'package:gotcha_mfg_app/shared/widgets/app_header.dart';
// import 'package:gotcha_mfg_app/shared/widgets/primary_button.dart';
// import 'package:youtube_player_flutter/youtube_player_flutter.dart';

// import '../../../../config/theme/app_colors.dart';
// import '../../../../core/mixpanel_service.dart';
// import '../../../../core/utils/platform_utils.dart';
// import '../../../../core/utils/snackbar_service.dart';
// import '../../../../locator.dart';
// import '../../../../shared/widgets/loading_widget.dart';
// import '../../../../shared/widgets/retry_widget.dart';
// import '../widget/ask_for_help.dart';
// import '../widget/helps.dart';

// @RoutePage()
// class HelpSeekingPathwayPage extends StatefulWidget {
//   const HelpSeekingPathwayPage({super.key});

//   @override
//   State<HelpSeekingPathwayPage> createState() => _HelpSeekingPathwayPageState();
// }

// class _HelpSeekingPathwayPageState extends State<HelpSeekingPathwayPage> {
//   late YoutubePlayerController _controller;
//   bool _isPlayerPlaying = true; // Track video playing state

//   @override
//   void initState() {
//     super.initState();
//     sl<MixpanelService>().trackScreenView('Help Seeking Pathway Page');

//     context.read<HelpSeekingCubit>().getHelpSeekingPathways();
//   }

//   @override
//   Widget build(BuildContext context) {
//     final textTheme = Theme.of(context).textTheme;
//     final size = MediaQuery.sizeOf(context);
//     return BlocConsumer<HelpSeekingCubit, HelpSeekingState>(
//       listener: (context, state) {
//         if (state is HelpSeekingPathwaysError) {
//           SnackBarService.info(
//             context: context,
//             message: state.message ?? '',
//           );
//         }
//         if (state is HelpSeekingPathwaysLoaded) {
//           _controller = YoutubePlayerController(
//             initialVideoId: YoutubePlayer.convertUrlToId(
//                     state.helpSeekingResponse.data?.video?[0].linkOrContactNo ??
//                         '') ??
//                 'HQbBJ7dd58Q',
//             flags: const YoutubePlayerFlags(
//               autoPlay: true,
//               hideControls: true,
//               showLiveFullscreenButton: false,
//             ),
//           );
//           _isPlayerPlaying = true; // Reset playing state when video is loaded
//         }
//       },
//       builder: (context, state) {
//         if (state is HelpSeekingLoading) {
//           return const LoadingWidget(color: Colors.white);
//         }
//         if (state is HelpSeekingPathwaysError) {
//           return RetryWidget(
//             color: Colors.white,
//             onRetry: () {
//               context.read<HelpSeekingCubit>().getHelpSeekingPathways();
//             },
//           );
//         }
//         if (state is HelpSeekingPathwaysLoaded) {
//           final urgent = state.helpSeekingResponse.data?.urgentHelp;
//           final trustedplaces = state.helpSeekingResponse.data?.trustedPlace;
//           return Scaffold(
//             appBar: AppBar(
//               toolbarHeight: 0,
//               elevation: 0,
//               systemOverlayStyle: const SystemUiOverlayStyle(
//                 statusBarColor: Colors.white,
//                 systemNavigationBarIconBrightness: Brightness.dark,
//               ),
//             ),
//             extendBodyBehindAppBar: true,
//             resizeToAvoidBottomInset: true,
//             backgroundColor: Colors.white,
//             body: Padding(
//               padding: EdgeInsets.only(
//                 top: isIos ? 4 : 8,
//                 right: 8,
//                 left: 8,
//               ),
//               child: ListView(
//                 children: [
//                   AppHeader(
//                     title: 'More help',
//                     onBackTap: () => Navigator.of(context).pop(),
//                   ),
//                   Container(
//                     color: AppColors.navy,
//                     padding: const EdgeInsets.only(),
//                     child: Container(
//                       decoration: const BoxDecoration(
//                         borderRadius: BorderRadius.only(
//                           topLeft: Radius.circular(30),
//                           topRight: Radius.circular(30),
//                         ),
//                         color: AppColors.grey,
//                       ),
//                       child: Padding(
//                         padding: const EdgeInsets.only(top: 16),
//                         child: Column(
//                           crossAxisAlignment: CrossAxisAlignment.start,
//                           children: [
//                             const Gap(12),
//                             const Padding(
//                               padding: EdgeInsets.symmetric(horizontal: 24.0),
//                               child: WhyAskForHelpWidget(),
//                             ),
//                             const Gap(24),
//                             Padding(
//                               padding:
//                                   const EdgeInsets.symmetric(horizontal: 24.0),
//                               child: Text(
//                                 'Not sure what to expect?',
//                                 style: textTheme.gothamMedium.copyWith(
//                                   fontSize: 20,
//                                   color: AppColors.navy,
//                                 ),
//                               ),
//                             ),
//                             const Gap(16),
//                             Padding(
//                               padding:
//                                   const EdgeInsets.symmetric(horizontal: 24.0),
//                               child: Stack(
//                                 children: [
//                                   ClipRRect(
//                                     borderRadius: BorderRadius.circular(30),
//                                     child: Container(
//                                       decoration: BoxDecoration(
//                                         borderRadius: BorderRadius.circular(30),
//                                       ),
//                                       child: AspectRatio(
//                                         aspectRatio: 18 / 24,
//                                         child: YoutubePlayer(
//                                           controller: _controller,
//                                           showVideoProgressIndicator: false,
//                                         ),
//                                       ),
//                                     ),
//                                   ),
//                                   Positioned(
//                                     left: 24.0,
//                                     top: 24.0,
//                                     child: Container(
//                                       padding: const EdgeInsets.symmetric(
//                                         horizontal: 12,
//                                         vertical: 6,
//                                       ),
//                                       decoration: BoxDecoration(
//                                         color: AppColors.lightRed,
//                                         borderRadius: BorderRadius.circular(16),
//                                       ),
//                                       child: Text(
//                                         'Self-assessment',
//                                         style:
//                                             textTheme.ralewaySemiBold.copyWith(
//                                           color: AppColors.navy,
//                                           fontSize: 10,
//                                         ),
//                                       ),
//                                     ),
//                                   ),
//                                   Positioned(
//                                     bottom: 24.0,
//                                     right: 24.0,
//                                     child: GestureDetector(
//                                       onTap: () {
//                                         setState(() {
//                                           if (_isPlayerPlaying) {
//                                             _controller.pause();
//                                           } else {
//                                             _controller.play();
//                                           }
//                                           _isPlayerPlaying = !_isPlayerPlaying;
//                                         });
//                                       },
//                                       child: Container(
//                                         padding: const EdgeInsets.all(8),
//                                         decoration: BoxDecoration(
//                                           color: Colors.black54,
//                                           borderRadius:
//                                               BorderRadius.circular(20),
//                                         ),
//                                         child: Icon(
//                                           _isPlayerPlaying
//                                               ? Icons.pause
//                                               : Icons.play_arrow,
//                                           color: Colors.white,
//                                           size: 24,
//                                         ),
//                                       ),
//                                     ),
//                                   ),
//                                 ],
//                               ),
//                             ),
//                             const Gap(16),
//                             Padding(
//                               padding:
//                                   const EdgeInsets.symmetric(horizontal: 24.0),
//                               child: SizedBox(
//                                 width: size.width,
//                                 child: PrimaryButton(
//                                   text: 'Try self-assessment tool',
//                                   onPressed: () {
//                                     _controller.pause();
//                                     context.pushRoute(
//                                         const SelfAssesmentWelcomeRoute());

//                                     // Navigator.push(
//                                     //   context,
//                                     //   MaterialPageRoute(
//                                     //     builder: (context) =>
//                                     //         const SelfAssesmentWelcomePage(),
//                                     //   ),
//                                     // );
//                                   },
//                                   borderRadius: 20,
//                                 ),
//                               ),
//                             ),
//                             const Gap(32),
//                             UrgentHelpWidget(
//                               urgenthelp: urgent,
//                             ),
//                             const DifferentTypesOfHelpWidget(),
//                             SomeTrustedPlaces(
//                               trustedplaces: trustedplaces,
//                             ),
//                           ],
//                         ),
//                       ),
//                     ),
//                   ),
//                 ],
//               ),
//             ),
//           );
//         }
//         return const SizedBox();
//       },
//     );
//   }
// }

import 'package:auto_route/auto_route.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:gap/gap.dart';
import 'package:gotcha_mfg_app/core/extensions/app_text_theme_extension.dart';
import 'package:gotcha_mfg_app/features/help_seeking/presentation/cubits/help_seeking/help_seeking_cubit.dart';
import 'package:gotcha_mfg_app/features/help_seeking/presentation/cubits/help_seeking/help_seeking_state.dart';
import 'package:gotcha_mfg_app/features/help_seeking/presentation/widget/some_trusted_places.dart';
import 'package:gotcha_mfg_app/shared/widgets/app_header.dart';
import 'package:visibility_detector/visibility_detector.dart';
import 'package:youtube_player_flutter/youtube_player_flutter.dart';

import '../../../../config/router/app_router.gr.dart';
import '../../../../config/theme/app_colors.dart';
import '../../../../core/mixpanel_service.dart';
import '../../../../core/utils/platform_utils.dart';
import '../../../../core/utils/snackbar_service.dart';
import '../../../../locator.dart';
import '../../../../shared/widgets/loading_widget.dart';
import '../../../../shared/widgets/primary_button.dart';
import '../../../../shared/widgets/retry_widget.dart';
import '../widget/different_types_help.dart';
import '../widget/urgent_help.dart';

@RoutePage()
class HelpSeekingPathwayPage extends StatefulWidget {
  const HelpSeekingPathwayPage({super.key});

  @override
  State<HelpSeekingPathwayPage> createState() => _HelpSeekingPathwayPageState();
}

class _HelpSeekingPathwayPageState extends State<HelpSeekingPathwayPage> {
  late YoutubePlayerController _controller;
  bool _isPlayerPlaying = true;

  @override
  void initState() {
    super.initState();
    sl<MixpanelService>().trackScreenView(
      'Help Seeking Pathway Page',
      properties: {'Code': 'screen_view.help_seeking_pathway_page'},
    );

    context.read<HelpSeekingCubit>().getHelpSeekingPathways();
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final textTheme = Theme.of(context).textTheme;
    final size = MediaQuery.sizeOf(context);
    return BlocConsumer<HelpSeekingCubit, HelpSeekingState>(
      listener: (context, state) {
        if (state is HelpSeekingPathwaysError) {
          SnackBarService.error(
            context: context,
            message: state.message ?? '',
          );
        }
        if (state is HelpSeekingPathwaysLoaded) {
          _controller = YoutubePlayerController(
            initialVideoId: YoutubePlayer.convertUrlToId(
                    state.helpSeekingResponse.data?.video?[0].linkOrContactNo ??
                        '') ??
                'HQbBJ7dd58Q',
            flags: const YoutubePlayerFlags(
              autoPlay: true,
              hideControls: true,
              showLiveFullscreenButton: false,
            ),
          );
          _isPlayerPlaying = true;
        }
      },
      builder: (context, state) {
        if (state is HelpSeekingLoading) {
          return const LoadingWidget(color: Colors.white);
        }
        if (state is HelpSeekingPathwaysError) {
          return RetryWidget(
            color: Colors.white,
            onRetry: () {
              context.read<HelpSeekingCubit>().getHelpSeekingPathways();
            },
          );
        }
        if (state is HelpSeekingPathwaysLoaded) {
          bool isSelfAssessment =
              state.helpSeekingResponse.data?.showSelfAssessment ?? false;
          // final isSelfAssessment =
          //     state.helpSeekingResponse.data?.showSelfAssessment ?? false;
          final urgent = state.helpSeekingResponse.data?.urgentHelp;
          final trustedplaces = state.helpSeekingResponse.data?.trustedPlace;
          return Scaffold(
            appBar: AppBar(
              toolbarHeight: 0,
              elevation: 0,
              systemOverlayStyle: const SystemUiOverlayStyle(
                statusBarColor: Colors.white,
                systemNavigationBarIconBrightness: Brightness.dark,
                statusBarBrightness: Brightness.light,
                statusBarIconBrightness: Brightness.dark,
                systemNavigationBarColor: AppColors.lightBlue,
              ),
            ),
            extendBodyBehindAppBar: false,
            resizeToAvoidBottomInset: true,
            backgroundColor: Colors.white,
            body: Padding(
              padding: EdgeInsets.only(
                top: isIos ? 4 : 8,
                right: 8,
                left: 8,
              ),
              child: ListView(
                padding: EdgeInsets.zero,
                children: [
                  AppHeader(
                    title: 'More help',
                    onBackTap: () => Navigator.of(context).pop(),
                  ),
                  Container(
                    color: AppColors.navy,
                    padding: const EdgeInsets.only(),
                    child: Container(
                      decoration: const BoxDecoration(
                        borderRadius: BorderRadius.only(
                          topLeft: Radius.circular(30),
                          topRight: Radius.circular(30),
                        ),
                        color: Colors.white,
                      ),
                      child: Padding(
                        padding: const EdgeInsets.only(top: 16),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            const Gap(12),
                            Padding(
                              padding:
                                  const EdgeInsets.symmetric(horizontal: 24.0),
                              child: Text(
                                "Need support? You don't have to worry alone.",
                                style: textTheme.sectionHeading,
                              ),
                            ),
                            const Gap(8),

                            Padding(
                              padding:
                                  const EdgeInsets.symmetric(horizontal: 24.0),
                              child: RichText(
                                // Changed Text to RichText
                                text: TextSpan(
                                  style: textTheme.bodyRegular.copyWith(
                                    height: 1.5,
                                  ), // Still using TextSpan, but now inside RichText's text property
                                  // Default style applied to the entire RichText
                                  children: [
                                    // TextSpan(
                                    //     text:
                                    //         "Seeking help is a sign of strength - you don’t have to wait until things feel overwhelming. An important part of mental fitness is knowing when to check in with someone you trust.",
                                    //     style: textTheme.bodyEmphasis),
                                    TextSpan(
                                      text:
                                          "Feeling overwhelmed, flat, or stuck is part of being human, but you don’t have to face it alone. Reaching out to someone you trust or seeking support from a professional is a strong and brave step.",
                                      style: textTheme.bodyRegular,
                                    ),
                                  ],
                                ),
                              ),
                            ),
                            // const Gap(12),
                            // const Padding(
                            //   padding: EdgeInsets.symmetric(horizontal: 24.0),
                            //   child: WhyAskForHelpWidget(),
                            // ),
                            isSelfAssessment ? const Gap(24) : const SizedBox(),
                            if (isSelfAssessment)
                              Padding(
                                padding: const EdgeInsets.symmetric(
                                    horizontal: 24.0),
                                child: Text('Not sure what to expect?',
                                    style: textTheme.sectionHeading),
                              ),
                            if (isSelfAssessment) const Gap(16),
                            if (isSelfAssessment)
                              Padding(
                                padding: const EdgeInsets.symmetric(
                                    horizontal: 24.0),
                                child: VisibilityDetector(
                                  key: const Key(
                                      'youtube-player-visibility-key'),
                                  onVisibilityChanged: (visibilityInfo) {
                                    if (visibilityInfo.visibleFraction == 0) {
                                      if (mounted) {
                                        // Check if the widget is mounted
                                        _controller.pause();
                                        setState(() {
                                          _isPlayerPlaying = false;
                                        });
                                      }
                                    }
                                  },
                                  child: Stack(
                                    children: [
                                      ClipRRect(
                                        borderRadius: BorderRadius.circular(30),
                                        child: Container(
                                          decoration: BoxDecoration(
                                            borderRadius:
                                                BorderRadius.circular(30),
                                          ),
                                          child: AspectRatio(
                                            aspectRatio: 18 / 24,
                                            child: YoutubePlayer(
                                              controller: _controller,
                                              showVideoProgressIndicator: false,
                                            ),
                                          ),
                                        ),
                                      ),
                                      Positioned(
                                        left: 24.0,
                                        top: 24.0,
                                        child: Container(
                                          padding: const EdgeInsets.symmetric(
                                            horizontal: 12,
                                            vertical: 6,
                                          ),
                                          decoration: BoxDecoration(
                                            color: AppColors.lightRed,
                                            borderRadius:
                                                BorderRadius.circular(16),
                                          ),
                                          child: Text(
                                            'Self-assessment',
                                            style: textTheme.ralewaySemiBold
                                                .copyWith(
                                              color: AppColors.navy,
                                              fontSize: 10,
                                            ),
                                          ),
                                        ),
                                      ),
                                      Positioned(
                                        bottom: 24.0,
                                        right: 24.0,
                                        child: GestureDetector(
                                          onTap: () {
                                            setState(() {
                                              if (_isPlayerPlaying) {
                                                _controller.pause();
                                              } else {
                                                _controller.play();
                                              }
                                              _isPlayerPlaying =
                                                  !_isPlayerPlaying;
                                            });
                                          },
                                          child: Container(
                                            padding: const EdgeInsets.all(8),
                                            decoration: BoxDecoration(
                                              color: Colors.black54,
                                              borderRadius:
                                                  BorderRadius.circular(20),
                                            ),
                                            child: Icon(
                                              _isPlayerPlaying
                                                  ? Icons.pause
                                                  : Icons.play_arrow,
                                              color: Colors.white,
                                              size: 24,
                                            ),
                                          ),
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                              ),
                            if (isSelfAssessment) const Gap(16),
                            if (isSelfAssessment)
                              Padding(
                                padding: const EdgeInsets.symmetric(
                                    horizontal: 24.0),
                                child: SizedBox(
                                  width: size.width,
                                  child: PrimaryButton(
                                    text: 'Try self-assessment tool',
                                    onPressed: () {
                                      _controller.pause();
                                      context.pushRoute(
                                          const SelfAssesmentWelcomeRoute());
                                      // mixpanel
                                      sl<MixpanelService>().trackButtonClick(
                                          'Try Self-assessment Tool',
                                          properties: {
                                            'Page': 'Help Seeking Pathway Page',
                                            'Code':
                                                'click.help_seeking_pathway_page.try_self_assessment_tool'
                                          });
                                    },
                                    borderRadius: 20,
                                  ),
                                ),
                              ),
                            const Gap(36),
                            const DifferentTypesOfHelpWidget(),
                            UrgentHelpWidget(
                              urgenthelp: urgent,
                            ),
                            SomeTrustedPlaces(
                              trustedplaces: trustedplaces,
                            ),
                          ],
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          );
        }
        return const SizedBox();
      },
    );
  }
}
