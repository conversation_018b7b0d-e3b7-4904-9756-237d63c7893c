import 'package:dio/dio.dart';
import 'package:firebase_crashlytics/firebase_crashlytics.dart';
import 'package:gotcha_mfg_app/features/help_seeking/data/models/help_seeking_response.dart';

import '../../../../shared/models/result.dart';
import 'help_seeking_remote_datasource.dart';

/// Help seeking remote data source implementation
class HelpSeekingRemoteDataSourceImpl implements HelpSeekingRemoteDataSource {
  /// Constructor
  HelpSeekingRemoteDataSourceImpl(this._dio);

  final Dio _dio;

  @override
  Future<Result<HelpSeekingResponse>> getHelpSeekingPathways() async {
    try {
      final response = await _dio.get('/app/help/help-seeking-pathways');
      if (response.statusCode == 200) {
        final data = response.data;
        final helpSeekingResponse = HelpSeekingResponse.fromJson(data);
        return Result.success(helpSeekingResponse);
      } else {
        return Result.failure(
            '${response.statusCode} - ${response.data['message']}');
      }
    } catch (e) {
      FirebaseCrashlytics.instance.recordError(e, StackTrace.current);
      return Result.failure(
          'An error occurred during getting help seeking pathways: ${e.toString()}');
    }
  }
}
