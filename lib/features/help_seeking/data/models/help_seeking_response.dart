import 'dart:convert';

class HelpSeekingResponse {
  final String? message;
  final String? status;
  final Data? data;

  HelpSeekingResponse({
    this.message,
    this.status,
    this.data,
  });

  HelpSeekingResponse copyWith({
    String? message,
    String? status,
    Data? data,
  }) =>
      HelpSeekingResponse(
        message: message ?? this.message,
        status: status ?? this.status,
        data: data ?? this.data,
      );

  factory HelpSeekingResponse.fromRawJson(String str) =>
      HelpSeekingResponse.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory HelpSeekingResponse.fromJson(Map<String, dynamic> json) =>
      HelpSeekingResponse(
        message: json["message"],
        status: json["status"],
        data: json["data"] == null ? null : Data.fromJson(json["data"]),
      );

  Map<String, dynamic> toJson() => {
        "message": message,
        "status": status,
        "data": data?.toJson(),
      };
}

class Data {
  final List<TrustedPlace>? video;
  final List<TrustedPlace>? trustedPlace;
  final List<TrustedPlace>? urgentHelp;
  bool? showSelfAssessment;

  Data(
      {this.video,
      this.trustedPlace,
      this.urgentHelp,
      this.showSelfAssessment});

  Data copyWith(
          {List<TrustedPlace>? video,
          List<TrustedPlace>? trustedPlace,
          List<TrustedPlace>? urgentHelp,
          final bool? showSelfAssessment}) =>
      Data(
        video: video ?? this.video,
        trustedPlace: trustedPlace ?? this.trustedPlace,
        urgentHelp: urgentHelp ?? this.urgentHelp,
        showSelfAssessment: showSelfAssessment ?? this.showSelfAssessment,
      );

  factory Data.fromRawJson(String str) => Data.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory Data.fromJson(Map<String, dynamic> json) => Data(
        video: json["video"] == null
            ? []
            : List<TrustedPlace>.from(
                json["video"]!.map((x) => TrustedPlace.fromJson(x))),
        trustedPlace: json["trusted_place"] == null
            ? []
            : List<TrustedPlace>.from(
                json["trusted_place"]!.map((x) => TrustedPlace.fromJson(x))),
        urgentHelp: json["urgent_help"] == null
            ? []
            : List<TrustedPlace>.from(
                json["urgent_help"]!.map((x) => TrustedPlace.fromJson(x))),
        showSelfAssessment: json["show_self_assessment"],
      );

  Map<String, dynamic> toJson() => {
        "video": video == null
            ? []
            : List<dynamic>.from(video!.map((x) => x.toJson())),
        "trusted_place": trustedPlace == null
            ? []
            : List<dynamic>.from(trustedPlace!.map((x) => x.toJson())),
        "urgent_help": urgentHelp == null
            ? []
            : List<dynamic>.from(urgentHelp!.map((x) => x.toJson())),
        "show_self_assessment": showSelfAssessment,
      };
}

class TrustedPlace {
  final String? id;
  final String? title;
  final String? type;
  final String? subtitle;
  final String? linkOrContactNo;
  final String? iconLink;

  TrustedPlace({
    this.id,
    this.title,
    this.type,
    this.subtitle,
    this.linkOrContactNo,
    this.iconLink,
  });

  TrustedPlace copyWith({
    String? id,
    String? title,
    String? type,
    String? subtitle,
    String? linkOrContactNo,
    String? iconLink,
  }) =>
      TrustedPlace(
        id: id ?? this.id,
        title: title ?? this.title,
        type: type ?? this.type,
        subtitle: subtitle ?? this.subtitle,
        linkOrContactNo: linkOrContactNo ?? this.linkOrContactNo,
        iconLink: iconLink ?? this.iconLink,
      );

  factory TrustedPlace.fromRawJson(String str) =>
      TrustedPlace.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory TrustedPlace.fromJson(Map<String, dynamic> json) => TrustedPlace(
        id: json["id"],
        title: json["title"],
        type: json["type"],
        subtitle: json["subtitle"],
        linkOrContactNo: json["link_or_contact_no"],
        iconLink: json["icon_link"],
      );

  Map<String, dynamic> toJson() => {
        "id": id,
        "title": title,
        "type": type,
        "subtitle": subtitle,
        "link_or_contact_no": linkOrContactNo,
        "icon_link": iconLink,
      };
}
