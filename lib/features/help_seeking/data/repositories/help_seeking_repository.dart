import 'package:gotcha_mfg_app/features/help_seeking/data/models/help_seeking_response.dart';

import '../../../../shared/models/result.dart';
import '../../domain/repositories/help_seeking_repository.dart';
import '../data_sources/help_seeking_remote_datasource.dart';

class HelpSeekingRepositoryImpl implements HelpSeekingRepository {
  HelpSeekingRepositoryImpl(this._remoteDataSource);
  final HelpSeekingRemoteDataSource _remoteDataSource;

  @override
  Future<Result<HelpSeekingResponse>> getHelpSeekingPathways() async {
    return await _remoteDataSource.getHelpSeekingPathways();
  }
}
