import 'package:gotcha_mfg_app/features/favourites/data/models/add_favourites.dart';
import 'package:gotcha_mfg_app/features/favourites/data/models/favourite_request_model.dart';

import '../../../../core/usecase/usecase.dart';
import '../../../../shared/models/result.dart';
import '../repositories/favourites_repository.dart';

class AddFavoritesUseCase
    implements UseCase<Result<AddFavourites>, FavouriteRequest> {
  AddFavoritesUseCase(this._repository);

  final FavouritesRepository _repository;

  @override
  Future<Result<AddFavourites>> call(FavouriteRequest request) async {
    return _repository.addFavorites(request);
  }
}
