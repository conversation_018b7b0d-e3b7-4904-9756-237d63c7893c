import 'package:gotcha_mfg_app/core/usecase/usecase.dart';
import 'package:gotcha_mfg_app/features/favourites/domain/repositories/favourites_repository.dart';
import 'package:gotcha_mfg_app/shared/models/result.dart';

import '../../data/models/get_favourites.dart';

class GetFilteredFavouriteUseCase
    implements
        UseCase<Result<FavouritesFilter>, FilteredFavouriteResponseParams> {
  GetFilteredFavouriteUseCase(this._repository);

  final FavouritesRepository _repository;

  @override
  Future<Result<FavouritesFilter>> call(
      FilteredFavouriteResponseParams params) async {
    return _repository.getFilteredFavourites(params);
  }
}

class FilteredFavouriteResponseParams {
  bool? isExercise;
  bool? isWorkout;
  bool? isAll;

  FilteredFavouriteResponseParams({
    this.isAll = true,
    this.isWorkout = false,
    this.isExercise = false,
  });

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = {};
    if (isWorkout != null) data['isWorkout'] = isWorkout;
    if (isExercise != null) data['isExercise'] = isExercise;
    if (isAll != null) data['isAll'] = isAll;
    return data;
  }
}
