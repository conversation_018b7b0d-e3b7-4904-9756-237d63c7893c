import 'package:gotcha_mfg_app/core/usecase/usecase.dart';
import 'package:gotcha_mfg_app/features/favourites/data/models/not_interest_model.dart';
import 'package:gotcha_mfg_app/features/favourites/data/models/not_interest_response.dart';
import 'package:gotcha_mfg_app/shared/models/result.dart';

import '../repositories/favourites_repository.dart';

/// UseCase for Not Interested
class NotInterestUseCase
    implements UseCase<Result<NotInterestResponse>, NotInterestRequestModel> {
  /// Constructor
  NotInterestUseCase(this._repository);

  final FavouritesRepository _repository;

  @override
  Future<Result<NotInterestResponse>> call(
      NotInterestRequestModel request) async {
    return _repository.notInterested(request);
  }
}
