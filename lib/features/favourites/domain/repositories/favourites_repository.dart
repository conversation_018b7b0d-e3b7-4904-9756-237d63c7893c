import 'package:gotcha_mfg_app/features/favourites/data/models/get_favourites.dart';
import 'package:gotcha_mfg_app/features/favourites/data/models/not_interest_model.dart';
import 'package:gotcha_mfg_app/features/favourites/data/models/not_interest_response.dart';
import 'package:gotcha_mfg_app/features/favourites/domain/usecases/get_filtered_favourites.dart';
import 'package:gotcha_mfg_app/shared/models/result.dart';

import '../../data/models/add_favourites.dart';
import '../../data/models/favourite_request_model.dart';

abstract class FavouritesRepository {
  // Future<Result<CategoriesResponse>> getCategories();
  Future<Result<FavouritesFilter>> getFilteredFavourites(
      FilteredFavouriteResponseParams params);
  Future<Result<AddFavourites>> addFavorites(FavouriteRequest request);
  Future<Result<NotInterestResponse>> notInterested(
      NotInterestRequestModel request);
}
