// import 'package:flutter/material.dart';

// class CategoryCard extends StatelessWidget {
//   final String imageUrl;
//   final String categoryName;
//   final VoidCallback onTap;
//   final TextStyle? textStyle;
//   final double borderRadius;
//   final Color overlayColor;

//   const CategoryCard({
//     super.key,
//     required this.imageUrl,
//     required this.categoryName,
//     required this.onTap,
//     this.textStyle,
//     this.borderRadius = 16,
//     this.overlayColor = Colors.black26,
//   });

//   @override
//   Widget build(BuildContext context) {
//     return GestureDetector(
//       onTap: onTap,
//       child: Container(
//         padding: const EdgeInsets.all(16),
//         decoration: BoxDecoration(
//           borderRadius: BorderRadius.circular(borderRadius),
//           image: DecorationImage(
//             image: NetworkImage(imageUrl),
//             fit: BoxFit.cover,
//             colorFilter: ColorFilter.mode(
//               overlayColor,
//               BlendMode.darken,
//             ),
//           ),
//         ),
//         alignment: Alignment.center,
//         child: Text(
//           categoryName,
//           style: textStyle ??
//               const TextStyle(
//                 fontSize: 14,
//                 color: Colors.white,
//                 fontWeight: FontWeight.bold,
//               ),
//           textAlign: TextAlign.center,
//         ),
//       ),
//     );
//   }
// }
