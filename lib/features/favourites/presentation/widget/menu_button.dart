import 'package:flutter/material.dart';

import '../../../../config/theme/app_colors.dart';

class MenuButton extends StatelessWidget {
  final String text;
  final bool isSelected;
  final VoidCallback onTap;
  final Color selectedColor;
  final Color unselectedColor;
  final Color selectedTextColor;
  final Color unselectedTextColor;
  final TextStyle? textStyle;
  final double height;
  final double horizontalPadding;

  const MenuButton({
    super.key,
    required this.text,
    required this.isSelected,
    required this.onTap,
    this.selectedColor = Colors.red,
    this.unselectedColor = Colors.white,
    this.selectedTextColor = Colors.white,
    this.unselectedTextColor = AppColors.navy,
    this.textStyle,
    this.height = 55,
    this.horizontalPadding = 15,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.fromLTRB(0, 0, 8, 0),
      child: GestureDetector(
        onTap: onTap,
        child: Container(
          height: height,
          decoration: BoxDecoration(
            border: Border.all(
              color: isSelected ? selectedColor : Colors.transparent,
            ),
            color: isSelected ? selectedColor : unselectedColor,
            borderRadius: const BorderRadius.all(Radius.circular(100)),
          ),
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Padding(
                padding: EdgeInsets.symmetric(horizontal: horizontalPadding),
                child: Text(
                  text,
                  style: (textStyle ?? const TextStyle()).copyWith(
                    color: isSelected ? selectedTextColor : unselectedTextColor,
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
