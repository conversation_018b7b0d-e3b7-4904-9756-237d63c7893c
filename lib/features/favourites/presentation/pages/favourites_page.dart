import 'package:auto_route/auto_route.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:gap/gap.dart';
import 'package:gotcha_mfg_app/config/theme/app_colors.dart';
import 'package:gotcha_mfg_app/core/extensions/app_text_theme_extension.dart';
import 'package:gotcha_mfg_app/core/utils/platform_utils.dart';
import 'package:gotcha_mfg_app/core/utils/snackbar_service.dart';
import 'package:gotcha_mfg_app/shared/widgets/app_header.dart';
import 'package:gotcha_mfg_app/shared/widgets/loading_widget.dart';
import 'package:gotcha_mfg_app/shared/widgets/retry_widget.dart';
import '../../../../config/router/app_router.gr.dart';
import '../../../../core/mixpanel_service.dart';
import '../../../../locator.dart';
import '../../../exercise/presentation/pages/mixed_exercise_page.dart';
import '../../../explore/presentation/pages/explore_home_page.dart';
import '../../domain/usecases/get_filtered_favourites.dart';
import '../bloc/favourite/favourites_cubit.dart';
import '../../../../shared/widgets/no_data.dart';

@RoutePage()
class FavouritesPage extends StatefulWidget {
  const FavouritesPage({super.key});

  @override
  State<FavouritesPage> createState() => _FavouritesPageState();
}

class _FavouritesPageState extends State<FavouritesPage> {
  final List<Menu> _menus = [
    Menu(name: 'All', checked: true, selectedVenue: 1),
    Menu(name: 'Workouts', checked: false, selectedVenue: 2),
    Menu(name: 'Exercises', checked: false, selectedVenue: 3),
  ];

  var params = FilteredFavouriteResponseParams(
    isAll: true,
    isWorkout: false,
    isExercise: false,
  );

  @override
  initState() {
    super.initState();
    sl<MixpanelService>().trackScreenView(
      'Favourites Page',
      properties: {'Code': 'screen_view.favourite_page'},
    );
    context.read<FavouritesCubit>().getFavourites(params);
  }

  void _updateSelectedItem(int index) {
    setState(() {
      for (var menu in _menus) {
        menu.checked = false;
      }
      _menus[index].checked = true;
    });
  }

  @override
  void dispose() {
    super.dispose();
  }

  FilteredFavouriteResponseParams filterparams =
      FilteredFavouriteResponseParams(isAll: true);

  @override
  Widget build(BuildContext context) {
    final textTheme = Theme.of(context).textTheme;
    final size = MediaQuery.of(context).size;

    return BlocConsumer<FavouritesCubit, GetFavouriteState>(
      listener: (context, state) {
        if (state is FavouritesError) {
          SnackBarService.error(
            context: context,
            message: state.message,
          );
        }
        if (state is FavouritesToggled) {
          context.read<FavouritesCubit>().getFavourites(filterparams);
        }
        if (state is FeatureNotInterestedSuccess) {
          context.read<FavouritesCubit>().getFavourites(filterparams);
        }
      },
      builder: (context, state) {
        if (state is FavouritesLoading) {
          return const LoadingWidget(color: Colors.white);
        }
        if (state is FavouritesError) {
          return RetryWidget(
            color: Colors.white,
            onRetry: () {
              context.read<FavouritesCubit>().getFavourites(params);
              ();
            },
          );
        }
        if (state is FavouritesLoaded) {
          final favourite = state.favouritesResponse;
          return Scaffold(
            appBar: AppBar(
              toolbarHeight: 0,
              elevation: 0,
              systemOverlayStyle: const SystemUiOverlayStyle(
                statusBarColor: Colors.white,
                systemNavigationBarIconBrightness: Brightness.dark,
                statusBarBrightness: Brightness.light,
                statusBarIconBrightness: Brightness.dark,
                systemNavigationBarColor: AppColors.grey,
              ),
            ),
            body: Padding(
              padding: EdgeInsets.fromLTRB(
                8,
                isIos ? 4 : 8,
                8,
                0,
              ),
              child: Column(
                children: [
                  AppHeader(
                    onBackTap: () {
                      Navigator.pop(context);
                    },
                    title: 'Favourites',
                  ),
                  Expanded(
                    child: Container(
                      color: AppColors.navy,

                      // Categories and Workouts or Search Results
                      child: Container(
                        height: size.height,
                        decoration: const BoxDecoration(
                          borderRadius: BorderRadius.only(
                            topLeft: Radius.circular(30),
                            topRight: Radius.circular(30),
                          ),
                          color: AppColors.grey,
                        ),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            const Gap(28),
                            Padding(
                              padding:
                                  const EdgeInsets.symmetric(horizontal: 24),
                              child: SizedBox(
                                height: 40,
                                width: double.infinity,
                                child: ListView.builder(
                                  // controller: scrollController,
                                  key: const Key("searchResults"),
                                  itemCount: _menus.length,
                                  scrollDirection: Axis.horizontal,
                                  itemBuilder: (context, index) {
                                    return MenuButton(
                                      text: _menus[index].name,
                                      isSelected: _menus[index].checked,
                                      onTap: () {
                                        _updateSelectedItem(index);
                                        if (index == 0) {
                                          filterparams =
                                              FilteredFavouriteResponseParams(
                                            isAll: true,
                                            isWorkout: false,
                                            isExercise: false,
                                          );
                                        } else if (index == 1) {
                                          filterparams =
                                              FilteredFavouriteResponseParams(
                                            isAll: false,
                                            isWorkout: true,
                                            isExercise: false,
                                          );
                                        } else if (index == 2) {
                                          filterparams =
                                              FilteredFavouriteResponseParams(
                                            isAll: false,
                                            isWorkout: false,
                                            isExercise: true,
                                          );
                                        }

                                        context
                                            .read<FavouritesCubit>()
                                            .getFavourites(filterparams);
                                        // mixpanel
                                        sl<MixpanelService>().trackButtonClick(
                                            'Menu Button Tapped',
                                            properties: {
                                              'Page': 'Favourites Page',
                                              'Code':
                                                  'click.favourites_page.menu_button_tapped',
                                              'Menu Name': _menus[index].name
                                            });
                                      },
                                      textStyle: textTheme.ralewaySemiBold
                                          .copyWith(fontSize: 14),
                                    );
                                  },
                                ),
                              ),
                            ),
                            Padding(
                              padding: const EdgeInsets.symmetric(
                                horizontal: 24,
                                vertical: 12,
                              ),
                              child: Text(
                                '${favourite?.data?.length} results', //add
                                style: textTheme.ralewayRegular.copyWith(
                                  fontSize: 14,
                                  color: AppColors.navy,
                                ),
                              ),
                            ),
                            Expanded(
                              child: favourite?.data?.isEmpty == true
                                  ? NoData(
                                      textTheme: textTheme,
                                    )
                                  : ListView.separated(
                                      // controller: scrollController,
                                      padding: const EdgeInsets.only(
                                          top: 4,
                                          bottom: 120,
                                          left: 24,
                                          right: 24),
                                      itemCount: favourite?.data?.length ?? 0,
                                      shrinkWrap: true,
                                      scrollDirection: Axis.vertical,
                                      itemBuilder: (context, index) {
                                        var item = favourite?.data?[index];
                                        return ExerciseResult(
                                          isExercise: item?.isExercise ?? false,
                                          id: item?.id ?? 'N/A',
                                          title: item?.title ?? 'N/A',
                                          subtitle: item?.exerciseType ?? 'N/A',
                                          duration:
                                              item?.mediaDuration ?? 'N/A',
                                          imageUrl: item?.thumbnailUrl ?? '',
                                          onTap: () {
                                            if (item?.isExercise == true) {
                                              Navigator.of(context).push(
                                                MaterialPageRoute(
                                                  builder: (context) =>
                                                      MixedExercisePage(
                                                    exerciseId: item?.id ?? '',
                                                    notification: false,
                                                    id: item?.id ?? '',
                                                    seriesId: '',
                                                    isLast: false,
                                                    isFirst: false,
                                                    isOverride: false,
                                                  ),
                                                ),
                                              );
                                              // mixpanel
                                              sl<MixpanelService>()
                                                  .trackButtonClick(
                                                      'Exercise Tapped',
                                                      properties: {
                                                    'Page': 'Favourites Page',
                                                    'Code':
                                                        'click.favourites_page.exercise_tapped'
                                                  });
                                              return;
                                            } else {
                                              context.pushRoute(
                                                WorkoutRoute(
                                                    seriesId: item?.id ?? ''),
                                              );
                                              // mixpanel
                                              sl<MixpanelService>()
                                                  .trackButtonClick(
                                                      'Workout Tapped',
                                                      properties: {
                                                    'Page': 'Favourites Page',
                                                    'Code':
                                                        'click.favourites_page.workout_tapped'
                                                  });
                                              return;
                                            }
                                            //----update

                                            // if (item?.mediaType == 'video') {
                                            //   context.pushRoute(
                                            //     VideoPlayerRoute(
                                            //       notification: false,
                                            //       id: item?.id ?? '',
                                            //       seriesId: "",
                                            //       isLast: false,
                                            //       isFirst: false,
                                            //       isOverride: false,
                                            //       isRedo: false,
                                            //     ),
                                            //   );
                                            //   return;
                                            // } else if (item?.mediaType ==
                                            //     'flip_text') {
                                            //   context.pushRoute(FlipTextRoute(
                                            //     title: '',
                                            //     subtitle: '',
                                            //     body: '',
                                            //     notification: false,
                                            //     id: item?.id ?? '',
                                            //     seriesId: "",
                                            //     isLast: false,
                                            //     isFirst: false,
                                            //     isOverride: false,
                                            //     isRedo: false,
                                            //   ));
                                            //   return;
                                            // } else {
                                            //   context
                                            //       .pushRoute(TextAnimationRoute(
                                            //     notification: false,
                                            //     isFirst: false,
                                            //     isLast: false,
                                            //     id: item?.id ?? '',
                                            //     seriesId: "",
                                            //     isOverride: false,
                                            //     isRedo: false,
                                            //   ));
                                            //   return;
                                            // }
                                          },
                                          textTheme: textTheme,
                                          isFavourite: true,
                                          url:
                                              "Find this item in The Mental Fitnes Gym App.",
                                        );
                                      },
                                      separatorBuilder:
                                          (BuildContext context, int index) {
                                        return const Gap(24);
                                      },
                                    ),
                            ),
                          ],
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          );
        }
        return const SizedBox(
          child: Center(
            child: Text('No items to show!'),
          ),
        );
      },
    );
  }
}

class Item {
  final String title;
  final String subtitle;
  final int duration;
  final String additionalInfo; // Example of an unused property

  Item({
    required this.title,
    required this.subtitle,
    required this.duration,
    this.additionalInfo = '',
  });
}

class Menu {
  final String name;
  bool checked;
  int selectedVenue;
  Menu({
    required this.name,
    required this.checked,
    required this.selectedVenue,
  });
}
