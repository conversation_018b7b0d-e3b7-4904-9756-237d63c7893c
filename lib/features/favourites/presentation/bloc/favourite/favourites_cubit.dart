import 'package:bloc/bloc.dart';
import 'package:equatable/equatable.dart';
import 'package:gotcha_mfg_app/features/favourites/data/models/get_favourites.dart';
import 'package:gotcha_mfg_app/features/favourites/data/models/not_interest_model.dart';
import 'package:gotcha_mfg_app/features/favourites/domain/usecases/not_interest_usecase.dart';

import '../../../data/models/favourite_request_model.dart';
import '../../../data/models/not_interest_response.dart';
import '../../../domain/usecases/add_favourites_usecases.dart';
import '../../../domain/usecases/get_filtered_favourites.dart';

part 'favourites_state.dart';

class FavouritesCubit extends Cubit<GetFavouriteState> {
  FavouritesCubit(
    this._getFavouritesUseCase,
    this._addFavouritesUseCase,
    this._notInterestUseCase,
  ) : super(GetfavouriteInitial());

  final GetFilteredFavouriteUseCase _getFavouritesUseCase;
  final AddFavoritesUseCase _addFavouritesUseCase;
  final NotInterestUseCase _notInterestUseCase;

  Future<void> getFavourites(FilteredFavouriteResponseParams params) async {
    emit(FavouritesLoading());
    final favourites = await _getFavouritesUseCase.call(params);
    if (favourites.isSuccess) {
      emit(FavouritesLoaded(
        favouritesResponse: favourites.data!,
      ));
    } else {
      emit(FavouritesError(favourites.error!));
    }
  }

  Future<void> toggleFavorites(FavouriteRequest request) async {
    emit(FavouritesLoading());
    final result = await _addFavouritesUseCase.call(request);
    if (result.isSuccess) {
      emit(const FavouritesToggled());
    } else {
      emit(FavouritesError(result.error!));
    }
  }

  Future<void> notInterested(NotInterestRequestModel request) async {
    emit(FavouritesLoading());
    final result = await _notInterestUseCase.call(request);
    if (result.isSuccess) {
      emit(FeatureNotInterestedSuccess(result.data!));
    } else {
      emit(FavouritesError(result.error ?? 'Unknown error'));
    }
  }
}
