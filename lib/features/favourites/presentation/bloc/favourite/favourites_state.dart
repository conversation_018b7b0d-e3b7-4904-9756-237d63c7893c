part of 'favourites_cubit.dart';

sealed class GetFavouriteState extends Equatable {
  const GetFavouriteState();

  @override
  List<Object> get props => [];
}

final class GetfavouriteInitial extends GetFavouriteState {}

final class FavouritesLoading extends GetFavouriteState {}

final class FavouritesLoaded extends GetFavouriteState {
  final FavouritesFilter? favouritesResponse;

  const FavouritesLoaded({
    required this.favouritesResponse,
  });

  @override
  List<Object> get props => [
        favouritesResponse ?? '',
      ];
}

final class FavouritesError extends GetFavouriteState {
  final String message;

  const FavouritesError(this.message);

  @override
  List<Object> get props => [message];
}

final class FavouritesToggled extends GetFavouriteState {
  const FavouritesToggled();
}

final class FeatureNotInterestedSuccess extends GetFavouriteState {
  const FeatureNotInterestedSuccess(this.data);

  final NotInterestResponse data;
}
