import 'package:gotcha_mfg_app/features/favourites/data/data_sources/favourite_remote_data_source.dart';
import 'package:gotcha_mfg_app/features/favourites/data/models/get_favourites.dart';
import 'package:gotcha_mfg_app/features/favourites/data/models/not_interest_model.dart';
import 'package:gotcha_mfg_app/features/favourites/data/models/not_interest_response.dart';
import 'package:gotcha_mfg_app/features/favourites/domain/repositories/favourites_repository.dart';
import 'package:gotcha_mfg_app/features/favourites/domain/usecases/get_filtered_favourites.dart';
import 'package:gotcha_mfg_app/shared/models/result.dart';

import '../models/add_favourites.dart';
import '../models/favourite_request_model.dart';

class FavouriteRepositoryImpl implements FavouritesRepository {
  FavouriteRepositoryImpl(this._remoteDataSource);

  final FavouriteRemoteDataSource _remoteDataSource;
  @override
  Future<Result<FavouritesFilter>> getFilteredFavourites(
      FilteredFavouriteResponseParams params) async {
    return await _remoteDataSource.getFilteredFavourites(params);
  }

  @override
  Future<Result<AddFavourites>> addFavorites(FavouriteRequest request) async {
    return await _remoteDataSource.addFavorites(request);
  }

  @override
  Future<Result<NotInterestResponse>> notInterested(
      NotInterestRequestModel request) async {
    return await _remoteDataSource.notInterested(request);
  }
}
