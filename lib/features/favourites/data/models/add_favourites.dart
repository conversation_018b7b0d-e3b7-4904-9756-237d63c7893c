import 'dart:convert';

class AddFavourites {
  final String? message;
  final String? status;
  final dynamic data;

  AddFavourites({
    this.message,
    this.status,
    this.data,
  });

  AddFavourites copyWith({
    String? message,
    String? status,
    dynamic data,
  }) =>
      AddFavourites(
        message: message ?? this.message,
        status: status ?? this.status,
        data: data ?? this.data,
      );

  factory AddFavourites.fromRawJson(String str) =>
      AddFavourites.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory AddFavourites.fromJson(Map<String, dynamic> json) => AddFavourites(
        message: json["message"],
        status: json["status"],
        data: json["data"],
      );

  Map<String, dynamic> toJson() => {
        "message": message,
        "status": status,
        "data": data,
      };
}
