import 'dart:convert';

class FavouritesFilter {
  final String? message;
  final String? status;
  final List<Datum>? data;

  FavouritesFilter({
    this.message,
    this.status,
    this.data,
  });

  FavouritesFilter copyWith({
    String? message,
    String? status,
    List<Datum>? data,
  }) =>
      FavouritesFilter(
        message: message ?? this.message,
        status: status ?? this.status,
        data: data ?? this.data,
      );

  factory FavouritesFilter.fromRawJson(String str) =>
      FavouritesFilter.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory FavouritesFilter.fromJson(Map<String, dynamic> json) =>
      FavouritesFilter(
        message: json["message"],
        status: json["status"],
        data: json["data"] == null
            ? []
            : List<Datum>.from(json["data"]!.map((x) => Datum.fromJson(x))),
      );

  Map<String, dynamic> toJson() => {
        "message": message,
        "status": status,
        "data": data == null
            ? []
            : List<dynamic>.from(data!.map((x) => x.toJson())),
      };
}

class Datum {
  final String? id;
  final String? title;
  final String? mediaDuration;
  final String? thumbnailUrl;
  final String? categoryName;
  final String? exerciseType;
  final String? mediaType;
  final bool? isFavorite;
  final bool? isExercise;

  Datum({
    this.id,
    this.title,
    this.mediaDuration,
    this.thumbnailUrl,
    this.categoryName,
    this.exerciseType,
    this.mediaType,
    this.isFavorite,
    this.isExercise,
  });

  Datum copyWith({
    String? id,
    String? title,
    String? mediaDuration,
    String? thumbnailUrl,
    String? categoryName,
    String? exerciseType,
    String? mediaType,
    bool? isFavorite,
    bool? isExercise,
  }) =>
      Datum(
        id: id ?? this.id,
        title: title ?? this.title,
        mediaDuration: mediaDuration ?? this.mediaDuration,
        thumbnailUrl: thumbnailUrl ?? this.thumbnailUrl,
        categoryName: categoryName ?? this.categoryName,
        exerciseType: exerciseType ?? this.exerciseType,
        mediaType: mediaType ?? this.mediaType,
        isFavorite: isFavorite ?? this.isFavorite,
        isExercise: isExercise ?? this.isExercise,
      );

  factory Datum.fromRawJson(String str) => Datum.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory Datum.fromJson(Map<String, dynamic> json) => Datum(
        id: json["id"],
        title: json["title"],
        mediaDuration: json["media_duration"],
        thumbnailUrl: json["thumbnail_url"],
        categoryName: json["category_name"],
        exerciseType: json["exercise_type"],
        mediaType: json["media_type"],
        isFavorite: json["is_favorite"],
        isExercise: json["is_exercise"],
      );

  Map<String, dynamic> toJson() => {
        "id": id,
        "title": title,
        "media_duration": mediaDuration,
        "thumbnail_url": thumbnailUrl,
        "category_name": categoryName,
        "exercise_type": exerciseType,
        "media_type": mediaType,
        "is_favorite": isFavorite,
        "is_exercise": isExercise,
      };
}
