class FavouriteRequest {
  final String id; // Add an id field
  final bool isExercise;

  FavouriteRequest({required this.id, required this.isExercise});

  // Factory constructor to create a FavouriteRequest from JSON
  factory FavouriteRequest.fromJson(Map<String, dynamic> json) {
    return FavouriteRequest(
      id: json['id'] ?? '', // Default to an empty string if id is not provided
      isExercise: json['isExercise'] ?? false,
    );
  }

  // Method to convert FavouriteRequest to JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id, // Include id in the JSON representation
      'isExercise': isExercise,
    };
  }
}
