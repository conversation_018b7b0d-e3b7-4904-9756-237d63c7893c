class NotInterestRequestModel {
  final String activityId;
  final String type;

  NotInterestRequestModel({
    required this.activityId,
    required this.type,
  });

  Map<String, dynamic> toJson() {
    return {
      'activity_id': activityId,
      'type': type,
    };
  }

  factory NotInterestRequestModel.fromJson(Map<String, dynamic> json) {
    return NotInterestRequestModel(
      activityId: json['activity_id'],
      type: json['type'],
    );
  }
}
