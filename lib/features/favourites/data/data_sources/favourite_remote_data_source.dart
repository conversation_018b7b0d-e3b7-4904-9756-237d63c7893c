import 'package:dio/dio.dart';
import 'package:firebase_crashlytics/firebase_crashlytics.dart';
import 'package:gotcha_mfg_app/features/favourites/data/models/not_interest_model.dart';
import 'package:gotcha_mfg_app/features/favourites/data/models/not_interest_response.dart';
import 'package:gotcha_mfg_app/features/favourites/domain/usecases/get_filtered_favourites.dart';
import 'package:gotcha_mfg_app/shared/models/result.dart';

import '../models/add_favourites.dart';
import '../models/favourite_request_model.dart';
import '../models/get_favourites.dart';

abstract class FavouriteRemoteDataSource {
  Future<Result<FavouritesFilter>> getFilteredFavourites(
      FilteredFavouriteResponseParams params);
  Future<Result<AddFavourites>> addFavorites(FavouriteRequest request);
  Future<Result<NotInterestResponse>> notInterested(
      NotInterestRequestModel request);
}

class FavouriteDataSourceImpl implements FavouriteRemoteDataSource {
  FavouriteDataSourceImpl(this._dio);

  final Dio _dio;

  @override
  Future<Result<FavouritesFilter>> getFilteredFavourites(
      FilteredFavouriteResponseParams params) async {
    try {
      final response = await _dio.get(
          '/app/favorite?isAll=${params.isAll}&isExercise=${params.isExercise}&isWorkout=${params.isWorkout}');
      if (response.statusCode == 200) {
        final data = response.data;
        final filteredResponse = FavouritesFilter.fromJson(data);
        return Result.success(filteredResponse);
      } else {
        return Result.failure(
            '${response.statusCode} - ${response.data['message']}');
      }
    } catch (e) {
      FirebaseCrashlytics.instance.recordError(e, StackTrace.current);
      return Result.failure(
          'An error occurred during getting filtered exercises: ${e.toString()}');
    }
  }

  @override
  Future<Result<AddFavourites>> addFavorites(FavouriteRequest request) async {
    var params = {"isExercise": request.isExercise};
    try {
      final response =
          await _dio.post('/app/favorite/${request.id}', data: params);
      if (response.statusCode == 200) {
        final data = response.data;
        final favouriteResponse = AddFavourites.fromJson(data);
        return Result.success(favouriteResponse);
      } else {
        return Result.failure(
            '${response.statusCode} - ${response.data['message']}');
      }
    } catch (e) {
      FirebaseCrashlytics.instance.recordError(e, StackTrace.current);
      return Result.failure(
          'An error occurred during calling Favorites: ${e.toString()}');
    }
  }

  @override
  Future<Result<NotInterestResponse>> notInterested(
      NotInterestRequestModel request) async {
    try {
      final response = await _dio.post(
        '/app/exercise/not/interested',
        data: request.toJson(), // Convert request model to JSON
      );

      if (response.statusCode == 200) {
        final data = response.data;
        final notInterestResponse = NotInterestResponse.fromJson(data);

        return Result.success(notInterestResponse);
      } else {
        return Result.failure(
            '${response.statusCode} - ${response.data['message']}');
      }
    } catch (e) {
      return Result.failure(
          'An error occurred during calling not interested: ${e.toString()}');
    }
  }
}
