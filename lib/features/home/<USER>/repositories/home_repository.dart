import 'package:gotcha_mfg_app/features/home/<USER>/models/check_in_request.dart';
import 'package:gotcha_mfg_app/features/home/<USER>/models/check_in_response.dart';
import 'package:gotcha_mfg_app/features/home/<USER>/models/emotions_detail_response.dart';
import 'package:gotcha_mfg_app/features/home/<USER>/models/emotions_response.dart';
import 'package:gotcha_mfg_app/features/home/<USER>/models/exercises_response.dart';
import 'package:gotcha_mfg_app/features/home/<USER>/usecases/get_detail_emotion.dart';
import 'package:gotcha_mfg_app/shared/models/result.dart';

import '../../data/models/common_response.dart';
import '../../data/models/continue_workout.dart';
import '../../data/models/delete_request.dart';
import '../../data/models/viewed_status_response.dart';

abstract class HomeRepository {
  Future<Result<ExercisesResponse>> getExercises();
  Future<Result<EmotionsResponse>> getEmotions();
  Future<Result<EmotionsDetailResponse>> getEmotionsDetail(
      EmotionDetailParams params);

  Future<Result<CheckInResponse>> checkIn(CheckInRequest request);
  Future<Result<ContinueWorkoutResponse>> getRecentUncompletedWorkout();
  Future<Result<ViewedStatusResponse>> getViewedStatus();
  Future<Result<DeleteEmotionResponse>> deleteCheckIn(DeleteParams params);
}
