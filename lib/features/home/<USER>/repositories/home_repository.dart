import 'package:gotcha_mfg_app/features/home/<USER>/data_sources/home_data_source.dart';
import 'package:gotcha_mfg_app/features/home/<USER>/models/check_in_request.dart';
import 'package:gotcha_mfg_app/features/home/<USER>/models/check_in_response.dart';
import 'package:gotcha_mfg_app/features/home/<USER>/models/common_response.dart';
import 'package:gotcha_mfg_app/features/home/<USER>/models/emotions_response.dart';
import 'package:gotcha_mfg_app/features/home/<USER>/models/exercises_response.dart';
import 'package:gotcha_mfg_app/features/home/<USER>/repositories/home_repository.dart';
import 'package:gotcha_mfg_app/features/home/<USER>/usecases/get_detail_emotion.dart';
import 'package:gotcha_mfg_app/shared/models/result.dart';

import '../models/continue_workout.dart';
import '../models/delete_request.dart';
import '../models/emotions_detail_response.dart';
import '../models/viewed_status_response.dart';

class HomeRepositoryImpl extends HomeRepository {
  final HomeDataSource _dataSource;

  HomeRepositoryImpl(this._dataSource);

  @override
  Future<Result<ExercisesResponse>> getExercises() async {
    return await _dataSource.getExercises();
  }

  @override
  Future<Result<EmotionsResponse>> getEmotions() async {
    return await _dataSource.getEmotions();
  }

  @override
  Future<Result<EmotionsDetailResponse>> getEmotionsDetail(
      EmotionDetailParams params) async {
    return await _dataSource.getDetailEmotions(params);
  }

  @override
  Future<Result<CheckInResponse>> checkIn(CheckInRequest request) {
    return _dataSource.checkIn(request);
  }

  @override
  Future<Result<ContinueWorkoutResponse>> getRecentUncompletedWorkout() async {
    return await _dataSource.getRecentUncompletedWorkout();
  }

  @override
  Future<Result<ViewedStatusResponse>> getViewedStatus() async {
    return await _dataSource.getViewedStatus();
  }

  @override
  Future<Result<DeleteEmotionResponse>> deleteCheckIn(
      DeleteParams params) async {
    return await _dataSource.deleteCheckIn(params);
  }
}
