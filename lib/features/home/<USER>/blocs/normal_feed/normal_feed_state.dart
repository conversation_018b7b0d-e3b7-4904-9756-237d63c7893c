part of 'normal_feed_cubit.dart';

sealed class NormalFeedState extends Equatable {
  const NormalFeedState();

  @override
  List<Object> get props => [];
}

final class NormalFeedInitial extends NormalFeedState {}

final class NormalFeedLoading extends NormalFeedState {}

final class NormalFeedLoaded extends NormalFeedState {
  final EmotionsResponse? emotionsResponse;
  final ExercisesResponse? exercisesResponse;
  final ContinueWorkoutResponse? continueWorkout;

  const NormalFeedLoaded({
    required this.continueWorkout,
    required this.emotionsResponse,
    required this.exercisesResponse,
  });

  NormalFeedLoaded copyWith(
          {EmotionsResponse? emotionsResponse,
          ExercisesResponse? exercisesResponse,
          ContinueWorkoutResponse? continueWorkout}) =>
      NormalFeedLoaded(
        continueWorkout: continueWorkout ?? this.continueWorkout,
        emotionsResponse: emotionsResponse ?? this.emotionsResponse,
        exercisesResponse: exercisesResponse ?? this.exercisesResponse,
      );
}

final class NormalFeedError extends NormalFeedState {
  final String error;
  const NormalFeedError(this.error);
}
