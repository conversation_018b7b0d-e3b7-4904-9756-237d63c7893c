import 'package:bloc/bloc.dart';
import 'package:equatable/equatable.dart';
import 'package:gotcha_mfg_app/core/usecase/usecase.dart';
import 'package:gotcha_mfg_app/features/home/<USER>/models/exercises_response.dart';
import 'package:gotcha_mfg_app/features/home/<USER>/usecases/get_emotions.dart';
import 'package:gotcha_mfg_app/features/home/<USER>/usecases/get_exercises.dart';

import '../../../data/models/continue_workout.dart';
import '../../../data/models/emotions_response.dart';
import '../../../domain/usecases/continue_workout_usecase.dart';
import '../../../domain/usecases/get_detail_emotion.dart';

part 'normal_feed_state.dart';

class NormalFeedCubit extends Cubit<NormalFeedState> {
  final GetEmotionsUsecase _getEmotionsUsecase;
  final GetDetailEmotionsUsecase _getDetailEmotionsUsecase;
  final GetExercisesUsecase _getExercisesUsecase;
  final GetRecentUncompletedWorkoutUseCase _getRecentUncompletedWorkoutUseCase;

  NormalFeedCubit(
    this._getEmotionsUsecase,
    this._getDetailEmotionsUsecase,
    this._getExercisesUsecase,
    this._getRecentUncompletedWorkoutUseCase,
  ) : super(NormalFeedInitial());

  Future<void> getData() async {
    emit(NormalFeedLoading());
    final emotionsResult = await _getEmotionsUsecase.call(NoParams());
    final exercisesResult = await _getExercisesUsecase.call(NoParams());
    final continueWorkoutResult =
        await _getRecentUncompletedWorkoutUseCase.call(NoParams());
    if (emotionsResult.isSuccess &&
        exercisesResult.isSuccess &&
        continueWorkoutResult.isSuccess) {
      emit(
        NormalFeedLoaded(
          continueWorkout: continueWorkoutResult.data!,
          emotionsResponse: emotionsResult.data!,
          exercisesResponse: exercisesResult.data!,
        ),
      );
    } else {
      if (emotionsResult.isFailure) {
        emit(NormalFeedError(emotionsResult.error!));
      }
      if (exercisesResult.isFailure) {
        emit(NormalFeedError(exercisesResult.error!));
      }
    }
  }
}
