part of 'bottom_navbar_cubit.dart';

// sealed class BottomNavbarState extends Equatable {
//   const BottomNavbarState();

//   @override
//   List<Object> get props => [];
// }

// final class BottomNavbarInitial extends BottomNavbarState {}

// final class BottomNavbarLoaded extends BottomNavbarState {
//   final List<em.Type>? emotions;
//   const BottomNavbarLoaded({required this.emotions});
// }
// final class BottomNavbarModeLoaded extends BottomNavbarState {
//   final int? mode;
//   const BottomNavbarModeLoaded({required this.mode});
// }

sealed class BottomNavbarState extends Equatable {
  const BottomNavbarState();

  @override
  List<Object?> get props => [];
}

final class BottomNavbarInitial extends BottomNavbarState {}

final class BottomNavbarLoaded extends BottomNavbarState {
  final List<em.Type>? emotions;

  const BottomNavbarLoaded({required this.emotions});

  @override
  List<Object?> get props => [emotions];
}

final class BottomNavbarModeLoaded extends BottomNavbarState {
  final int? mode;

  const BottomNavbarModeLoaded({required this.mode});

  @override
  List<Object?> get props => [mode];
}

final class SelectedItemLoaded extends BottomNavbarState {
  final int selected;

  const SelectedItemLoaded({required this.selected});

  @override
  List<Object?> get props => [selected];
}
