import 'package:bloc/bloc.dart';
import 'package:equatable/equatable.dart';
import 'package:gotcha_mfg_app/features/home/<USER>/models/emotions_response.dart'
    as em;

part 'bottom_navbar_state.dart';

class BottomNavbarCubit extends Cubit<BottomNavbarState> {
  BottomNavbarCubit() : super(BottomNavbarInitial());
  // void isHome(Bool isHome) {
  //   emit(BottomNavbarLoaded(val: isHome));
  // }
  void refresh(List<em.Type>? emotions) {
    var updatedEmotions = emotions;
    emit(BottomNavbarLoaded(emotions: updatedEmotions));
  }

  void modeUpdate(int? mode) {
    var updatedmode = mode;
    emit(BottomNavbarModeLoaded(mode: updatedmode));
  }

  void itemUpdate(int selected) {
    var updatedselected = selected;
    emit(SelectedItemLoaded(selected: updatedselected));
  }

  void goToDiscoverPage() {}
}
