import 'package:bloc/bloc.dart';
import 'package:equatable/equatable.dart';
import 'package:gotcha_mfg_app/core/usecase/usecase.dart';
import 'package:gotcha_mfg_app/features/home/<USER>/models/exercises_response.dart';
import 'package:gotcha_mfg_app/features/home/<USER>/usecases/get_exercises.dart';

import '../../../data/models/viewed_status_response.dart';
import '../../../domain/usecases/get_viewed_status_use_case.dart';

part 'welcome_feed_state.dart';

class WelcomeFeedCubit extends Cubit<WelcomeFeedState> {
  final GetExercisesUsecase _getExercisesUsecase;
  final GetViewedStatusUseCase _getViewedStatusUseCase;

  WelcomeFeedCubit(
    this._getExercisesUsecase,
    this._getViewedStatusUseCase,
  ) : super(WelcomeFeedInitial());

  Future<void> getExercises() async {
    emit(WelcomeFeedLoading());
    final result = await _getExercisesUsecase.call(NoParams());
    if (result.isSuccess) {
      emit(WelcomeFeedLoaded(
        result.data!,
        null,
      ));
    } else {
      emit(WelcomeFeedError(result.error!));
    }
  }

  Future<void> getViewedStatus() async {
    var state = this.state as WelcomeFeedLoaded;
    emit(WelcomeFeedLoading());
    final result = await _getViewedStatusUseCase.call(NoParams());
    if (result.isSuccess) {
      emit(state.copyWith(viewedStatusResponse: result.data!));
    } else {
      emit(state);
    }
  }
}
