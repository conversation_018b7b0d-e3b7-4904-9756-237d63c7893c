part of 'welcome_feed_cubit.dart';

sealed class WelcomeFeedState extends Equatable {
  const WelcomeFeedState();

  @override
  List<Object> get props => [];
}

final class WelcomeFeedInitial extends WelcomeFeedState {}

final class WelcomeFeedLoading extends WelcomeFeedState {}

final class WelcomeFeedLoaded extends WelcomeFeedState {
  final ExercisesResponse exercisesResponse;
  final ViewedStatusResponse? viewedStatusResponse;

  const WelcomeFeedLoaded(
    this.exercisesResponse,
    this.viewedStatusResponse,
  );

  WelcomeFeedLoaded copyWith({
    ExercisesResponse? exercisesResponse,
    ViewedStatusResponse? viewedStatusResponse,
  }) {
    return WelcomeFeedLoaded(
      exercisesResponse ?? this.exercisesResponse,
      viewedStatusResponse ?? this.viewedStatusResponse,
    );
  }
}

final class WelcomeFeedError extends WelcomeFeedState {
  final String error;
  const WelcomeFeedError(this.error);
}

final class ExercisesLoaded extends WelcomeFeedState {}

final class ExercisesError extends WelcomeFeedState {}
