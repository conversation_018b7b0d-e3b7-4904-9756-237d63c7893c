// import 'package:bloc/bloc.dart';
// import 'package:gotcha_mfg_app/features/home/<USER>/models/check_in_request.dart';

// part 'checkin_data_state.dart';

// class CheckinDataCubit extends Cubit<CheckinDataState> {
//   CheckinDataCubit() : super(const CheckInDataLoaded(list: []));

//   void addQuestion(Question question) {
//     var updatedQuestions =
//         List<Question>.from((state as CheckInDataLoaded).list)..add(question);
//     emit(CheckInDataLoaded(list: updatedQuestions));
//   }

//   void remove(Question question) {
//     final updatedQuestions =
//         List<Question>.from((state as CheckInDataLoaded).list)
//           ..removeWhere((q) => (q.answerId == question.answerId && q.questionId == question.questionId));

//     emit(CheckInDataLoaded(list: updatedQuestions));
//   }

//   void clear() {
//     emit(const CheckInDataLoaded(list: []));
//   }
// }

import 'package:bloc/bloc.dart';
import 'package:gotcha_mfg_app/features/home/<USER>/models/check_in_request.dart';

part 'checkin_data_state.dart';

class CheckinDataCubit extends Cubit<CheckinDataState> {
  CheckinDataCubit() : super(const CheckInDataLoaded(list: []));

  void addQuestion(Question question) {
    final currentState = state as CheckInDataLoaded;
    final currentQuestions = currentState.list;

    // Check if a question with the same questionId and answerId already exists
    bool alreadyExists = currentQuestions.any((q) =>
        q.questionId == question.questionId && q.answerId == question.answerId);

    if (!alreadyExists) {
      var updatedQuestions = List<Question>.from(currentQuestions)
        ..add(question);
      emit(CheckInDataLoaded(list: updatedQuestions));
    } else {
      // If the question already exists, you might want to emit the same state
      // or a new state with the same list to indicate no change.
      // For simplicity, we will emit a new state with the same list.
      emit(CheckInDataLoaded(list: currentQuestions));
    }
  }

  void addOrUpdateNote(Question question) {
    final currentState = state as CheckInDataLoaded;
    final currentQuestions = currentState.list;
    final isNoteEmpty = question.note?.trim().isEmpty == true;
    final existingIndex =
        currentQuestions.indexWhere((q) => q.questionId == question.questionId);

    List<Question> updatedQuestions =
        List<Question>.from(currentQuestions); // Prepare for potential updates

    if (isNoteEmpty) {
      if (existingIndex != -1) {
        // Remove existing question if note is empty
        updatedQuestions.removeAt(existingIndex);
        emit(CheckInDataLoaded(list: updatedQuestions));
      } else {
        // If note is empty and question doesn't exist, no change needed
        emit(CheckInDataLoaded(list: currentQuestions)); // Emit current state
      }
    } else {
      // Note is NOT empty
      if (existingIndex != -1) {
        // Update existing question's note
        final existingQuestion = updatedQuestions[existingIndex];
        updatedQuestions[existingIndex] =
            existingQuestion.copyWith(note: question.note);
        emit(CheckInDataLoaded(list: updatedQuestions));
      } else {
        // Add new question
        updatedQuestions.add(question);
        emit(CheckInDataLoaded(list: updatedQuestions));
      }
    }
  }

  void remove(Question question) {
    final updatedQuestions =
        List<Question>.from((state as CheckInDataLoaded).list)
          ..removeWhere((q) => (q.answerId == question.answerId &&
              q.questionId == question.questionId));

    emit(CheckInDataLoaded(list: updatedQuestions));
  }

  void clear() {
    emit(const CheckInDataLoaded(list: []));
  }
}
