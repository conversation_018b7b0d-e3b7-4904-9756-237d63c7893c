part of 'check_in_cubit.dart';

sealed class CheckInState extends Equatable {
  const CheckInState();

  @override
  List<Object> get props => [];
}

final class CheckInInitial extends CheckInState {}

final class CheckInLoading extends CheckInState {}

final class DeleteSuccess extends CheckInState {}

final class CheckInLoaded extends CheckInState {
  final EmotionsDetailResponse data;
  final List<Question>? questions;

  const CheckInLoaded(this.data, this.questions);

  CheckInLoaded copyWith({
    EmotionsDetailResponse? data,
    List<Question>? questions,
  }) {
    return CheckInLoaded(
      data ?? this.data,
      questions ?? this.questions,
    );
  }
}

final class CheckInError extends CheckInState {
  final String error;

  const CheckInError(this.error);
}

final class CheckInSuccess extends CheckInState {
  final CheckInResponse check;

  const CheckInSuccess(this.check);
}
