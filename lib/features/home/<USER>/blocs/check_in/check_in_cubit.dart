import 'package:bloc/bloc.dart';
import 'package:equatable/equatable.dart';
import 'package:gotcha_mfg_app/features/home/<USER>/models/check_in_request.dart';
import 'package:gotcha_mfg_app/features/home/<USER>/models/emotions_detail_response.dart';
import 'package:gotcha_mfg_app/features/home/<USER>/usecases/check_in.dart';
import 'package:gotcha_mfg_app/features/home/<USER>/usecases/get_detail_emotion.dart';

import '../../../data/models/check_in_response.dart';
import '../../../data/models/delete_request.dart';
import '../../../domain/usecases/delete_usecase.dart';

part 'check_in_state.dart';

class CheckInCubit extends Cubit<CheckInState> {
  CheckInCubit(
    this._getDetailEmotionsUsecase,
    this._checkInUsecase,
    this._deleteCheckInUseCase,
  ) : super(CheckInInitial());

  final GetDetailEmotionsUsecase _getDetailEmotionsUsecase;
  final CheckInUsecase _checkInUsecase;
  final DeleteCheckInUseCase _deleteCheckInUseCase;

  List<Question> selectedQuestions = [];

  Future<void> getDetailEmotions(String id) async {
    emit(CheckInLoading());
    final result = await _getDetailEmotionsUsecase(EmotionDetailParams(id: id));
    if (result.isSuccess) {
      emit(CheckInLoaded(result.data!, null));
    } else {
      emit(CheckInError(result.error!));
    }
  }

  Future<void> checkIn(CheckInRequest request) async {
    var previousState = state;
    emit(CheckInLoading());
    final result = await _checkInUsecase(request);
    if (result.isSuccess) {
      emit(CheckInSuccess(result.data!));
      emit(previousState);
    } else {
      emit(CheckInError(result.error!));
    }
  }

  void addQuestion(Question question) {
    if (selectedQuestions.contains(question)) {
      selectedQuestions.remove(question);
    } else {
      selectedQuestions.add(question);
    }
  }

  void removeQuestion(Question question) {
    if (!selectedQuestions.contains(question)) return;
    selectedQuestions.remove(question);
  }

  void clearQuestions() {
    selectedQuestions = [];
  }

  Future<void> deleteCheckIn(DeleteParams params) async {
    emit(CheckInLoading());
    final result = await _deleteCheckInUseCase.call(params);
    if (result.isSuccess) {
      emit(DeleteSuccess()); //Can send data in state here if needed
    } else {
      emit(CheckInError(result.data?.message ?? "Unknown error"));
    }
  }
}
