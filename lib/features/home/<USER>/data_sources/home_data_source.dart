import 'package:dio/dio.dart';
import 'package:firebase_crashlytics/firebase_crashlytics.dart';
import 'package:gotcha_mfg_app/features/home/<USER>/models/check_in_request.dart';
import 'package:gotcha_mfg_app/features/home/<USER>/models/check_in_response.dart';
import 'package:gotcha_mfg_app/features/home/<USER>/models/emotions_response.dart';
import 'package:gotcha_mfg_app/features/home/<USER>/models/exercises_response.dart';
import 'package:gotcha_mfg_app/features/home/<USER>/usecases/get_detail_emotion.dart';
import 'package:gotcha_mfg_app/shared/models/result.dart';

import '../models/common_response.dart';
import '../models/continue_workout.dart';
import '../models/delete_request.dart';
import '../models/emotions_detail_response.dart';
import '../models/viewed_status_response.dart';

abstract class HomeDataSource {
  Future<Result<ExercisesResponse>> getExercises();
  Future<Result<EmotionsResponse>> getEmotions();
  Future<Result<EmotionsDetailResponse>> getDetailEmotions(
      EmotionDetailParams params);
  Future<Result<CheckInResponse>> checkIn(CheckInRequest request);
  Future<Result<ContinueWorkoutResponse>> getRecentUncompletedWorkout();
  Future<Result<ViewedStatusResponse>> getViewedStatus();
  Future<Result<DeleteEmotionResponse>> deleteCheckIn(DeleteParams params);
}

class HomeDataSourceImpl implements HomeDataSource {
  final Dio _dio;

  HomeDataSourceImpl(this._dio);

  @override
  Future<Result<ExercisesResponse>> getExercises() async {
    try {
      final response = await _dio.get('/app/exercise');
      if (response.statusCode == 200) {
        final data = response.data;
        final exercisesResponse = ExercisesResponse.fromJson(data);
        return Result.success(exercisesResponse);
      } else {
        return Result.failure(
            '${response.statusCode} - ${response.data['message']}');
      }
    } catch (e) {
      FirebaseCrashlytics.instance.recordError(e, StackTrace.current);
      return Result.failure(
          'An error occurred during getting exercises: ${e.toString()}');
    }
  }

  @override
  Future<Result<EmotionsResponse>> getEmotions() async {
    try {
      final response = await _dio.get('/app/check-in/types');
      if (response.statusCode == 200) {
        final data = response.data;
        final emotionsresponse = EmotionsResponse.fromJson(data);
        return Result.success(emotionsresponse);
      } else {
        return Result.failure(
            '${response.statusCode} - ${response.data['message']}');
      }
    } catch (e) {
      FirebaseCrashlytics.instance.recordError(e, StackTrace.current);
      return Result.failure(
          'An error occurred during getting feelings: ${e.toString()}');
    }
  }

  @override
  Future<Result<EmotionsDetailResponse>> getDetailEmotions(
      EmotionDetailParams params) async {
    try {
      final response =
          await _dio.get('/app/check-in/questions/${params.id}/false');
      if (response.statusCode == 200) {
        final data = response.data;
        final emotionsdeatilresponse = EmotionsDetailResponse.fromJson(data);
        return Result.success(emotionsdeatilresponse);
      } else {
        return Result.failure(
            '${response.statusCode} - ${response.data['message']}');
      }
    } catch (e) {
      FirebaseCrashlytics.instance.recordError(e, StackTrace.current);
      return Result.failure(
          'An error occurred during getting emotions: ${e.toString()}');
    }
  }

  @override
  Future<Result<CheckInResponse>> checkIn(CheckInRequest request) async {
    try {
      final response = await _dio.post('/app/check-in', data: request);
      if (response.statusCode == 200) {
        final data = response.data;
        final checkinresponse = CheckInResponse.fromJson(data);
        return Result.success(checkinresponse);
      } else {
        return Result.failure(
            '${response.statusCode} - ${response.data['message']}');
      }
    } catch (e) {
      FirebaseCrashlytics.instance.recordError(e, StackTrace.current);
      return Result.failure(
          'An error occurred during check-in: ${e.toString()}');
    }
  }

  @override
  Future<Result<ContinueWorkoutResponse>> getRecentUncompletedWorkout() async {
    try {
      final response = await _dio.get('/app/workout/recent/uncompleted');
      if (response.statusCode == 200) {
        final data = response.data;
        final continueWorkoutResponse = ContinueWorkoutResponse.fromJson(data);
        return Result.success(continueWorkoutResponse);
      } else {
        return Result.failure(
            '${response.statusCode} - ${response.data['message']}');
      }
    } catch (e) {
      FirebaseCrashlytics.instance.recordError(e, StackTrace.current);
      return Result.failure(
          'An error occurred during getting recent uncompleted workout: ${e.toString()}');
    }
  }

  @override
  Future<Result<ViewedStatusResponse>> getViewedStatus() async {
    try {
      final response = await _dio.get('/app/user/exercise/viewed/status');
      if (response.statusCode == 200) {
        final data = response.data;
        final viewedStatusResponse = ViewedStatusResponse.fromJson(data);
        return Result.success(viewedStatusResponse);
      } else {
        return Result.failure(
            '${response.statusCode} - ${response.data['message']}');
      }
    } catch (e) {
      FirebaseCrashlytics.instance.recordError(e, StackTrace.current);
      return Result.failure(
          'An error occurred during getting viewed status: ${e.toString()}');
    }
  }

  @override
  Future<Result<DeleteEmotionResponse>> deleteCheckIn(
    DeleteParams params,
  ) async {
    try {
      final response = await _dio.put(
        '/app/check-in/${params.dailyCheckInId}/${params.emotionId}',
      );

      if (response.statusCode == 200) {
        final data = response.data;
        final deleteResponse = DeleteEmotionResponse.fromJson(data);
        return Result.success(deleteResponse);
      } else {
        return Result.failure(
            '${response.statusCode} - ${response.data['message']}');
      }
    } catch (e) {
      return Result.failure(
          'An error occurred during deleting check-in: ${e.toString()}');
    }
  }
}
