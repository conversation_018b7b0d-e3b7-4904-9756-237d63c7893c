import '../../../../core/usecase/usecase.dart';
import '../../../../shared/models/result.dart';
import '../../data/models/emotions_response.dart';
import '../repositories/home_repository.dart';

class GetEmotionsUsecase
    implements UseCase<Result<EmotionsResponse>, NoParams> {
  GetEmotionsUsecase(this._repository);

  final HomeRepository _repository;

  @override
  Future<Result<EmotionsResponse>> call(NoParams params) async {
    return _repository.getEmotions();
  }
}
