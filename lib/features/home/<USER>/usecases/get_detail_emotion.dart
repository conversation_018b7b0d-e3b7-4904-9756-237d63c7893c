import 'package:gotcha_mfg_app/features/home/<USER>/models/emotions_detail_response.dart';

import '../../../../core/usecase/usecase.dart';
import '../../../../shared/models/result.dart';
import '../repositories/home_repository.dart';

class GetDetailEmotionsUsecase
    implements UseCase<Result<EmotionsDetailResponse>, EmotionDetailParams> {
  GetDetailEmotionsUsecase(this._repository);

  final HomeRepository _repository;

  @override
  Future<Result<EmotionsDetailResponse>> call(
      EmotionDetailParams params) async {
    return _repository.getEmotionsDetail(params);
  }
}

class EmotionDetailParams {
  final String id;
  EmotionDetailParams({required this.id});
}
