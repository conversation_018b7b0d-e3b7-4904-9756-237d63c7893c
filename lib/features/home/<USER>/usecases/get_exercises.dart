import 'package:gotcha_mfg_app/core/usecase/usecase.dart';
import 'package:gotcha_mfg_app/features/home/<USER>/models/exercises_response.dart';
import 'package:gotcha_mfg_app/features/home/<USER>/repositories/home_repository.dart';
import 'package:gotcha_mfg_app/shared/models/result.dart';

class GetExercisesUsecase
    implements UseCase<Result<ExercisesResponse>, NoParams> {
  GetExercisesUsecase(this._repository);

  final HomeRepository _repository;

  @override
  Future<Result<ExercisesResponse>> call(NoParams params) async {
    return _repository.getExercises();
  }
}
