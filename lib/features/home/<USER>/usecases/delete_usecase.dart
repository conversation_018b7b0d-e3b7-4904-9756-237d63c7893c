import 'package:gotcha_mfg_app/features/home/<USER>/models/common_response.dart';

import '../../../../core/usecase/usecase.dart';
import '../../../../shared/models/result.dart';
import '../../data/models/delete_request.dart';
import '../repositories/home_repository.dart';

class DeleteCheckInUseCase
    implements UseCase<Result<DeleteEmotionResponse>, DeleteParams> {
  DeleteCheckInUseCase(this._repository);

  final HomeRepository _repository;

  @override
  Future<Result<DeleteEmotionResponse>> call(DeleteParams params) async {
    return _repository.deleteCheckIn(params);
  }
}
