import 'package:gotcha_mfg_app/core/usecase/usecase.dart';
import 'package:gotcha_mfg_app/features/home/<USER>/models/check_in_request.dart';
import 'package:gotcha_mfg_app/features/home/<USER>/models/check_in_response.dart';
import 'package:gotcha_mfg_app/features/home/<USER>/repositories/home_repository.dart';
import 'package:gotcha_mfg_app/shared/models/result.dart';

class CheckInUsecase
    implements UseCase<Result<CheckInResponse>, CheckInRequest> {
  final HomeRepository repository;
  CheckInUsecase(this.repository);

  @override
  Future<Result<CheckInResponse>> call(CheckInRequest params) {
    return repository.checkIn(params);
  }
}
