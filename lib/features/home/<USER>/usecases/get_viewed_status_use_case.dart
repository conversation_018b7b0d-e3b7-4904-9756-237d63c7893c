import '../../../../core/usecase/usecase.dart';
import '../../../../shared/models/result.dart';
import '../../data/models/viewed_status_response.dart';
import '../repositories/home_repository.dart';

/// UseCase for getting viewed status
class GetViewedStatusUseCase
    implements UseCase<Result<ViewedStatusResponse>, NoParams> {
  /// Constructor
  GetViewedStatusUseCase(this._repository);

  final HomeRepository _repository;

  @override
  Future<Result<ViewedStatusResponse>> call(NoParams params) async {
    return _repository.getViewedStatus();
  }
}
