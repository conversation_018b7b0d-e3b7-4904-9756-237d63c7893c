import 'package:gotcha_mfg_app/core/usecase/usecase.dart';
import 'package:gotcha_mfg_app/features/home/<USER>/repositories/home_repository.dart';

import '../../../../shared/models/result.dart';
import '../../data/models/continue_workout.dart';

/// UseCase for get recent uncompleted workout
class GetRecentUncompletedWorkoutUseCase
    implements UseCase<Result<ContinueWorkoutResponse>, NoParams> {
  /// Constructor
  GetRecentUncompletedWorkoutUseCase(this._repository);
  final HomeRepository _repository;

  @override
  Future<Result<ContinueWorkoutResponse>> call(NoParams params) async {
    return _repository.getRecentUncompletedWorkout();
  }
}
