import 'package:flutter/material.dart';

class CheckInTitle extends StatelessWidget {
  final String text;
  final bool optionaltext;
  final bool onboarding;
  final TextStyle mainTextStyle;
  final TextStyle optionalTextStyle;

  const CheckInTitle({
    super.key,
    required this.text,
    required this.optionaltext,
    required this.onboarding,
    required this.mainTextStyle,
    required this.optionalTextStyle,
  });

  @override
  Widget build(BuildContext context) {
    return RichText(
      text: _buildTextSpan(text),
    );
  }

  TextSpan _buildTextSpan(String text) {
    // if (text.contains('(optional)')) {
    // final parts = text.split('(optional)');
    return TextSpan(
      children: [
        TextSpan(
          text: text,
          style: mainTextStyle,
        ),
        optionaltext == true
            ? TextSpan(
                text: onboarding == true ? 'Optional' : ' (optional)',
                style: optionalTextStyle.copyWith(height: 1.5),
              )
            : TextSpan(
                text: '',
                style: optionalTextStyle,
              ),
        // if (parts.length > 1) TextSpan(text: parts[1], style: mainTextStyle),
      ],
    );
    // } else {
    // return TextSpan(text: text, style: mainTextStyle);
  }
}
