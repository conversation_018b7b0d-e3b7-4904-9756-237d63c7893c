import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:gotcha_mfg_app/core/extensions/url_extensions.dart';
import 'package:gotcha_mfg_app/features/home/<USER>/models/emotions_response.dart';
import 'package:gotcha_mfg_app/features/home/<USER>/widgets/mood_widget.dart';

class MoodRow extends StatelessWidget {
  const MoodRow({
    required this.selectedItem,
    required this.onSelectedItemChanged,
    required this.moods,
    super.key,
  });

  final int selectedItem;
  final ValueChanged<int> onSelectedItemChanged;
  final List<Type> moods;

  @override
  Widget build(BuildContext context) {
    List<Type> sortedMoods = List.from(moods)
      ..sort((a, b) => (a.point ?? 0).compareTo(b.point ?? 0));
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 0),
      child: Row(
        children: [
          for (int i = 0; i < sortedMoods.length; i++) ...[
            MoodWidget(
              url: sortedMoods[i].iconUrl?.iconUrl ?? '',
              icon: Icons.sentiment_satisfied,
              label: sortedMoods[i].name ?? 'N/A',
              isSelected: selectedItem == i,
              onTap: () => onSelectedItemChanged(i),
            ),
            if (i < sortedMoods.length - 1) const Gap(7),
          ]
        ],
      ),
    );
  }
}
