import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:gotcha_mfg_app/config/theme/app_colors.dart';
import 'package:gotcha_mfg_app/core/extensions/app_text_theme_extension.dart';
import 'package:gotcha_mfg_app/core/extensions/string_extensions.dart';
import 'package:gotcha_mfg_app/core/utils/app_print.dart';
import 'package:gotcha_mfg_app/shared/widgets/network_image_indicator.dart';

class MoodWidget extends StatelessWidget {
  const MoodWidget({
    required this.icon,
    required this.label,
    required this.onTap,
    this.isSelected = false,
    super.key,
    required this.url,
  });

  final IconData icon;
  final String label;
  final String? url;
  final bool isSelected;
  final VoidCallback onTap;

  @override
  Widget build(BuildContext context) {
    info('url: $url');
    final textTheme = Theme.of(context).textTheme;
    return Expanded(
      child: GestureDetector(
        onTap: onTap,
        child: Container(
          decoration: BoxDecoration(
            color: isSelected ? AppColors.lightRed : Colors.white,
            borderRadius: BorderRadius.circular(12),
            border: Border.all(
              width: 1.5,
              color: isSelected ? AppColors.coral : Colors.white,
            ),
          ),
          height: 80,
          padding: const EdgeInsets.symmetric(
            horizontal: 0,
            vertical: 8,
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              url == null
                  ? const SizedBox()
                  : SizedBox(
                      width: 24,
                      child: NetworkImageWithIndicatorSmall(
                        imageUrl: '$url',
                        fit: BoxFit.cover,
                      ),
                    ),
              // Image.asset(
              //   label.icon,
              //   width: 24,
              //   color: AppColors.navy,
              // ),
              const Gap(8),
              Text(
                label.capitalizeFirstLetter(),
                style: textTheme.ralewayMedium.copyWith(
                  fontSize: 8,
                  color: AppColors.navy,
                ),
                textAlign: TextAlign.center,
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
              ),
            ],
          ),
        ),
      ),
    );
  }
}
