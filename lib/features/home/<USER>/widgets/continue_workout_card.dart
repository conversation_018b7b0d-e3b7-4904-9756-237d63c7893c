import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:gotcha_mfg_app/config/theme/app_colors.dart';
import 'package:gotcha_mfg_app/core/extensions/app_text_theme_extension.dart';
import 'package:gotcha_mfg_app/core/extensions/url_extensions.dart';
import 'package:gotcha_mfg_app/shared/widgets/network_image_indicator.dart';

class ContinueWorkoutCard extends StatelessWidget {
  const ContinueWorkoutCard({
    required this.title,
    required this.subtitle,
    required this.duration,
    required this.imageUrl,
    required this.onTap,
    required this.totalWorkouts,
    required this.completedWorkouts,
    super.key,
  });

  final String title;
  final String subtitle;
  final String duration;
  final String imageUrl;
  final VoidCallback? onTap;
  final int totalWorkouts;
  final int completedWorkouts;

  @override
  Widget build(BuildContext context) {
    final textTheme = Theme.of(context).textTheme;
    final progressValue =
        totalWorkouts > 0 ? completedWorkouts / totalWorkouts : 0.0;

    final buttonText =
        completedWorkouts < totalWorkouts ? 'Continue' : 'Completed';

    return GestureDetector(
      onTap: onTap,
      child: Card(
        elevation: 0,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(24),
        ),
        child: SizedBox(
          height: 196,
          // Removed DecorationImage from Container's decoration
          child: Stack(
            children: [
              // Background Image
              Positioned.fill(
                child: ClipRRect(
                  borderRadius: BorderRadius.circular(24),
                  child:
                      NetworkImageWithIndicator(imageUrl: imageUrl.workoutUrl),
                ),
              ),
              // Gradient Overlay
              Container(
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(24),
                  gradient: LinearGradient(
                    begin: Alignment.centerLeft,
                    end: Alignment.centerRight,
                    colors: [
                      Colors.black.withOpacity(0.8),
                      Colors.black.withOpacity(0.4),
                      Colors.transparent,
                      Colors.transparent,
                    ],
                  ),
                ),
              ),
              // Content
              Padding(
                padding: const EdgeInsets.all(25),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Text(
                      title,
                      style: textTheme.gothamBold.copyWith(
                        color: Colors.white,
                        fontSize: 14,
                      ),
                    ),
                    const Gap(4),
                    Row(
                      children: [
                        SizedBox(
                          height: 18,
                          width: 18,
                          child: CircularProgressIndicator(
                            value: progressValue,
                            color: Colors.white,
                            strokeWidth: 2.5,
                            backgroundColor: Colors.grey,
                          ),
                        ),
                        const Gap(10),
                        Text(
                          '$completedWorkouts/$totalWorkouts completed',
                          style: textTheme.ralewayRegular.copyWith(
                            color: Colors.white,
                            fontSize: 14,
                          ),
                        ),
                      ],
                    ),
                    const Gap(8),
                    SizedBox(
                      height: 50,
                      child: ElevatedButton(
                        onPressed: onTap,
                        style: const ButtonStyle(
                          elevation: WidgetStatePropertyAll(0),
                          backgroundColor:
                              WidgetStatePropertyAll(AppColors.coral),
                          foregroundColor: WidgetStatePropertyAll(Colors.white),
                        ),
                        child: Text(
                          buttonText,
                          style: textTheme.gothamBold.copyWith(
                            fontSize: 18,
                            color: Colors.white,
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
