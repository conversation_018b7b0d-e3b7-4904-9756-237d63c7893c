import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:gap/gap.dart';
import 'package:gotcha_mfg_app/config/theme/app_colors.dart';
import 'package:gotcha_mfg_app/core/extensions/app_text_theme_extension.dart';
import 'package:gotcha_mfg_app/features/home/<USER>/models/check_in_request.dart';
import 'package:gotcha_mfg_app/features/home/<USER>/blocs/check_in/check_in_cubit.dart';
import 'package:gotcha_mfg_app/features/home/<USER>/blocs/check_in_data/checkin_data_cubit.dart';
import 'package:gotcha_mfg_app/features/home/<USER>/widgets/check_in_subsection.dart';
import 'package:gotcha_mfg_app/features/home/<USER>/widgets/check_in_title.dart';
import 'package:gotcha_mfg_app/shared/widgets/loading_widget.dart';
import 'package:gotcha_mfg_app/shared/widgets/retry_widget.dart';

import '../../../../core/mixpanel_service.dart';
import '../../../../core/utils/app_print.dart';
import '../../../../locator.dart';
import '../../data/models/emotions_response.dart' as emotion;

class CheckIn extends StatefulWidget {
  const CheckIn({
    super.key,
    required this.selectedItem,
    required this.checkInTypeId,
    required this.latestCheckInData,
    this.isEdit,
  });

  final int selectedItem;
  final String? checkInTypeId;
  final emotion.LatestCheckInData? latestCheckInData;
  final bool? isEdit;

  @override
  State<CheckIn> createState() => _CheckInState();
}

class _CheckInState extends State<CheckIn> {
  final List<Question> questions = [];

  final Map<String, TextEditingController> noteControllers = {};

  @override
  void initState() {
    super.initState();
    sl<MixpanelService>().trackScreenView(
      'Check In Page',
      properties: {'Code': 'screen_view.check_in_page'},
    );
  }

  @override
  Widget build(BuildContext context) {
    final textTheme = Theme.of(context).textTheme;
    final size = MediaQuery.sizeOf(context);
    var border = const OutlineInputBorder(
      borderRadius: BorderRadius.all(
        Radius.circular(30),
      ),
      borderSide: BorderSide(
        color: AppColors.midBlue,
        width: 1.5,
      ),
    );
    var filledBorder = const OutlineInputBorder(
      borderRadius: BorderRadius.all(
        Radius.circular(30),
      ),
      borderSide: BorderSide(
        color: AppColors.coral,
        width: 1.5,
      ),
    );

    return BlocConsumer<CheckInCubit, CheckInState>(
      listener: (context, state) {
        if (state is CheckInLoaded) {
          var sections = state.data.data ?? [];
          var noteSections =
              sections.where((section) => section.isNote ?? false).toList();

          // Create controllers for note sections
          for (var note in noteSections) {
            if (!noteControllers.containsKey(note.questionId)) {
              noteControllers[note.questionId ?? ''] = TextEditingController();
            }
          }
        }
      },
      builder: (context, state) {
        if (state is CheckInLoading) {
          return Container(
            padding: const EdgeInsets.all(16),
            decoration: const BoxDecoration(
              borderRadius: BorderRadius.only(
                bottomLeft: Radius.circular(30),
                bottomRight: Radius.circular(30),
              ),
              color: AppColors.grey,
            ),
            height: size.height * 0.6,
            child: const Padding(
              padding: EdgeInsets.only(bottom: 160),
              child: LoadingWidget(
                color: AppColors.grey,
              ),
            ),
          );
        } else if (state is CheckInLoaded) {
          var sections = state.data.data ?? [];
          var regularSections =
              sections.where((section) => !(section.isNote ?? false)).toList();
          var noteSections =
              sections.where((section) => section.isNote ?? false).toList();

          return Container(
            padding: const EdgeInsets.symmetric(horizontal: 24),
            decoration: const BoxDecoration(
              borderRadius: BorderRadius.only(
                bottomLeft: Radius.circular(24),
                bottomRight: Radius.circular(24),
              ),
              color: AppColors.grey,
            ),
            // Use a SingleChildScrollView instead of a ListView with NeverScrollableScrollPhysics
            child: SingleChildScrollView(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Render regular sections
                  ...regularSections.asMap().entries.map((entry) {
                    int index = entry.key;
                    var section = entry.value;

                    return Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        CheckInSubSection(
                          latestCheckInData: widget.latestCheckInData,
                          index: index,
                          optional: section.isOptional ?? false,
                          context: context,
                          title: section.question ?? 'N/A',
                          items: section.answers ?? [],
                          checkInTypeId: widget.checkInTypeId,
                          questionId: section.questionId,
                        ),
                        const Gap(16),
                      ],
                    );
                  }),

                  // Render note sections at the bottom
                  ...noteSections.map((section) {
                    var controller = noteControllers[section.questionId];
                    var cubit = context.read<CheckinDataCubit>();
                    for (var i in widget.latestCheckInData?.questions ?? []) {
                      if ((i.questionId.toString() == section.questionId)) {
                        controller!.text = i.note ?? '';
                        cubit.addOrUpdateNote(
                          Question(
                            questionId: section.questionId,
                            answer: null,
                            answerId: null,
                            checkInTypesId: widget.checkInTypeId,
                            note: i.note,
                            other: null,
                          ),
                        );
                      }
                    }

                    return BlocBuilder<CheckinDataCubit, CheckinDataState>(
                      builder: (context, state) {
                        if (state is CheckInDataLoaded) {
                          return Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              CheckInTitle(
                                onboarding: false,
                                optionaltext: section.isOptional ?? false,
                                text: section.question ?? 'N/A',
                                mainTextStyle: textTheme.ralewayMedium
                                    .copyWith(fontSize: 14),
                                optionalTextStyle:
                                    textTheme.bodyEmphasis.copyWith(
                                  color: const Color(0xFFA9A9A9),
                                  fontSize: 14,
                                ),
                              ),
                              const Gap(12),
                              Material(
                                color: Colors.white,
                                borderRadius: BorderRadius.circular(30),
                                child: TextField(
                                  controller: controller,
                                  onChanged: (value) {
                                    cubit.addOrUpdateNote(
                                      Question(
                                        questionId: section.questionId,
                                        answer: null,
                                        answerId: null,
                                        checkInTypesId: widget.checkInTypeId,
                                        note: value,
                                        other: null,
                                      ),
                                    );
                                  },
                                  style: textTheme.ralewayRegular.copyWith(
                                    fontSize: 14,
                                    color: AppColors.navy,
                                  ),
                                  decoration: InputDecoration(
                                    hintText: 'Type here...',
                                    isDense: true,
                                    fillColor: controller!.text.isEmpty
                                        ? Colors.white
                                        : AppColors.lightRed,
                                    border: controller.text.isEmpty
                                        ? border
                                        : filledBorder,
                                    focusedBorder: controller.text.isEmpty
                                        ? border
                                        : filledBorder,
                                    enabledBorder: controller.text.isEmpty
                                        ? border
                                        : filledBorder,
                                  ),
                                ),
                              ),
                              const Gap(24),
                            ],
                          );
                        }
                        return const SizedBox();
                      },
                    );
                  }),
                  const Gap(200),
                ],
              ),
            ),
          );
        } else {
          info("message: $state");
          return Container(
            padding: const EdgeInsets.all(16),
            decoration: const BoxDecoration(
              borderRadius: BorderRadius.only(
                bottomLeft: Radius.circular(24),
                bottomRight: Radius.circular(24),
              ),
              color: AppColors.grey,
            ),
            height: size.height * 0.65,
            child: RetryWidgetMini(
              onRetry: () {
                if (widget.checkInTypeId == null) return;
                context
                    .read<CheckInCubit>()
                    .getDetailEmotions(widget.checkInTypeId!);
              },
              color: AppColors.grey,
            ),
          );
        }
      },
    );
  }
}
