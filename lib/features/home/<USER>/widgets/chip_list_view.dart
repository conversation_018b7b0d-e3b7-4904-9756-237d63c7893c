import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:gotcha_mfg_app/core/extensions/app_text_theme_extension.dart';
import 'package:gotcha_mfg_app/core/extensions/string_extensions.dart';
import 'package:gotcha_mfg_app/core/extensions/url_extensions.dart';
import 'package:gotcha_mfg_app/features/home/<USER>/models/exercises_response.dart';
import 'package:gotcha_mfg_app/shared/widgets/network_image_indicator.dart';

import '../../../../config/theme/app_colors.dart';

class ChipListView extends StatelessWidget {
  final List<Answer>? tag;
  final VoidCallback onAdd;
  final Function(Answer checkIn) onDelete;

  const ChipListView({
    super.key,
    required this.tag,
    required this.onAdd,
    required this.onDelete,
  });

  @override
  Widget build(BuildContext context) {
    final textTheme = Theme.of(context).textTheme;

    return Padding(
      padding: const EdgeInsets.only(
        left: 0,
        right: 0,
        bottom: 8,
      ),
      child: Si<PERSON><PERSON><PERSON>(
        height: 50,
        child: ListView(
          scrollDirection: Axis.horizontal,
          children: [
            const Gap(24),
            // Chip for the main check-in type
            // if (tag != null && tag!.isNotEmpty)
            //   Padding(
            //     padding: const EdgeInsets.only(right: 8),
            //     child: Chip(
            //       label: Text(
            //         tag. ?? 'N/A',
            //         style: textTheme.ralewaySemiBold.copyWith(fontSize: 14),
            //       ),
            //       deleteIcon: const Icon(Icons.close, size: 16),
            //       backgroundColor: AppColors.lightPink,
            //       shape: const StadiumBorder(
            //         side: BorderSide(color: AppColors.coral),
            //       ),
            //       onDeleted: () => onDelete(tag?.checkInType),
            //     ),
            //   ),
            // Chips for latest check-ins
            ...?tag?.where((e) => (e.name != null)).map((checkIn) {
              return Padding(
                padding: const EdgeInsets.only(right: 8),
                child: Chip(
                  avatar: checkIn.iconUrl == null
                      ? null
                      : ClipRRect(
                          borderRadius: BorderRadius.circular(100),
                          child: SizedBox(
                            height: 40,
                            width: 40,
                            child: Padding(
                              padding: const EdgeInsets.all(0),
                              child: NetworkImageWithIndicator(
                                imageUrl: checkIn.iconUrl?.iconUrl ?? '',
                              ),
                            ),
                          ),
                        ),
                  label: Text(
                    checkIn.name?.capitalizeFirstLetterOfEachWord() ?? 'N/A',
                    style: textTheme.ralewaySemiBold.copyWith(fontSize: 14),
                  ),
                  deleteIcon: const Icon(Icons.close, size: 16),
                  backgroundColor: AppColors.lightRed,
                  shape: const StadiumBorder(
                    side: BorderSide(color: AppColors.coral, width: 1.5),
                  ),
                  onDeleted: () => onDelete(checkIn),
                ),
              );
            }),
            // Add Button
            Chip(
              label: Text(
                'Add',
                style: textTheme.ralewaySemiBold.copyWith(fontSize: 14),
              ),
              deleteIcon: const Icon(
                Icons.add,
                size: 16,
                color: AppColors.navy,
              ),
              onDeleted: () => onAdd(),
              backgroundColor: Colors.white,
              shape: const StadiumBorder(
                side: BorderSide(color: Colors.white),
              ),
            ),
            const Gap(24),
          ],
        ),
      ),
    );
  }
}
