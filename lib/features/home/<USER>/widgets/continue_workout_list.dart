import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:gotcha_mfg_app/config/theme/app_assets.dart';
import 'package:gotcha_mfg_app/core/extensions/app_text_theme_extension.dart';
import 'package:gotcha_mfg_app/locator.dart';

import '../../../../config/theme/app_colors.dart';
import '../../../../core/mixpanel_service.dart';
import '../../data/models/continue_workout.dart';
import 'continue_workout_card.dart';

class ContinueWorkoutList extends StatefulWidget {
  const ContinueWorkoutList({
    super.key,
    required this.continueWorkout,
    required this.onTap,
  });

  final List<Data>? continueWorkout;
  final ValueChanged<Data> onTap;

  @override
  State<ContinueWorkoutList> createState() => _ContinueWorkoutListState();
}

class _ContinueWorkoutListState extends State<ContinueWorkoutList> {
  var todaysComplete = false;
  int? day;

  @override
  initState() {
    super.initState();
    var today = DateTime.now();
    var firstItem = widget.continueWorkout?.first;
    if (firstItem?.workouts?.isNotEmpty ?? false) {
      for (var k in firstItem!.workouts!.where((k) => k.isCompleted == true)) {
        if (k.isCompleted == true && k.completedAt != null) {
          var completedAt = k.completedAt!.toLocal();
          if (DateTime(completedAt.year, completedAt.month, completedAt.day) ==
              DateTime(today.year, today.month, today.day)) {
            todaysComplete = true;
            // log("name123 ${k.workoutId}");
            day = (firstItem.workouts
                    ?.where((k) => k.isCompleted == true)
                    .toList()
                    .indexOf(k) ??
                0);
            break;
          } else {
            // ignore
          }
        }
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final textTheme = Theme.of(context).textTheme;

    return Container(
      decoration: const BoxDecoration(
        color: AppColors.lightRed,
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          GestureDetector(
            onTap: () {
              // setState(() {
              //   isExpanded = !isExpanded;
              // });
            },
            child: Container(
              padding: EdgeInsets.fromLTRB(24, 0, 24, !todaysComplete ? 0 : 16),
              child: todaysComplete
                  ? Row(
                      children: [
                        Image.asset(
                          AppAssets.completetick,
                          scale: 4.5,
                        ),
                        const SizedBox(width: 8),
                        Expanded(
                          child: Text(
                            'Day $day of your workout complete',
                            style: textTheme.ralewaySemiBold.copyWith(
                              fontSize: 17,
                            ),
                          ),
                        ),
                        TextButton(
                          onPressed: () {
                            widget.onTap(widget.continueWorkout![0]);
                          },
                          child: Text(
                            'View',
                            style: textTheme.linkText.copyWith(
                              color: AppColors.coral,
                            ),
                          ),
                        ),
                      ],
                    )
                  : Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Expanded(
                          child: Text(
                            'Keep going with your workout',
                            style: textTheme.ralewaySemiBold.copyWith(
                              fontSize: 17,
                            ),
                          ),
                        ),
                      ],
                    ),
            ),
          ),
          if (!todaysComplete) ...[
            const Gap(8),
            ListView.separated(
              physics: const NeverScrollableScrollPhysics(),
              shrinkWrap: true,
              itemCount: widget.continueWorkout?.length ?? 0,
              itemBuilder: (context, index) {
                final currentWorkout = widget.continueWorkout![index];
                return Padding(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 24,
                    vertical: 4,
                  ),
                  child: ContinueWorkoutCard(
                    title: currentWorkout.seriesTitle ?? 'N/A',
                    subtitle: currentWorkout.seriesDescription ?? 'N/A',
                    duration: currentWorkout.seriesDescription ?? 'N/A',
                    imageUrl: currentWorkout.seriesImageUrl.toString(),
                    onTap: () {
                      widget.onTap(currentWorkout);
                      sl<MixpanelService>().trackButtonClick('Continue Workout',
                          properties: {
                            'Page': 'Normal Feed Page',
                            'Code': 'click.continue_workout'
                          });
                    },
                    totalWorkouts: currentWorkout.totalWorkouts ?? 0,
                    completedWorkouts:
                        currentWorkout.totalCompletedWorkouts ?? 0,
                  ),
                );
              },
              separatorBuilder: (context, index) => const Gap(12),
            ),
            const Gap(20),
          ],
        ],
      ),
    );
  }
}
