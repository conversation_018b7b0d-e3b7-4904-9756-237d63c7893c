import 'package:flutter/material.dart';
import 'package:gotcha_mfg_app/config/theme/app_assets.dart';
import 'package:gotcha_mfg_app/config/theme/app_colors.dart';
import 'package:gotcha_mfg_app/core/extensions/app_text_theme_extension.dart';

class Header extends StatelessWidget {
  const Header({
    required this.textTheme,
    required this.onTap,
    super.key,
  });

  final TextTheme textTheme;
  final VoidCallback onTap;

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.symmetric(
        horizontal: 16,
        vertical: 24,
      ),
      decoration: const BoxDecoration(
        color: AppColors.navy,
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(30),
          topRight: Radius.circular(30),
        ),
      ),
      child: Row(
        children: [
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'Hi!',
                style: textTheme.gothamBold.copyWith(
                  fontSize: 20,
                  color: Colors.white,
                ),
              ),
              Text(
                'Welcome back to the gym',
                style: textTheme.ralewayRegular.copyWith(
                  color: Colors.white,
                  fontSize: 12,
                ),
              ),
            ],
          ),
          const Spacer(),
          GestureDetector(
            onTap: onTap,
            child: Image.asset(
              AppAssets.g4lMfg,
              width: 104,
            ),
          ),
        ],
      ),
    );
  }
}
