// import 'dart:developer';

// import 'package:flutter/material.dart';
// import 'package:flutter_bloc/flutter_bloc.dart';
// import 'package:gotcha_mfg_app/config/theme/app_colors.dart';
// import 'package:gotcha_mfg_app/core/extensions/app_text_theme_extension.dart';
// import 'package:gotcha_mfg_app/core/extensions/string_extensions.dart';
// import 'package:gotcha_mfg_app/core/utils/snackbar_service.dart';
// import 'package:gotcha_mfg_app/features/home/<USER>/models/check_in_request.dart';
// import 'package:gotcha_mfg_app/features/home/<USER>/models/emotions_detail_response.dart';
// import 'package:gotcha_mfg_app/features/home/<USER>/blocs/check_in_data/checkin_data_cubit.dart'
//     as data;
// import 'package:gotcha_mfg_app/features/home/<USER>/widgets/check_in_title.dart';
// import 'package:gotcha_mfg_app/shared/widgets/loading_widget.dart';

// import '../../data/models/emotions_response.dart' as emotion;
// import 'package:collection/collection.dart';

// class CheckInSubSection extends StatefulWidget {
//   const CheckInSubSection({
//     required this.context,
//     required this.title,
//     required this.items,
//     this.checkInTypeId,
//     this.questionId,
//     super.key,
//     required this.optional,
//     this.latestCheckInData,
//     this.index,
//   });

//   final emotion.LatestCheckInData? latestCheckInData;
//   final String title;
//   final bool optional;
//   final BuildContext context;
//   final List<Answer> items;
//   final String? checkInTypeId;
//   final String? questionId;
//   final int? index;

//   @override
//   State<CheckInSubSection> createState() => _CheckInSubSectionState();
// }

// class _CheckInSubSectionState extends State<CheckInSubSection> {
//   static const int maxSelectionLimit = 3;

//   final ValueNotifier<bool> _isTextFieldVisible = ValueNotifier(false);
//   final TextEditingController _textFieldController = TextEditingController();

//   bool _showAllItems = false;

//   @override
//   void initState() {
//     super.initState();
//     // sl<MixpanelService>().trackScreenView('CheckIn Sub Section Page');

//     final cubit = context.read<data.CheckinDataCubit>();
//     cubit.clear();
//     // if (widget.latestCheckInData?.questions != null &&
//     //     widget.index != null &&
//     //     widget.index! < widget.latestCheckInData!.questions!.length) {
//     //   final other = widget.latestCheckInData!.questions![widget.index!].other;
//     //   if (other != null && other.isNotEmpty) {
//     //     cubit.addQuestion(
//     //       Question(
//     //         questionId: widget.questionId,
//     //         checkInTypesId: widget.checkInTypeId,
//     //         other: other,
//     //       ),
//     //     );
//     //   }
//     // }

//     // for (var item in widget.items) {
//     //   if (wasInitiallyPreselected(item.answerId)) {
//     //     final question = Question(
//     //       questionId: widget.questionId,
//     //       answerId: item.answerId,
//     //       checkInTypesId: widget.checkInTypeId,
//     //       answer: item.answer,
//     //     );
//     //     cubit.addQuestion(question);
//     //   }
//     // }
//     WidgetsBinding.instance.addPostFrameCallback((_) {
//       if (widget.latestCheckInData?.questions != null &&
//           widget.index != null &&
//           widget.index! < widget.latestCheckInData!.questions!.length) {
//         final latestQuestion = widget.latestCheckInData!.questions
//             ?.firstWhereOrNull((e) =>
//                 (e.questionId == widget.questionId)); // Use firstWhereOrNull
//         final other = latestQuestion?.other; // Access 'other' safely

//         if (other != null && other.isNotEmpty) {
//           cubit.addQuestion(
//             Question(
//               questionId: widget.questionId,
//               checkInTypesId: widget.checkInTypeId,
//               other: other,
//             ),
//           );
//         }
//       }

//       for (var item in widget.items) {
//         if (wasInitiallyPreselected(item.answerId)) {
//           final question = Question(
//             questionId: widget.questionId,
//             answerId: item.answerId,
//             checkInTypesId: widget.checkInTypeId,
//             answer: item.answer,
//           );
//           cubit.addQuestion(question);
//         }
//       }
//     });
//   }

//   @override
//   void dispose() {
//     _textFieldController.dispose();
//     _isTextFieldVisible.dispose();
//     // if (mounted) context.read<data.CheckinDataCubit>().clear();
//     super.dispose();
//   }

//   bool wasInitiallyPreselected(String? answerId) {
//     if (widget.latestCheckInData == null ||
//         widget.latestCheckInData!.questions == null) {
//       return false;
//     }
//     for (var question in widget.latestCheckInData!.questions!) {
//       // if question.other is not null then add to cubit as other item
//       if (question.answers != null) {
//         for (var answer in question.answers!) {
//           if (answer.id == answerId) {
//             return true;
//           }
//         }
//       }
//     }
//     return false;
//   }

//   @override
//   Widget build(BuildContext context) {
//     var textTheme = Theme.of(context).textTheme;

//     return BlocBuilder<data.CheckinDataCubit, data.CheckinDataState>(
//         builder: (context, state) {
//       final cubit = context.read<data.CheckinDataCubit>();

//       if (state is! data.CheckInDataLoaded) {
//         return const Center(child: Loader());
//       }

//       final selectedQuestions = state.list
//           .where((q) => q.questionId == widget.questionId && q.answerId != null)
//           .toList();

//       final selectedAnswerIds =
//           selectedQuestions.map((e) => e.answerId).toSet();

//       List<Answer> selectedItems = [];
//       List<Answer> unselectedItems = [];

//       for (var item in widget.items) {
//         if (selectedAnswerIds.contains(item.answerId)) {
//           selectedItems.add(item);
//         } else {
//           unselectedItems.add(item);
//         }
//       }

//       List<Answer> itemsToShow = [...selectedItems];

//       if (!_showAllItems) {
//         int remainingSpace = maxSelectionLimit - selectedItems.length;
//         if (remainingSpace > 0) {
//           itemsToShow.addAll(unselectedItems.take(remainingSpace));
//         }
//       } else {
//         itemsToShow = widget.items;
//       }
//       // Ensure no duplicates in itemsToShow, although it should not happen with current logic
//       itemsToShow = itemsToShow.toSet().toList();
//       itemsToShow = itemsToShow
//           .take(widget.items.length)
//           .toList(); // To handle cases where somehow itemsToShow becomes longer than widget.items

//       log('selectedQuestions: ${selectedQuestions.length}');

//       bool isCurrentlySelected(Question question) {
//         return selectedQuestions.any((q) =>
//             q.answerId == question.answerId &&
//             q.questionId == widget.questionId);
//       }

//       bool hasOther() {
//         return state.list.any(
//           (q) => (q.other != null &&
//               q.questionId == widget.questionId &&
//               q.checkInTypesId == widget.checkInTypeId),
//         );
//       }

//       void toggleTextFieldVisibility() {
//         final cubit = context.read<data.CheckinDataCubit>();

//         if (hasOther()) {
//           setState(() {
//             cubit.remove(state.list.firstWhere((q) =>
//                 q.other != null &&
//                 q.questionId == widget.questionId &&
//                 q.checkInTypesId == widget.checkInTypeId));
//           });
//           return;
//         }
//         _isTextFieldVisible.value = !_isTextFieldVisible.value;
//       }

//       void toggleQuestionSelection(Question question) {
//         setState(() {
//           if (isCurrentlySelected(question)) {
//             cubit.remove(question);
//           } else {
//             if (selectedQuestions
//                     .where((e) => e.questionId == widget.questionId)
//                     .length >=
//                 maxSelectionLimit) {
//               SnackBarService.error(
//                 context: context,
//                 message: 'You can only select up to $maxSelectionLimit chips.',
//               );
//               return;
//             }
//             cubit.addQuestion(question);
//           }
//         });
//       }

//       Question? getOther() {
//         return state.list.firstWhere(
//             (q) =>
//                 q.other != null &&
//                 q.questionId == widget.questionId &&
//                 q.checkInTypesId == widget.checkInTypeId,
//             orElse: () => Question());
//       }

//       void addOtherQuestion(String input) {
//         setState(() {
//           if (hasOther()) {
//             cubit.remove(getOther()!);
//           }
//           if (selectedQuestions
//                   .where((e) => e.questionId == widget.questionId)
//                   .length >=
//               maxSelectionLimit) {
//             SnackBarService.error(
//               context: context,
//               message: 'You can only select up to $maxSelectionLimit chips.',
//             );
//             return;
//           }

//           cubit.addQuestion(
//             Question(
//               questionId: widget.questionId,
//               checkInTypesId: widget.checkInTypeId,
//               other: input,
//             ),
//           );
//         });
//         _isTextFieldVisible.value = false;
//         _textFieldController.clear();
//       }

//       Widget buildActionChip(Answer item, index) {
//         final question = Question(
//           questionId: widget.questionId,
//           answerId: item.answerId,
//           checkInTypesId: widget.checkInTypeId,
//           answer: item.answer,
//         );

//         var isSelected = isCurrentlySelected(question);

//         log('isSelected123: $isSelected');

//         return ActionChip(
//           shape: StadiumBorder(
//             side:
//                 BorderSide(color: isSelected ? AppColors.coral : Colors.white),
//           ),
//           onPressed: () => toggleQuestionSelection(question),
//           label: Text(
//             item.answer?.capitalizeFirstLetterOfEachWord() ?? 'N/A',
//             style: Theme.of(context).textTheme.ralewaySemiBold.copyWith(
//                   fontSize: 14,
//                   color: AppColors.navy,
//                 ),
//           ),
//           backgroundColor: isSelected ? AppColors.lightRed : Colors.white,
//         );
//       }

//       Widget buildOtherChip() {
//         final otherText =
//             hasOther() ? getOther()?.other ?? '+ Other' : '+ Other';

//         final textTheme = Theme.of(context).textTheme;

//         return ActionChip(
//           label: Text(
//             otherText,
//             style: hasOther()
//                 ? textTheme.ralewaySemiBold.copyWith(fontSize: 14)
//                 : textTheme.ralewayLight.copyWith(fontSize: 14),
//           ),
//           onPressed: toggleTextFieldVisibility,
//           shape: StadiumBorder(
//             side: BorderSide(
//               color: hasOther() ? AppColors.coral : AppColors.midBlue,
//             ),
//           ),
//           backgroundColor: hasOther() ? AppColors.lightRed : Colors.white,
//         );
//       }

//       Widget buildTextField() {
//         var border = OutlineInputBorder(
//           borderRadius: BorderRadius.circular(30),
//           borderSide: const BorderSide(
//             color: AppColors.midBlue,
//             width: 0.5,
//           ),
//         );
//         return ValueListenableBuilder<bool>(
//           valueListenable: _isTextFieldVisible,
//           builder: (context, isVisible, _) {
//             if (!isVisible) return buildOtherChip();
//             var textTheme = Theme.of(context).textTheme;
//             return Container(
//               margin: const EdgeInsets.only(top: 4),
//               height: 40,
//               decoration: BoxDecoration(
//                 borderRadius: BorderRadius.circular(30),
//                 color: Colors.white,
//               ),
//               width: MediaQuery.of(context).size.width,
//               child: TextField(
//                 controller: _textFieldController,
//                 style: textTheme.ralewayBold.copyWith(
//                   fontSize: 14,
//                   color: AppColors.navy,
//                 ),
//                 decoration: InputDecoration(
//                   hintText: 'Type here...',
//                   isDense: true,
//                   contentPadding: const EdgeInsets.symmetric(horizontal: 16),
//                   enabledBorder: border,
//                   focusedBorder: border,
//                   border: border,
//                   suffixIcon: IconButton(
//                     icon: const Icon(Icons.check, color: AppColors.coral),
//                     onPressed: () {
//                       if (_textFieldController.text.isEmpty) {
//                         if (hasOther()) {
//                           state.list.removeWhere((q) =>
//                               q.other != null &&
//                               q.questionId == widget.questionId);
//                         }
//                         _isTextFieldVisible.value = false;
//                         _textFieldController.clear();
//                         return;
//                       }

//                       addOtherQuestion(_textFieldController.text.trim());
//                     },
//                   ),
//                 ),
//               ),
//             );
//           },
//         );
//       }

//       return Column(
//         crossAxisAlignment: CrossAxisAlignment.start,
//         children: [
//           CheckInTitle(
//             optionaltext: widget.optional, onboarding: false,
//             text: widget.title, //error
//             mainTextStyle: textTheme.bodyEmphasis.copyWith(height: 1.5),
//             optionalTextStyle:
//                 textTheme.bodyEmphasis.copyWith(color: const Color(0xFFA9A9A9)),
//           ),
//           const SizedBox(height: 8),
//           Wrap(
//             spacing: 8,
//             children: [
//               for (final item in itemsToShow)
//                 buildActionChip(item, widget.index),
//               buildTextField(),
//               if (widget.items.length > maxSelectionLimit &&
//                   !_showAllItems &&
//                   unselectedItems
//                       .isNotEmpty) // Only show "Show more" if there are more items to show
//                 TextButton(
//                   onPressed: () {
//                     setState(() {
//                       _showAllItems = true;
//                     });
//                   },
//                   child: Text(
//                     'Show more >',
//                     style: Theme.of(context).textTheme.ralewaySemiBold.copyWith(
//                           fontSize: 14,
//                           color: AppColors.coral,
//                         ),
//                   ),
//                 ),
//             ],
//           ),
//         ],
//       );
//     });
//   }
// }

import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:gotcha_mfg_app/config/theme/app_colors.dart';
import 'package:gotcha_mfg_app/core/extensions/app_text_theme_extension.dart';
import 'package:gotcha_mfg_app/core/extensions/string_extensions.dart';
import 'package:gotcha_mfg_app/core/extensions/url_extensions.dart';
import 'package:gotcha_mfg_app/core/utils/snackbar_service.dart';
import 'package:gotcha_mfg_app/features/home/<USER>/models/check_in_request.dart';
import 'package:gotcha_mfg_app/features/home/<USER>/models/emotions_detail_response.dart';
import 'package:gotcha_mfg_app/features/home/<USER>/blocs/check_in_data/checkin_data_cubit.dart'
    as data;
import 'package:gotcha_mfg_app/features/home/<USER>/widgets/check_in_title.dart';
import 'package:gotcha_mfg_app/shared/widgets/loading_widget.dart';
import 'package:gotcha_mfg_app/shared/widgets/network_image_indicator.dart';

import '../../../../core/utils/app_print.dart';
import '../../data/models/emotions_response.dart' as emotion;
import 'package:collection/collection.dart';

class CheckInSubSection extends StatefulWidget {
  const CheckInSubSection({
    required this.context,
    required this.title,
    required this.items,
    this.checkInTypeId,
    this.questionId,
    super.key,
    required this.optional,
    this.latestCheckInData,
    this.index,
  });

  final emotion.LatestCheckInData? latestCheckInData;
  final String title;
  final bool optional;
  final BuildContext context;
  final List<Answer> items;
  final String? checkInTypeId;
  final String? questionId;
  final int? index;

  @override
  State<CheckInSubSection> createState() => _CheckInSubSectionState();
}

class _CheckInSubSectionState extends State<CheckInSubSection> {
  static const int maxSelectionLimit = 3;

  final ValueNotifier<bool> _isTextFieldVisible = ValueNotifier(false);
  final TextEditingController _textFieldController = TextEditingController();

  bool _showAllItems = false;

  @override
  void initState() {
    super.initState();
    final cubit = context.read<data.CheckinDataCubit>();
    cubit.clear();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (widget.latestCheckInData?.questions != null &&
          widget.index != null &&
          widget.index! < widget.latestCheckInData!.questions!.length) {
        final latestQuestion = widget.latestCheckInData!.questions
            ?.firstWhereOrNull((e) => (e.questionId == widget.questionId));
        final other = latestQuestion?.other;
        if (other != null && other.isNotEmpty) {
          cubit.addQuestion(
            Question(
              questionId: widget.questionId,
              checkInTypesId: widget.checkInTypeId,
              other: other,
            ),
          );
        }
      }

      for (var item in widget.items) {
        if (wasInitiallyPreselected(
          item.answerId,
          item.answerCheckInTypesId,
        )) {
          final question = Question(
            questionId: widget.questionId,
            answerId: item.answerId,
            checkInTypesId: widget.checkInTypeId,
            answer: item.answer,
          );
          cubit.addQuestion(question);
        }
      }
    });
  }

  @override
  void dispose() {
    _textFieldController.dispose();
    _isTextFieldVisible.dispose();
    // if (mounted) context.read<data.CheckinDataCubit>().clear();
    super.dispose();
  }

  // bool wasInitiallyPreselected(String? answerId) {
  //   if (widget.latestCheckInData == null ||
  //       widget.latestCheckInData!.questions == null) {
  //     return false;
  //   }
  //   for (var question in widget.latestCheckInData!.questions!) {
  //     // if question.other is not null then add to cubit as other item
  //     if (question.answers != null) {
  //       for (var answer in question.answers!) {
  //         if (answer.id == answerId) {
  //           return true;
  //         }
  //       }
  //     }
  //   }
  //   return false;
  // }

  bool wasInitiallyPreselected(
    String? answerId,
    String? answerCheckInTypesId,
  ) {
    if (widget.latestCheckInData == null ||
        widget.latestCheckInData!.questions == null) {
      return false;
    }
    for (var question in widget.latestCheckInData!.questions!) {
      // if question.other is not null then add to cubit as other item
      if (question.answers != null) {
        for (var answer in question.answers!) {
          if ((answer.id == answerId) ||
              (answer.answerCheckInTypesId == answerCheckInTypesId)) {
            return true;
          }
        }
      }
    }
    return false;
  }

  @override
  Widget build(BuildContext context) {
    var textTheme = Theme.of(context).textTheme;

    return BlocBuilder<data.CheckinDataCubit, data.CheckinDataState>(
        builder: (context, state) {
      final cubit = context.read<data.CheckinDataCubit>();

      if (state is! data.CheckInDataLoaded) {
        return const Center(child: Loader());
      }

      final selectedQuestions = state.list
          .where((q) => q.questionId == widget.questionId && q.answerId != null)
          .toList();

      final selectedAnswerIds =
          selectedQuestions.map((e) => e.answerId).toSet();

      List<Answer> selectedItems = [];
      List<Answer> unselectedItems = [];

      for (var item in widget.items) {
        if (selectedAnswerIds.contains(item.answerId)) {
          selectedItems.add(item);
        } else {
          unselectedItems.add(item);
        }
      }

      List<Answer> itemsToShow = [...selectedItems];

      if (!_showAllItems) {
        int remainingSpace = maxSelectionLimit - selectedItems.length;
        if (remainingSpace > 0) {
          itemsToShow.addAll(unselectedItems.take(remainingSpace));
        }
      } else {
        itemsToShow = widget.items;
      }
      // Ensure no duplicates in itemsToShow, although it should not happen with current logic
      itemsToShow = itemsToShow.toSet().toList();
      itemsToShow = itemsToShow
          .take(widget.items.length)
          .toList(); // To handle cases where somehow itemsToShow becomes longer than widget.items

      info('selectedQuestions: ${selectedQuestions.length}');

      bool isCurrentlySelected(Question question) {
        return selectedQuestions.any((q) =>
            q.answerId == question.answerId &&
            q.questionId == widget.questionId);
      }

      bool hasOther() {
        return state.list.any(
          (q) => (q.other != null &&
              q.questionId == widget.questionId &&
              q.checkInTypesId == widget.checkInTypeId),
        );
      }

      void toggleTextFieldVisibility() {
        final cubit = context.read<data.CheckinDataCubit>();

        if (hasOther()) {
          setState(() {
            cubit.remove(state.list.firstWhere((q) =>
                q.other != null &&
                q.questionId == widget.questionId &&
                q.checkInTypesId == widget.checkInTypeId));
          });
          return;
        }
        _isTextFieldVisible.value = !_isTextFieldVisible.value;
      }

      void toggleQuestionSelection(Question question) {
        setState(() {
          if (isCurrentlySelected(question)) {
            cubit.remove(question);
          } else {
            if (selectedQuestions
                    .where((e) => e.questionId == widget.questionId)
                    .length >=
                maxSelectionLimit) {
              SnackBarService.error(
                positionFromBottom: MediaQuery.of(context).size.height * 0.1,
                context: context,
                message: 'You can only select up to $maxSelectionLimit chips.',
              );
              return;
            }
            cubit.addQuestion(question);
          }
        });
      }

      Question? getOther() {
        return state.list.firstWhere(
            (q) =>
                q.other != null &&
                q.questionId == widget.questionId &&
                q.checkInTypesId == widget.checkInTypeId,
            orElse: () => Question());
      }

      void addOtherQuestion(String input) {
        setState(() {
          if (hasOther()) {
            cubit.remove(getOther()!);
          }
          if (selectedQuestions
                  .where((e) => e.questionId == widget.questionId)
                  .length >=
              maxSelectionLimit) {
            SnackBarService.error(
              context: context,
              message: 'You can only select up to $maxSelectionLimit chips.',
            );
            return;
          }

          cubit.addQuestion(
            Question(
              questionId: widget.questionId,
              checkInTypesId: widget.checkInTypeId,
              other: input,
            ),
          );
        });
        _isTextFieldVisible.value = false;
        _textFieldController.clear();
      }

      Widget buildActionChip(Answer item, index) {
        final question = Question(
          questionId: widget.questionId,
          answerId: item.answerId,
          checkInTypesId: widget.checkInTypeId,
          answer: item.answer,
        );

        var isSelected = isCurrentlySelected(question);

        return ActionChip(
          avatar: item.iconUrl == null
              ? null
              : ClipRRect(
                  borderRadius: BorderRadius.circular(100),
                  child: NetworkImageWithIndicatorSmall(
                    imageUrl: item.iconUrl?.iconUrl ?? '',
                  ),
                ),
          shape: StadiumBorder(
            side: BorderSide(
                color: isSelected ? AppColors.coral : Colors.white, width: 1.5),
          ),
          onPressed: () => toggleQuestionSelection(question),
          label: Text(
            item.answer?.capitalizeFirstLetterOfEachWord() ?? 'N/A',
            style: Theme.of(context).textTheme.ralewaySemiBold.copyWith(
                  fontSize: 14,
                  color: AppColors.navy,
                ),
          ),
          backgroundColor: isSelected ? AppColors.lightRed : Colors.white,
        );
      }

      Widget buildOtherChip() {
        final otherText =
            hasOther() ? getOther()?.other ?? '+ Other' : '+ Other';

        final textTheme = Theme.of(context).textTheme;

        return ActionChip(
          label: Text(
            otherText,
            style: hasOther()
                ? textTheme.ralewaySemiBold.copyWith(fontSize: 14)
                : textTheme.ralewayLight.copyWith(fontSize: 14),
          ),
          onPressed: toggleTextFieldVisibility,
          shape: StadiumBorder(
            side: BorderSide(
              width: 1.5,
              color: hasOther() ? AppColors.coral : AppColors.midBlue,
            ),
          ),
          backgroundColor: hasOther() ? AppColors.lightRed : Colors.white,
        );
      }

      Widget buildTextField() {
        var border = OutlineInputBorder(
          borderRadius: BorderRadius.circular(30),
          borderSide: const BorderSide(
            color: AppColors.midBlue,
            width: 1.5,
          ),
        );
        return ValueListenableBuilder<bool>(
          valueListenable: _isTextFieldVisible,
          builder: (context, isVisible, _) {
            if (!isVisible) return buildOtherChip();
            var textTheme = Theme.of(context).textTheme;
            return Container(
              margin: const EdgeInsets.only(top: 4),
              height: 44,
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(30),
                color: Colors.white,
              ),
              width: MediaQuery.of(context).size.width,
              child: TextField(
                controller: _textFieldController,
                style: textTheme.ralewayBold.copyWith(
                  fontSize: 14,
                  color: AppColors.navy,
                ),
                decoration: InputDecoration(
                  hintText: 'Type here...',
                  isDense: true,
                  contentPadding: const EdgeInsets.symmetric(horizontal: 16),
                  enabledBorder: border,
                  focusedBorder: border,
                  border: border,
                  suffixIcon: IconButton(
                    icon: const Icon(Icons.check, color: AppColors.coral),
                    onPressed: () {
                      if (_textFieldController.text.isEmpty) {
                        if (hasOther()) {
                          state.list.removeWhere((q) =>
                              q.other != null &&
                              q.questionId == widget.questionId);
                        }
                        _isTextFieldVisible.value = false;
                        _textFieldController.clear();
                        return;
                      }

                      addOtherQuestion(_textFieldController.text.trim());
                    },
                  ),
                ),
              ),
            );
          },
        );
      }

      return Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          CheckInTitle(
            optionaltext: widget.optional,
            onboarding: false,
            text: widget.title,
            mainTextStyle:
                textTheme.ralewayMedium.copyWith(height: 1.5, fontSize: 14),
            optionalTextStyle: textTheme.bodyEmphasis.copyWith(
              color: const Color(0xFFA9A9A9),
              fontSize: 14,
            ),
          ),
          const SizedBox(height: 8),
          Wrap(
            spacing: 8,
            children: [
              for (final item in itemsToShow)
                buildActionChip(
                  item,
                  widget.index,
                ),
              buildTextField(),
              if (widget.items.length > maxSelectionLimit &&
                  !_showAllItems &&
                  unselectedItems
                      .isNotEmpty) // Only show "Show more" if there are more items to show
                TextButton(
                  onPressed: () {
                    setState(() {
                      _showAllItems = true;
                    });
                  },
                  child: Text(
                    'Show more >',
                    style: Theme.of(context).textTheme.ralewaySemiBold.copyWith(
                          fontSize: 14,
                          color: AppColors.coral,
                        ),
                  ),
                ),
            ],
          ),
        ],
      );
    });
  }
}
