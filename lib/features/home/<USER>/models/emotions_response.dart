// import 'dart:convert';

// class EmotionsResponse {
//   String? message;
//   String? status;
//   Data? data;

//   EmotionsResponse({
//     this.message,
//     this.status,
//     this.data,
//   });

//   factory EmotionsResponse.fromRawJson(String str) =>
//       EmotionsResponse.fromJson(json.decode(str));

//   String toRawJson() => json.encode(toJson());

//   factory EmotionsResponse.fromJson(Map<String, dynamic> json) =>
//       EmotionsResponse(
//         message: json["message"],
//         status: json["status"],
//         data: json["data"] == null ? null : Data.fromJson(json["data"]),
//       );

//   Map<String, dynamic> toJson() => {
//         "message": message,
//         "status": status,
//         "data": data?.toJson(),
//       };
// }

// class Data {
//   List<Type>? type;
//   bool? isCheckInToday;
//   String? latestCheckInType;
//   String? latestCheckInTypeId;

//   Data({
//     this.type,
//     this.isCheckInToday,
//     this.latestCheckInType,
//     this.latestCheckInTypeId,
//   });

//   factory Data.fromRawJson(String str) => Data.fromJson(json.decode(str));

//   String toRawJson() => json.encode(toJson());

//   factory Data.fromJson(Map<String, dynamic> json) => Data(
//         type: json["type"] == null
//             ? []
//             : List<Type>.from(json["type"]!.map((x) => Type.fromJson(x))),
//         isCheckInToday: json["is_check_in_today"],
//         latestCheckInType: json["latest_check_in_type"],
//         latestCheckInTypeId: json["latest_check_in_type_id"],
//       );

//   Map<String, dynamic> toJson() => {
//         "type": type == null
//             ? []
//             : List<dynamic>.from(type!.map((x) => x.toJson())),
//         "is_check_in_today": isCheckInToday,
//         "latest_check_in_type": latestCheckInType,
//         "latest_check_in_type_id": latestCheckInTypeId,
//       };
// }

// class Type {
//   String? id;
//   String? type;
//   DateTime? createdAt;
//   DateTime? updatedAt;

//   Type({
//     this.id,
//     this.type,
//     this.createdAt,
//     this.updatedAt,
//   });

//   factory Type.fromRawJson(String str) => Type.fromJson(json.decode(str));

//   String toRawJson() => json.encode(toJson());

//   factory Type.fromJson(Map<String, dynamic> json) => Type(
//         id: json["id"],
//         type: json["type"],
//         createdAt: json["created_at"] == null
//             ? null
//             : DateTime.parse(json["created_at"]),
//         updatedAt: json["updated_at"] == null
//             ? null
//             : DateTime.parse(json["updated_at"]),
//       );

//   Map<String, dynamic> toJson() => {
//         "id": id,
//         "type": type,
//         "created_at": createdAt?.toIso8601String(),
//         "updated_at": updatedAt?.toIso8601String(),
//       };
// }

// To parse this JSON data, do
//
//     final emotionsResponse = emotionsResponseFromJson(jsonString);

import 'dart:convert';

class EmotionsResponse {
  final String? message;
  final String? status;
  final Data? data;

  EmotionsResponse({
    this.message,
    this.status,
    this.data,
  });

  EmotionsResponse copyWith({
    String? message,
    String? status,
    Data? data,
  }) =>
      EmotionsResponse(
        message: message ?? this.message,
        status: status ?? this.status,
        data: data ?? this.data,
      );

  factory EmotionsResponse.fromRawJson(String str) =>
      EmotionsResponse.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory EmotionsResponse.fromJson(Map<String, dynamic> json) =>
      EmotionsResponse(
        message: json["message"],
        status: json["status"],
        data: json["data"] == null ? null : Data.fromJson(json["data"]),
      );

  Map<String, dynamic> toJson() => {
        "message": message,
        "status": status,
        "data": data?.toJson(),
      };
}

class Data {
  final List<Type>? type;
  final bool? isCheckInToday;
  final dynamic latestCheckInType;
  final dynamic latestCheckInTypeId;
  final List<Question>? question;
  final LatestCheckInData? latestCheckInData;
  final DateTime? latestCheckInDate;
  final bool? isOnboarding;
  final String? firstName;
  final String? lastName;

  Data(
      {this.type,
      this.isCheckInToday,
      this.latestCheckInType,
      this.latestCheckInTypeId,
      this.question,
      this.latestCheckInData,
      this.latestCheckInDate,
      this.isOnboarding,
      this.firstName,
      this.lastName});

  Data copyWith(
          {List<Type>? type,
          bool? isCheckInToday,
          dynamic latestCheckInType,
          dynamic latestCheckInTypeId,
          List<Question>? question,
          final LatestCheckInData? latestCheckInData,
          DateTime? latestCheckInDate,
          bool? isOnboarding,
          String? firstName,
          String? lastName}) =>
      Data(
        type: type ?? this.type,
        isCheckInToday: isCheckInToday ?? this.isCheckInToday,
        latestCheckInType: latestCheckInType ?? this.latestCheckInType,
        latestCheckInTypeId: latestCheckInTypeId ?? this.latestCheckInTypeId,
        question: question ?? this.question,
        latestCheckInData: latestCheckInData ?? this.latestCheckInData,
        latestCheckInDate: latestCheckInDate ?? this.latestCheckInDate,
        isOnboarding: isOnboarding ?? this.isOnboarding,
        firstName: firstName ?? this.firstName,
        lastName: lastName ?? this.lastName,
      );

  factory Data.fromRawJson(String str) => Data.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory Data.fromJson(Map<String, dynamic> json) => Data(
        type: json["type"] == null
            ? []
            : List<Type>.from(json["type"]!.map((x) => Type.fromJson(x))),
        isCheckInToday: json["is_check_in_today"],
        latestCheckInType: json["latest_check_in_type"],
        latestCheckInTypeId: json["latest_check_in_type_id"],
        question: json["question"] == null
            ? []
            : List<Question>.from(
                json["question"]!.map((x) => Question.fromJson(x))),
        latestCheckInData: json["latest_check_in_data"] == null
            ? null
            : LatestCheckInData.fromJson(json["latest_check_in_data"]),
        latestCheckInDate: json["latest_check_in_date"] == null
            ? null
            : DateTime.parse(json["latest_check_in_date"]),
        isOnboarding: json["is_onboarding"],
        firstName: json["first_name"],
        lastName: json["last_name"],
      );

  Map<String, dynamic> toJson() => {
        "type": type == null
            ? []
            : List<dynamic>.from(type!.map((x) => x.toJson())),
        "is_check_in_today": isCheckInToday,
        "latest_check_in_type": latestCheckInType,
        "latest_check_in_type_id": latestCheckInTypeId,
        "question": question == null
            ? []
            : List<dynamic>.from(question!.map((x) => x.toJson())),
        "latest_check_in_data": latestCheckInData?.toJson(),
        "latest_check_in_date": latestCheckInDate?.toIso8601String(),
        "is_onboarding": isOnboarding,
        "first_name": firstName,
        "last_name": lastName,
      };
}

class LatestCheckInData {
  final Feeling? feeling;
  final List<Question>? questions;

  LatestCheckInData({
    this.feeling,
    this.questions,
  });

  LatestCheckInData copyWith({
    Feeling? feeling,
    List<Question>? questions,
  }) =>
      LatestCheckInData(
        feeling: feeling ?? this.feeling,
        questions: questions ?? this.questions,
      );

  factory LatestCheckInData.fromRawJson(String str) =>
      LatestCheckInData.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory LatestCheckInData.fromJson(Map<String, dynamic> json) =>
      LatestCheckInData(
        feeling:
            json["feeling"] == null ? null : Feeling.fromJson(json["feeling"]),
        questions: json["questions"] == null
            ? []
            : List<Question>.from(
                json["questions"]!.map((x) => Question.fromJson(x))),
      );

  Map<String, dynamic> toJson() => {
        "feeling": feeling?.toJson(),
        "questions": questions == null
            ? []
            : List<dynamic>.from(questions!.map((x) => x.toJson())),
      };
}

class Question {
  final String? id;
  final String? question;
  final DateTime? createdAt;
  final DateTime? updatedAt;
  final bool? isOptional;
  final bool? isNote;
  final bool? isOther;
  final String? description;
  final String? type;
  final int? orderOfQuestion;
  final String? questionId;
  final List<Feeling>? answers;
  final String? other;
  final String? note;

  Question({
    this.id,
    this.question,
    this.createdAt,
    this.updatedAt,
    this.isOptional,
    this.isNote,
    this.isOther,
    this.description,
    this.type,
    this.orderOfQuestion,
    this.questionId,
    this.answers,
    this.other,
    this.note,
  });

  Question copyWith({
    String? id,
    String? question,
    DateTime? createdAt,
    DateTime? updatedAt,
    bool? isOptional,
    bool? isNote,
    bool? isOther,
    String? description,
    String? type,
    int? orderOfQuestion,
    String? questionId,
    List<Feeling>? answers,
    dynamic other,
    dynamic note,
  }) =>
      Question(
        id: id ?? this.id,
        question: question ?? this.question,
        createdAt: createdAt ?? this.createdAt,
        updatedAt: updatedAt ?? this.updatedAt,
        isOptional: isOptional ?? this.isOptional,
        isNote: isNote ?? this.isNote,
        isOther: isOther ?? this.isOther,
        description: description ?? this.description,
        type: type ?? this.type,
        orderOfQuestion: orderOfQuestion ?? this.orderOfQuestion,
        questionId: questionId ?? this.questionId,
        answers: answers ?? this.answers,
        other: other ?? this.other,
        note: note ?? this.note,
      );

  factory Question.fromRawJson(String str) =>
      Question.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory Question.fromJson(Map<String, dynamic> json) => Question(
        id: json["id"],
        question: json["question"],
        createdAt: json["created_at"] == null
            ? null
            : DateTime.parse(json["created_at"]),
        updatedAt: json["updated_at"] == null
            ? null
            : DateTime.parse(json["updated_at"]),
        isOptional: json["is_optional"],
        isNote: json["is_note"],
        isOther: json["is_other"],
        description: json["description"],
        type: json["type"],
        orderOfQuestion: json["order_of_question"],
        questionId: json["question_id"],
        answers: json["answers"] == null
            ? []
            : List<Feeling>.from(
                json["answers"]!.map((x) => Feeling.fromJson(x))),
        other: json["other"],
        note: json["note"],
      );

  Map<String, dynamic> toJson() => {
        "id": id,
        "question": question,
        "created_at": createdAt?.toIso8601String(),
        "updated_at": updatedAt?.toIso8601String(),
        "is_optional": isOptional,
        "is_note": isNote,
        "is_other": isOther,
        "description": description,
        "type": type,
        "order_of_question": orderOfQuestion,
        "question_id": questionId,
        "answers": answers == null
            ? []
            : List<dynamic>.from(answers!.map((x) => x.toJson())),
        "other": other,
        "note": note,
      };
}

class Type {
  final String? id;
  final String? name;
  final String? type;
  final int? point;
  final String? description;
  final DateTime? createdAt;
  final DateTime? updatedAt;
  final String? iconUrl;

  Type({
    this.id,
    this.name,
    this.type,
    this.point,
    this.description,
    this.createdAt,
    this.updatedAt,
    this.iconUrl,
  });

  Type copyWith({
    String? id,
    String? name,
    String? type,
    int? point,
    String? description,
    DateTime? createdAt,
    DateTime? updatedAt,
    String? iconUrl,
  }) =>
      Type(
        id: id ?? this.id,
        name: name ?? this.name,
        type: type ?? this.type,
        point: point ?? this.point,
        description: description ?? this.description,
        createdAt: createdAt ?? this.createdAt,
        updatedAt: updatedAt ?? this.updatedAt,
        iconUrl: iconUrl ?? this.iconUrl,
      );

  factory Type.fromRawJson(String str) => Type.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory Type.fromJson(Map<String, dynamic> json) => Type(
        id: json["id"],
        name: json["name"],
        type: json["type"],
        point: json["point"],
        description: json["description"],
        createdAt: json["created_at"] == null
            ? null
            : DateTime.parse(json["created_at"]),
        updatedAt: json["updated_at"] == null
            ? null
            : DateTime.parse(json["updated_at"]),
        iconUrl: json["icon_url"],
      );

  Map<String, dynamic> toJson() => {
        "id": id,
        "name": name,
        "type": type,
        "point": point,
        "description": description,
        "created_at": createdAt?.toIso8601String(),
        "updated_at": updatedAt?.toIso8601String(),
        "icon_url": iconUrl,
      };
}

class Feeling {
  final String? id;
  final String? name;
  final String? type;
  final String? answerCheckInTypesId;

  Feeling({
    this.id,
    this.name,
    this.type,
    this.answerCheckInTypesId,
  });

  Feeling copyWith({
    String? id,
    String? name,
    String? type,
    String? answerCheckInTypesId,
  }) =>
      Feeling(
        id: id ?? this.id,
        name: name ?? this.name,
        type: type ?? this.type,
        answerCheckInTypesId: answerCheckInTypesId ?? this.answerCheckInTypesId,
      );

  factory Feeling.fromRawJson(String str) => Feeling.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory Feeling.fromJson(Map<String, dynamic> json) => Feeling(
        id: json["id"],
        name: json["name"],
        type: json["type"],
        answerCheckInTypesId: json["answer_check_in_type_id"],
      );

  Map<String, dynamic> toJson() => {
        "id": id,
        "name": name,
        "type": type,
        "answer_check_in_type_id": answerCheckInTypesId,
      };
}

// class Question {
//     final String? questionId;
//     final List<Feeling>? answers;
//     final dynamic other;
//     final dynamic note;

//     Question({
//         this.questionId,
//         this.answers,
//         this.other,
//         this.note,
//     });

//     Question copyWith({
//         String? questionId,
//         List<Feeling>? answers,
//         dynamic other,
//         dynamic note,
//     }) =>
//         Question(
//             questionId: questionId ?? this.questionId,
//             answers: answers ?? this.answers,
//             other: other ?? this.other,
//             note: note ?? this.note,
//         );

//     factory Question.fromRawJson(String str) => Question.fromJson(json.decode(str));

//     String toRawJson() => json.encode(toJson());

//     factory Question.fromJson(Map<String, dynamic> json) => Question(
//         questionId: json["question_id"],
//         answers: json["answers"] == null ? [] : List<Feeling>.from(json["answers"]!.map((x) => Feeling.fromJson(x))),
//         other: json["other"],
//         note: json["note"],
//     );

//     Map<String, dynamic> toJson() => {
//         "question_id": questionId,
//         "answers": answers == null ? [] : List<dynamic>.from(answers!.map((x) => x.toJson())),
//         "other": other,
//         "note": note,
//     };
// }

// }
