import 'dart:convert';

class ExercisesResponse {
  String? message;
  String? status;
  Data? data;

  ExercisesResponse({
    this.message,
    this.status,
    this.data,
  });

  factory ExercisesResponse.fromRawJson(String str) =>
      ExercisesResponse.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory ExercisesResponse.fromJson(Map<String, dynamic> json) =>
      ExercisesResponse(
        message: json["message"],
        status: json["status"],
        data: json["data"] == null ? null : Data.fromJson(json["data"]),
      );

  Map<String, dynamic> toJson() => {
        "message": message,
        "status": status,
        "data": data?.toJson(),
      };
}

class Data {
  List<Exercise?>? exercises;
  Tag? tag;

  Data({
    this.exercises,
    this.tag,
  });

  factory Data.fromRawJson(String str) => Data.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory Data.fromJson(Map<String, dynamic> json) => Data(
        exercises: json["exercises"] == null
            ? []
            : List<Exercise?>.from(json["exercises"]!
                .map((x) => x == null ? null : Exercise.fromJson(x))),
        tag: json["tag"] == null ? null : Tag.fromJson(json["tag"]),
      );

  Map<String, dynamic> toJson() => {
        "exercises": exercises == null
            ? []
            : List<dynamic>.from(exercises!.map((x) => x?.toJson())),
        "tag": tag?.toJson(),
      };
}

class Exercise {
  String? exerciseId;
  String? exerciseTitle;
  String? mediaType;
  String? mediaUrl;
  String? thumbnailUrl;
  String? mediaDuration;
  String? categoryName;
  String? exerciseType;

  Exercise({
    this.exerciseId,
    this.exerciseTitle,
    this.mediaType,
    this.mediaUrl,
    this.thumbnailUrl,
    this.mediaDuration,
    this.categoryName,
    this.exerciseType,
  });

  factory Exercise.fromRawJson(String str) =>
      Exercise.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory Exercise.fromJson(Map<String, dynamic> json) => Exercise(
        exerciseId: json["exercise_id"],
        exerciseTitle: json["exercise_title"],
        mediaType: json["media_type"],
        mediaUrl: json["media_url"],
        thumbnailUrl: json["thumbnail_url"],
        mediaDuration: json["media_duration"],
        categoryName: json["category_name"],
        exerciseType: json["exercise_type"],
      );

  Map<String, dynamic> toJson() => {
        "exercise_id": exerciseId,
        "exercise_title": exerciseTitle,
        "media_type": mediaType,
        "media_url": mediaUrl,
        "thumbnail_url": thumbnailUrl,
        "media_duration": mediaDuration,
        "category_name": categoryName,
        "exercise_type": exerciseType,
      };
}

class Tag {
  String? dailyCheckInId;
  String? userId;
  List<Answer>? answers;

  Tag({
    this.dailyCheckInId,
    this.userId,
    this.answers,
  });

  factory Tag.fromRawJson(String str) => Tag.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory Tag.fromJson(Map<String, dynamic> json) => Tag(
        dailyCheckInId: json["daily_check_in_id"],
        userId: json["user_id"],
        answers: json["answers"] == null
            ? []
            : List<Answer>.from(
                json["answers"]!.map((x) => Answer.fromJson(x))),
      );

  Map<String, dynamic> toJson() => {
        "daily_check_in_id": dailyCheckInId,
        "user_id": userId,
        "answers": answers == null
            ? []
            : List<dynamic>.from(answers!.map((x) => x.toJson())),
      };
}

class Answer {
  final String? id;
  final String? name;
  final String? answerId;
  final String? type;
  final String? iconUrl;

  Answer({
    this.id,
    this.name,
    this.answerId,
    this.type,
    this.iconUrl,
  });

  Answer copyWith({
    String? id,
    String? name,
    String? answerId,
    String? type,
    String? iconUrl,
  }) =>
      Answer(
        id: id ?? this.id,
        name: name ?? this.name,
        answerId: answerId ?? this.answerId,
        type: type ?? this.type,
        iconUrl: iconUrl ?? this.iconUrl,
      );

  factory Answer.fromRawJson(String str) => Answer.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory Answer.fromJson(Map<String, dynamic> json) => Answer(
        id: json["id"],
        name: json["name"],
        answerId: json["answer_id"],
        type: json["type"],
        iconUrl: json["icon_url"],
      );

  Map<String, dynamic> toJson() => {
        "id": id,
        "name": name,
        "answer_id": answerId,
        "type": type,
        "icon_url": iconUrl,
      };
}
