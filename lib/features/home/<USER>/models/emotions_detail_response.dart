import 'dart:convert';

// class EmotionsDetailResponse {
//   String? message;
//   String? status;
//   List<Datum>? data;

//   EmotionsDetailResponse({
//     this.message,
//     this.status,
//     this.data,
//   });

//   factory EmotionsDetailResponse.fromRawJson(String str) =>
//       EmotionsDetailResponse.fromJson(json.decode(str));

//   String toRawJson() => json.encode(toJson());

//   factory EmotionsDetailResponse.fromJson(Map<String, dynamic> json) =>
//       EmotionsDetailResponse(
//         message: json["message"],
//         status: json["status"],
//         data: json["data"] == null
//             ? []
//             : List<Datum>.from(json["data"]!.map((x) => Datum.fromJson(x))),
//       );

//   Map<String, dynamic> toJson() => {
//         "message": message,
//         "status": status,
//         "data": data == null
//             ? []
//             : List<dynamic>.from(data!.map((x) => x.toJson())),
//       };
// }

// class Datum {
//   String? questionId;
//   String? question;
//   List<Answer>? answers;
//   bool? isOptional;
//   bool? isNote;
//   bool? isOther;

//   Datum({
//     this.questionId,
//     this.question,
//     this.answers,
//     this.isOptional,
//     this.isNote,
//     this.isOther,
//   });

//   factory Datum.fromRawJson(String str) => Datum.fromJson(json.decode(str));

//   String toRawJson() => json.encode(toJson());

//   factory Datum.fromJson(Map<String, dynamic> json) => Datum(
//         questionId: json["question_id"],
//         question: json["question"],
//         answers: json["answers"] == null
//             ? []
//             : List<Answer>.from(
//                 json["answers"]!.map((x) => Answer.fromJson(x))),
//         isOptional: json["is_optional"],
//         isNote: json["is_note"],
//         isOther: json["is_other"],
//       );

//   Map<String, dynamic> toJson() => {
//         "question_id": questionId,
//         "question": question,
//         "answers": answers == null
//             ? []
//             : List<dynamic>.from(answers!.map((x) => x.toJson())),
//         "is_optional": isOptional,
//         "is_note": isNote,
//         "is_other": isOther,
//       };
// }

// class Answer {
//   String? answerId;
//   String? answer;

//   Answer({
//     this.answerId,
//     this.answer,
//   });

//   factory Answer.fromRawJson(String str) => Answer.fromJson(json.decode(str));

//   String toRawJson() => json.encode(toJson());

//   factory Answer.fromJson(Map<String, dynamic> json) => Answer(
//         answerId: json["answer_id"],
//         answer: json["answer"],
//       );

//   Map<String, dynamic> toJson() => {
//         "answer_id": answerId,
//         "answer": answer,
//       };
// }
// To parse this JSON data, do
//
//     final emotionsDetailResponse = emotionsDetailResponseFromJson(jsonString);

EmotionsDetailResponse emotionsDetailResponseFromJson(String str) =>
    EmotionsDetailResponse.fromJson(json.decode(str));

String emotionsDetailResponseToJson(EmotionsDetailResponse data) =>
    json.encode(data.toJson());

class EmotionsDetailResponse {
  final String? message;
  final String? status;
  final List<Datum>? data;

  EmotionsDetailResponse({
    this.message,
    this.status,
    this.data,
  });

  EmotionsDetailResponse copyWith({
    String? message,
    String? status,
    List<Datum>? data,
  }) =>
      EmotionsDetailResponse(
        message: message ?? this.message,
        status: status ?? this.status,
        data: data ?? this.data,
      );

  factory EmotionsDetailResponse.fromJson(Map<String, dynamic> json) =>
      EmotionsDetailResponse(
        message: json["message"],
        status: json["status"],
        data: json["data"] == null
            ? []
            : List<Datum>.from(json["data"]!.map((x) => Datum.fromJson(x))),
      );

  Map<String, dynamic> toJson() => {
        "message": message,
        "status": status,
        "data": data == null
            ? []
            : List<dynamic>.from(data!.map((x) => x.toJson())),
      };
}

class Datum {
  final String? questionId;
  final String? question;
  final List<Answer>? answers;
  final bool? isOptional;
  final bool? isNote;
  final bool? isOther;
  final String? description;
  final String? type;
  final int? orderOfQuestion;

  Datum({
    this.questionId,
    this.question,
    this.answers,
    this.isOptional,
    this.isNote,
    this.isOther,
    this.description,
    this.type,
    this.orderOfQuestion,
  });

  Datum copyWith({
    String? questionId,
    String? question,
    List<Answer>? answers,
    bool? isOptional,
    bool? isNote,
    bool? isOther,
    String? description,
    String? type,
    int? orderOfQuestion,
  }) =>
      Datum(
        questionId: questionId ?? this.questionId,
        question: question ?? this.question,
        answers: answers ?? this.answers,
        isOptional: isOptional ?? this.isOptional,
        isNote: isNote ?? this.isNote,
        isOther: isOther ?? this.isOther,
        description: description ?? this.description,
        type: type ?? this.type,
        orderOfQuestion: orderOfQuestion ?? this.orderOfQuestion,
      );

  factory Datum.fromJson(Map<String, dynamic> json) => Datum(
        questionId: json["question_id"],
        question: json["question"],
        answers: json["answers"] == null
            ? []
            : List<Answer>.from(
                json["answers"]!.map((x) => Answer.fromJson(x))),
        isOptional: json["is_optional"],
        isNote: json["is_note"],
        isOther: json["is_other"],
        description: json["description"],
        type: json["type"],
        orderOfQuestion: json["order_of_question"],
      );

  Map<String, dynamic> toJson() => {
        "question_id": questionId,
        "question": question,
        "answers": answers == null
            ? []
            : List<dynamic>.from(answers!.map((x) => x.toJson())),
        "is_optional": isOptional,
        "is_note": isNote,
        "is_other": isOther,
        "description": description,
        "type": type,
        "order_of_question": orderOfQuestion,
      };
}

class Answer {
  final String? answerId;
  final String? answer;
  final String? answerCheckInTypesId;
  final String? iconUrl;

  Answer({
    this.answerId,
    this.answer,
    this.answerCheckInTypesId,
    this.iconUrl,
  });

  Answer copyWith({
    String? answerId,
    String? answer,
    String? answerCheckInTypesId,
    String? iconUrl,
  }) =>
      Answer(
        answerId: answerId ?? this.answerId,
        answer: answer ?? this.answer,
        answerCheckInTypesId: answerCheckInTypesId ?? this.answerCheckInTypesId,
        iconUrl: iconUrl ?? this.iconUrl,
      );

  factory Answer.fromRawJson(String str) => Answer.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory Answer.fromJson(Map<String, dynamic> json) => Answer(
        answerId: json["answer_id"],
        answer: json["answer"],
        answerCheckInTypesId: json["answer_check_in_types_id"],
        iconUrl: json["icon_url"],
      );

  Map<String, dynamic> toJson() => {
        "answer_id": answerId,
        "answer": answer,
        "answer_check_in_types_id": answerCheckInTypesId,
        "icon_url": iconUrl,
      };
}
