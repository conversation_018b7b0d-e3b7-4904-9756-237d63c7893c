import 'dart:convert';

class ViewedStatusResponse {
  String? message;
  String? status;
  Data? data;

  ViewedStatusResponse({
    this.message,
    this.status,
    this.data,
  });

  factory ViewedStatusResponse.fromRawJson(String str) =>
      ViewedStatusResponse.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory ViewedStatusResponse.fromJson(Map<String, dynamic> json) =>
      ViewedStatusResponse(
        message: json["message"],
        status: json["status"],
        data: json["data"] == null ? null : Data.fromJson(json["data"]),
      );

  Map<String, dynamic> toJson() => {
        "message": message,
        "status": status,
        "data": data?.toJson(),
      };
}

class Data {
  bool? isAtLeastOneViewed;

  Data({
    this.isAtLeastOneViewed,
  });

  factory Data.fromRawJson(String str) => Data.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory Data.fromJson(Map<String, dynamic> json) => Data(
        isAtLeastOneViewed: json["is_at_least_one_viewed"],
      );

  Map<String, dynamic> toJson() => {
        "is_at_least_one_viewed": isAtLeastOneViewed,
      };
}
