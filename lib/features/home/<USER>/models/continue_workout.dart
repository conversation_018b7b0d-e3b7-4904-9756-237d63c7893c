import 'dart:convert';

class ContinueWorkoutResponse {
  final String? message;
  final String? status;
  final Data? data;

  ContinueWorkoutResponse({
    this.message,
    this.status,
    this.data,
  });

  ContinueWorkoutResponse copyWith({
    String? message,
    String? status,
    Data? data,
  }) =>
      ContinueWorkoutResponse(
        message: message ?? this.message,
        status: status ?? this.status,
        data: data ?? this.data,
      );

  factory ContinueWorkoutResponse.fromRawJson(String str) =>
      ContinueWorkoutResponse.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory ContinueWorkoutResponse.fromJson(Map<String, dynamic> json) =>
      ContinueWorkoutResponse(
        message: json["message"],
        status: json["status"],
        data: json["data"] == null ? null : Data.fromJson(json["data"]),
      );

  Map<String, dynamic> toJson() => {
        "message": message,
        "status": status,
        "data": data?.toJson(),
      };
}

class Data {
  final String? seriesId;
  final String? seriesTitle;
  final String? seriesDescription;
  final String? seriesImageUrl;
  final bool? seriesIsCompleted;
  final int? totalWorkouts;
  final int? totalCompletedWorkouts;
  final List<Workout>? workouts;

  Data({
    this.seriesId,
    this.seriesTitle,
    this.seriesDescription,
    this.seriesImageUrl,
    this.seriesIsCompleted,
    this.totalWorkouts,
    this.totalCompletedWorkouts,
    this.workouts,
  });

  Data copyWith({
    String? seriesId,
    String? seriesTitle,
    String? seriesDescription,
    String? seriesImageUrl,
    bool? seriesIsCompleted,
    int? totalWorkouts,
    int? totalCompletedWorkouts,
    List<Workout>? workouts,
  }) =>
      Data(
        seriesId: seriesId ?? this.seriesId,
        seriesTitle: seriesTitle ?? this.seriesTitle,
        seriesDescription: seriesDescription ?? this.seriesDescription,
        seriesImageUrl: seriesImageUrl ?? this.seriesImageUrl,
        seriesIsCompleted: seriesIsCompleted ?? this.seriesIsCompleted,
        totalWorkouts: totalWorkouts ?? this.totalWorkouts,
        totalCompletedWorkouts:
            totalCompletedWorkouts ?? this.totalCompletedWorkouts,
        workouts: workouts ?? this.workouts,
      );

  factory Data.fromRawJson(String str) => Data.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory Data.fromJson(Map<String, dynamic> json) => Data(
        seriesId: json["series_id"],
        seriesTitle: json["series_title"],
        seriesDescription: json["series_description"],
        seriesImageUrl: json["series_image_url"],
        seriesIsCompleted: json["series_is_completed"],
        totalWorkouts: json["total_workouts"],
        totalCompletedWorkouts: json["total_completed_workouts"],
        workouts: json["workouts"] == null
            ? []
            : List<Workout>.from(
                json["workouts"]!.map((x) => Workout.fromJson(x))),
      );

  Map<String, dynamic> toJson() => {
        "series_id": seriesId,
        "series_title": seriesTitle,
        "series_description": seriesDescription,
        "series_image_url": seriesImageUrl,
        "series_is_completed": seriesIsCompleted,
        "total_workouts": totalWorkouts,
        "total_completed_workouts": totalCompletedWorkouts,
        "workouts": workouts == null
            ? []
            : List<dynamic>.from(workouts!.map((x) => x.toJson())),
      };
}

class Workout {
  final String? workoutId;
  final DateTime? completedAt;
  final int? orderOfExercise;
  final int? day;
  final String? exerciseId;
  final bool? isCompleted;

  Workout({
    this.workoutId,
    this.completedAt,
    this.orderOfExercise,
    this.day,
    this.exerciseId,
    this.isCompleted,
  });

  Workout copyWith({
    String? workoutId,
    dynamic completedAt,
    int? orderOfExercise,
    int? day,
    String? exerciseId,
    bool? isCompleted,
  }) =>
      Workout(
        workoutId: workoutId ?? this.workoutId,
        completedAt: completedAt ?? this.completedAt,
        orderOfExercise: orderOfExercise ?? this.orderOfExercise,
        day: day ?? this.day,
        exerciseId: exerciseId ?? this.exerciseId,
        isCompleted: isCompleted ?? this.isCompleted,
      );

  factory Workout.fromRawJson(String str) => Workout.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory Workout.fromJson(Map<String, dynamic> json) => Workout(
        workoutId: json["workout_id"],
        completedAt: json["completed_at"] == null
            ? null
            : DateTime.parse(json["completed_at"]),
        orderOfExercise: json["order_of_exercise"],
        day: json["day"],
        exerciseId: json["exercise_id"],
        isCompleted: json["is_completed"],
      );

  Map<String, dynamic> toJson() => {
        "workout_id": workoutId,
        "completed_at": completedAt?.toIso8601String(),
        "order_of_exercise": orderOfExercise,
        "day": day,
        "exercise_id": exerciseId,
        "is_completed": isCompleted,
      };
}
