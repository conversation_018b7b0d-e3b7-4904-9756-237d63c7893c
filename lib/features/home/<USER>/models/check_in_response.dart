// import 'dart:convert';

// class CheckInResponse {
//   final String? message;
//   final String? status;
//   final Data? data;

//   CheckInResponse({
//     this.message,
//     this.status,
//     this.data,
//   });

//   CheckInResponse copyWith({
//     String? message,
//     String? status,
//     Data? data,
//   }) =>
//       CheckInResponse(
//         message: message ?? this.message,
//         status: status ?? this.status,
//         data: data ?? this.data,
//       );

//   factory CheckInResponse.fromRawJson(String str) =>
//       CheckInResponse.fromJson(json.decode(str));

//   String toRawJson() => json.encode(toJson());

//   factory CheckInResponse.fromJson(Map<String, dynamic> json) =>
//       CheckInResponse(
//         message: json["message"],
//         status: json["status"],
//         data: json["data"] == null ? null : Data.fromJson(json["data"]),
//       );

//   Map<String, dynamic> toJson() => {
//         "message": message,
//         "status": status,
//         "data": data?.toJson(),
//       };
// }

// class Data {
//   final String? emotion;
//   final int? checkInCount;

//   Data({
//     this.emotion,
//     this.checkInCount,
//   });

//   Data copyWith({
//     String? emotion,
//     int? checkInCount,
//   }) =>
//       Data(
//         emotion: emotion ?? this.emotion,
//         checkInCount: checkInCount ?? this.checkInCount,
//       );

//   factory Data.fromRawJson(String str) => Data.fromJson(json.decode(str));

//   String toRawJson() => json.encode(toJson());

//   factory Data.fromJson(Map<String, dynamic> json) => Data(
//         emotion: json["emotion"],
//         checkInCount: json["check_in_count"],
//       );

//   Map<String, dynamic> toJson() => {
//         "emotion": emotion,
//         "check_in_count": checkInCount,
//       };
// }
import 'dart:convert';

class CheckInResponse {
  final String? message;
  final String? status;
  final Data? data;

  CheckInResponse({
    this.message,
    this.status,
    this.data,
  });

  CheckInResponse copyWith({
    String? message,
    String? status,
    Data? data,
  }) =>
      CheckInResponse(
        message: message ?? this.message,
        status: status ?? this.status,
        data: data ?? this.data,
      );

  factory CheckInResponse.fromRawJson(String str) =>
      CheckInResponse.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory CheckInResponse.fromJson(Map<String, dynamic> json) =>
      CheckInResponse(
        message: json["message"],
        status: json["status"],
        data: json["data"] == null ? null : Data.fromJson(json["data"]),
      );

  Map<String, dynamic> toJson() => {
        "message": message,
        "status": status,
        "data": data?.toJson(),
      };
}

class Data {
  final String? emotion;
  final int? checkInCount;
  final Notification? notification;

  Data({
    this.emotion,
    this.checkInCount,
    this.notification,
  });

  Data copyWith({
    String? emotion,
    int? checkInCount,
    Notification? notification,
  }) =>
      Data(
        emotion: emotion ?? this.emotion,
        checkInCount: checkInCount ?? this.checkInCount,
        notification: notification ?? this.notification,
      );

  factory Data.fromRawJson(String str) => Data.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory Data.fromJson(Map<String, dynamic> json) => Data(
        emotion: json["emotion"],
        checkInCount: json["check_in_count"],
        notification: json["notification"] == null
            ? null
            : Notification.fromJson(json["notification"]),
      );

  Map<String, dynamic> toJson() => {
        "emotion": emotion,
        "check_in_count": checkInCount,
        "notification": notification?.toJson(),
      };
}

class Notification {
  final String? id;
  final String? category;
  final String? type;
  final bool? isActive;
  final String? title;
  final String? body;
  final dynamic onTapUrl;
  final bool? pushNotification;
  final bool? inAppNotification;
  final dynamic scheduledTime;
  final dynamic scheduledDate;
  final DateTime? createdAt;
  final DateTime? updatedAt;
  final bool? isPush;
  final bool? allUsers;
  final bool? isAdmin;

  Notification({
    this.id,
    this.category,
    this.type,
    this.isActive,
    this.title,
    this.body,
    this.onTapUrl,
    this.pushNotification,
    this.inAppNotification,
    this.scheduledTime,
    this.scheduledDate,
    this.createdAt,
    this.updatedAt,
    this.isPush,
    this.allUsers,
    this.isAdmin,
  });

  Notification copyWith({
    String? id,
    String? category,
    String? type,
    bool? isActive,
    String? title,
    String? body,
    dynamic onTapUrl,
    bool? pushNotification,
    bool? inAppNotification,
    dynamic scheduledTime,
    dynamic scheduledDate,
    DateTime? createdAt,
    DateTime? updatedAt,
    bool? isPush,
    bool? allUsers,
    bool? isAdmin,
  }) =>
      Notification(
        id: id ?? this.id,
        category: category ?? this.category,
        type: type ?? this.type,
        isActive: isActive ?? this.isActive,
        title: title ?? this.title,
        body: body ?? this.body,
        onTapUrl: onTapUrl ?? this.onTapUrl,
        pushNotification: pushNotification ?? this.pushNotification,
        inAppNotification: inAppNotification ?? this.inAppNotification,
        scheduledTime: scheduledTime ?? this.scheduledTime,
        scheduledDate: scheduledDate ?? this.scheduledDate,
        createdAt: createdAt ?? this.createdAt,
        updatedAt: updatedAt ?? this.updatedAt,
        isPush: isPush ?? this.isPush,
        allUsers: allUsers ?? this.allUsers,
        isAdmin: isAdmin ?? this.isAdmin,
      );

  factory Notification.fromRawJson(String str) =>
      Notification.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory Notification.fromJson(Map<String, dynamic> json) => Notification(
        id: json["id"],
        category: json["category"],
        type: json["type"],
        isActive: json["is_active"],
        title: json["title"],
        body: json["body"],
        onTapUrl: json["on_tap_url"],
        pushNotification: json["push_notification"],
        inAppNotification: json["in_app_notification"],
        scheduledTime: json["scheduled_time"],
        scheduledDate: json["scheduled_date"],
        createdAt: json["created_at"] == null
            ? null
            : DateTime.parse(json["created_at"]),
        updatedAt: json["updated_at"] == null
            ? null
            : DateTime.parse(json["updated_at"]),
        isPush: json["is_push"],
        allUsers: json["all_users"],
        isAdmin: json["is_admin"],
      );

  Map<String, dynamic> toJson() => {
        "id": id,
        "category": category,
        "type": type,
        "is_active": isActive,
        "title": title,
        "body": body,
        "on_tap_url": onTapUrl,
        "push_notification": pushNotification,
        "in_app_notification": inAppNotification,
        "scheduled_time": scheduledTime,
        "scheduled_date": scheduledDate,
        "created_at": createdAt?.toIso8601String(),
        "updated_at": updatedAt?.toIso8601String(),
        "is_push": isPush,
        "all_users": allUsers,
        "is_admin": isAdmin,
      };
}
