// To parse this JSON data, do
//
//     final checkInRequest = checkInRequestFromJson(jsonString);

import 'dart:convert';

CheckInRequest checkInRequestFromJson(String str) =>
    CheckInRequest.fromJson(json.decode(str));

String checkInRequestToJson(CheckInRequest data) => json.encode(data.toJson());

class CheckInRequest {
  final String? checkInTypesId;
  final bool? isOnboarding;
  final List<Question>? questions;

  CheckInRequest({
    this.checkInTypesId,
    required this.isOnboarding,
    this.questions,
  });

  CheckInRequest copyWith({
    String? checkInTypesId,
    bool? isOnboarding,
    List<Question>? questions,
  }) =>
      CheckInRequest(
        checkInTypesId: checkInTypesId ?? this.checkInTypesId,
        isOnboarding: isOnboarding ?? this.isOnboarding,
        questions: questions ?? this.questions,
      );

  factory CheckInRequest.fromJson(Map<String, dynamic> json) => CheckInRequest(
        checkInTypesId: json["check_in_types_id"],
        isOnboarding: json["is_onboarding"],
        questions: json["questions"] == null
            ? []
            : List<Question>.from(
                json["questions"]!.map((x) => Question.fromJson(x))),
      );

  Map<String, dynamic> toJson() => {
        "check_in_types_id": checkInTypesId,
        "is_onboarding": isOnboarding,
        "questions": questions == null
            ? []
            : List<dynamic>.from(questions!.map((x) => x.toJson())),
      };
}

class Question {
  final String? questionId;
  final String? answer;
  final String? answerId;
  final String? checkInTypesId;
  final String? note;
  final String? other;

  Question({
    this.questionId,
    this.answer,
    this.answerId,
    this.checkInTypesId,
    this.note,
    this.other,
  });

  Question copyWith({
    String? questionId,
    String? answer,
    String? answerId,
    String? checkInTypesId,
    String? note,
    String? other,
  }) =>
      Question(
        questionId: questionId ?? this.questionId,
        answer: answer ?? this.answer,
        answerId: answerId ?? this.answerId,
        checkInTypesId: checkInTypesId ?? this.checkInTypesId,
        note: note ?? this.note,
        other: other ?? this.other,
      );

  factory Question.fromJson(Map<String, dynamic> json) => Question(
        questionId: json["question_id"],
        answer: json["answer"],
        answerId: json["answer_id"],
        checkInTypesId: json["check_in_types_id"],
        note: json["note"],
        other: json["other"],
      );

  Map<String, dynamic> toJson() => {
        "question_id": questionId,
        "answer": answer,
        "answer_id": answerId,
        "check_in_types_id": checkInTypesId,
        "note": note,
        "other": other,
      };
}
