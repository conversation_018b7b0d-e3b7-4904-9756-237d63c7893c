// response/village_user_response.dart
class DeleteEmotionResponse {
  DeleteEmotionResponse({
    required this.message,
    required this.status,
    this.data,
  });

  final String message;
  final String status;
  final dynamic data;

  factory DeleteEmotionResponse.fromJson(Map<String, dynamic> json) =>
      DeleteEmotionResponse(
        message: json["message"],
        status: json["status"],
        data: json["data"],
      );

  Map<String, dynamic> toJson() => {
        "message": message,
        "status": status,
        "data": data,
      };
}
