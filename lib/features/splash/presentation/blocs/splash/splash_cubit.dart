import 'package:bloc/bloc.dart';
import 'package:flutter_timezone/flutter_timezone.dart';
import 'package:gotcha_mfg_app/features/splash/data/models/info_request.dart';
import 'package:gotcha_mfg_app/features/splash/data/models/info_response.dart';
import 'package:gotcha_mfg_app/features/splash/domain/usecases/info_usecase.dart';
import 'package:gotcha_mfg_app/features/splash/presentation/pages/splash_page.dart';

import '../../../../../core/firebase_push.dart';
import '../../../../../core/mixpanel_service.dart';
import '../../../../../core/storage/token_manager.dart';
import '../../../../../core/utils/device_info.dart';
import '../../../../../locator.dart';
import '../../../../../core/utils/app_print.dart' as app_print;

part 'splash_state.dart';

/// Splash Cubit
class SplashCubit extends Cubit<SplashState> {
  /// Constructor
  SplashCubit(
    this._infoUseCase,
    // this._onboardingUsecase,
  ) : super(SplashInitial());

  final InfoUseCase _infoUseCase;

  /// Info
  Future<void> info(InfoRequest request) async {
    emit(SplashLoading());
    final result = await _infoUseCase.call(request);
    if (result.isSuccess) {
      emit(SplashInfoLoaded(result.data!));
    } else {
      emit(SplashInfoError());
    }
  }

  Future<void> rerouteToOnboarding() async {
    emit(SplashReroute());
  }

  void silentInfo() async {
    String? deviceId;
    String? deviceName;
    String? deviceVersion;
    String? uniqueId;
    String? accessToken;
    String? fcmToken;
    try {
      deviceId = await getDeviceId();
      deviceName = await getDeviceName();
      deviceVersion = await getDeviceVersion();
      uniqueId = await sl<TokenManager>().getUniqueId();
      accessToken = await sl<TokenManager>().getAccessToken();
      fcmToken = await MFGPushNotification.messaging.getToken();
    } catch (e) {
      app_print.info(e.toString());
    }
    if (uniqueId != null && accessToken != null) {
      // User is already logged in - use startup identification
      sl<MixpanelService>().identifyOnStartup(uniqueId);
      final String currentTimeZone = await FlutterTimezone.getLocalTimezone();
      var infoRequest = InfoRequest(
        deviceId: uniqueId,
        os: deviceName ?? "Device",
        osVersion: deviceVersion ?? "1",
        token: fcmToken,
        fcmId: deviceId,
        timezone: currentTimeZone,
        version: version.toString(),
      );
      await _infoUseCase.call(infoRequest);
    }
  }
}
