part of 'splash_cubit.dart';

sealed class SplashState {
  const SplashState();

  @override
  List<Object> get props => [];
}

final class SplashInitial extends SplashState {}

final class SplashLoading extends SplashState {}

final class SplashInfoLoaded extends SplashState {
  final InfoResponse response;

  const SplashInfoLoaded(this.response);
}

final class SplashInfoError extends SplashState {}

final class SplashReroute extends SplashState {}
