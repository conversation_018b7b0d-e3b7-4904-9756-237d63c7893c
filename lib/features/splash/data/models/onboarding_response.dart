import 'dart:convert';

class OnboardingResponse {
  String? message;
  String? status;
  Data? data;

  OnboardingResponse({
    this.message,
    this.status,
    this.data,
  });

  factory OnboardingResponse.fromRawJson(String str) =>
      OnboardingResponse.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory OnboardingResponse.fromJson(Map<String, dynamic> json) =>
      OnboardingResponse(
        message: json['message'],
        status: json['status'],
        data: json['data'] == null ? null : Data.fromJson(json['data']),
      );

  Map<String, dynamic> toJson() => {
        'message': message,
        'status': status,
        'data': data?.toJson(),
      };
}

class Data {
  String? accessToken;
  String? refreshToken;
  String? userId;

  Data({
    this.accessToken,
    this.refreshToken,
    this.userId,
  });

  factory Data.fromRawJson(String str) => Data.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory Data.fromJson(Map<String, dynamic> json) => Data(
        accessToken: json['access_token'],
        refreshToken: json['refresh_token'],
        userId: json['user_id'],
      );

  Map<String, dynamic> toJson() => {
        'access_token': accessToken,
        'refresh_token': refreshToken,
        'user_id': userId,
      };
}
