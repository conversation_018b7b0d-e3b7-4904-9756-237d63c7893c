// import 'dart:convert';

// class InfoResponse {
//   String? message;
//   String? status;
//   Data? data;

//   InfoResponse({
//     this.message,
//     this.status,
//     this.data,
//   });

//   factory InfoResponse.fromRawJson(String str) =>
//       InfoResponse.fromJson(json.decode(str));

//   String toRawJson() => json.encode(toJson());

//   factory InfoResponse.fromJson(Map<String, dynamic> json) => InfoResponse(
//         message: json["message"],
//         status: json["status"],
//         data: json["data"] == null ? null : Data.fromJson(json["data"]),
//       );

//   Map<String, dynamic> toJson() => {
//         "message": message,
//         "status": status,
//         "data": data?.toJson(),
//       };
// }

// class Data {
//   bool? isOnboardingComplete;
//   Miscellaneous? miscellaneous;
//   bool? isExerciseViewed;
//   Cdns? cdns;

//   Data({
//     this.isOnboardingComplete,
//     this.miscellaneous,
//     this.isExerciseViewed,
//     this.cdns,
//   });

//   factory Data.fromRawJson(String str) => Data.fromJson(json.decode(str));

//   String toRawJson() => json.encode(toJson());

//   factory Data.fromJson(Map<String, dynamic> json) => Data(
//         isOnboardingComplete: json["is_onboarding_complete"],
//         miscellaneous: json["miscellaneous"] == null
//             ? null
//             : Miscellaneous.fromJson(json["miscellaneous"]),
//         isExerciseViewed: json["is_exercise_viewed"],
//         cdns: json["cdns"] == null ? null : Cdns.fromJson(json["cdns"]),
//       );

//   Map<String, dynamic> toJson() => {
//         "is_onboarding_complete": isOnboardingComplete,
//         "miscellaneous": miscellaneous?.toJson(),
//         "is_exercise_viewed": isExerciseViewed,
//         "cdns": cdns?.toJson(),
//       };
// }

// class Cdns {
//   String? exercise;
//   String? icon;
//   String? category;
//   String? workout;

//   Cdns({
//     this.exercise,
//     this.icon,
//     this.category,
//     this.workout,
//   });

//   factory Cdns.fromRawJson(String str) => Cdns.fromJson(json.decode(str));

//   String toRawJson() => json.encode(toJson());

//   factory Cdns.fromJson(Map<String, dynamic> json) => Cdns(
//         exercise: json["exercise"],
//         icon: json["icon"],
//         category: json["category"],
//         workout: json["workout"],
//       );

//   Map<String, dynamic> toJson() => {
//         "exercise": exercise,
//         "icon": icon,
//         "category": category,
//         "workout": workout,
//       };
// }

// class Miscellaneous {
//   String? onboardingVideoUrl;
//   String? wellDoneVideoUrl;
//   String? feedbackVideoUrl;
//   int? feedbackPercentage;

//   Miscellaneous({
//     this.onboardingVideoUrl,
//     this.wellDoneVideoUrl,
//     this.feedbackVideoUrl,
//     this.feedbackPercentage,
//   });

//   factory Miscellaneous.fromRawJson(String str) =>
//       Miscellaneous.fromJson(json.decode(str));

//   String toRawJson() => json.encode(toJson());

//   factory Miscellaneous.fromJson(Map<String, dynamic> json) => Miscellaneous(
//         onboardingVideoUrl: json["onboarding_video_url"],
//         wellDoneVideoUrl: json["well_done_video_url"],
//         feedbackVideoUrl: json["feedback_video_url"],
//         feedbackPercentage: json["feedback_percentage"],
//       );

//   Map<String, dynamic> toJson() => {
//         "onboarding_video_url": onboardingVideoUrl,
//         "well_done_video_url": wellDoneVideoUrl,
//         "feedback_video_url": feedbackVideoUrl,
//         "feedback_percentage": feedbackPercentage,
//       };
// }

// import 'dart:convert';

// class InfoResponse {
//   String? message;
//   String? status;
//   Data? data;

//   InfoResponse({
//     this.message,
//     this.status,
//     this.data,
//   });

//   factory InfoResponse.fromRawJson(String str) =>
//       InfoResponse.fromJson(json.decode(str));

//   String toRawJson() => json.encode(toJson());

//   factory InfoResponse.fromJson(Map<String, dynamic> json) => InfoResponse(
//         message: json["message"],
//         status: json["status"],
//         data: json["data"] == null ? null : Data.fromJson(json["data"]),
//       );

//   Map<String, dynamic> toJson() => {
//         "message": message,
//         "status": status,
//         "data": data?.toJson(),
//       };
// }

// class Data {
//   bool? isOnboardingComplete;
//   Miscellaneous? miscellaneous;
//   bool? isExerciseViewed;
//   Cdns? cdns;
//   bool? isReminderScheduled;

//   Data({
//     this.isOnboardingComplete,
//     this.miscellaneous,
//     this.isExerciseViewed,
//     this.cdns,
//     this.isReminderScheduled,
//   });

//   factory Data.fromRawJson(String str) => Data.fromJson(json.decode(str));

//   String toRawJson() => json.encode(toJson());

//   factory Data.fromJson(Map<String, dynamic> json) => Data(
//         isOnboardingComplete: json["is_onboarding_complete"],
//         miscellaneous: json["miscellaneous"] == null
//             ? null
//             : Miscellaneous.fromJson(json["miscellaneous"]),
//         isExerciseViewed: json["is_exercise_viewed"],
//         cdns: json["cdns"] == null ? null : Cdns.fromJson(json["cdns"]),
//         isReminderScheduled: json["is_reminder_scheduled"],
//       );

//   Map<String, dynamic> toJson() => {
//         "is_onboarding_complete": isOnboardingComplete,
//         "miscellaneous": miscellaneous?.toJson(),
//         "is_exercise_viewed": isExerciseViewed,
//         "cdns": cdns?.toJson(),
//         "is_reminder_scheduled": isReminderScheduled,
//       };
// }

// class Cdns {
//   String? exercise;
//   String? icon;
//   String? category;
//   String? workout;

//   Cdns({
//     this.exercise,
//     this.icon,
//     this.category,
//     this.workout,
//   });

//   factory Cdns.fromRawJson(String str) => Cdns.fromJson(json.decode(str));

//   String toRawJson() => json.encode(toJson());

//   factory Cdns.fromJson(Map<String, dynamic> json) => Cdns(
//         exercise: json["exercise"],
//         icon: json["icon"],
//         category: json["category"],
//         workout: json["workout"],
//       );

//   Map<String, dynamic> toJson() => {
//         "exercise": exercise,
//         "icon": icon,
//         "category": category,
//         "workout": workout,
//       };
// }

// class Miscellaneous {
//   List<String>? onboardingUrls;
//   List<String>? feedbackUrls;
//   String? feedbackVideoUrl;
//   List<String>? wellDoneUrls;
//   List<String>? messages;

//   Miscellaneous({
//     this.onboardingUrls,
//     this.feedbackUrls,
//     this.feedbackVideoUrl,
//     this.wellDoneUrls,
//     this.messages,
//   });

//   factory Miscellaneous.fromRawJson(String str) =>
//       Miscellaneous.fromJson(json.decode(str));

//   String toRawJson() => json.encode(toJson());

//   factory Miscellaneous.fromJson(Map<String, dynamic> json) => Miscellaneous(
//         onboardingUrls: json["onboarding_urls"] == null
//             ? []
//             : List<String>.from(json["onboarding_urls"]!.map((x) => x)),
//         feedbackUrls: json["feedback_urls"] == null
//             ? []
//             : List<String>.from(json["feedback_urls"]!.map((x) => x)),
//         feedbackVideoUrl: json["feedback_video_url"],
//         wellDoneUrls: json["well_done_urls"] == null
//             ? []
//             : List<String>.from(json["well_done_urls"]!.map((x) => x)),
//         messages: json["messages"] == null
//             ? []
//             : List<String>.from(json["messages"]!.map((x) => x)),
//       );

//   Map<String, dynamic> toJson() => {
//         "onboarding_urls": onboardingUrls == null
//             ? []
//             : List<dynamic>.from(onboardingUrls!.map((x) => x)),
//         "feedback_urls": feedbackUrls == null
//             ? []
//             : List<dynamic>.from(feedbackUrls!.map((x) => x)),
//         "feedback_video_url": feedbackVideoUrl,
//         "well_done_urls": wellDoneUrls == null
//             ? []
//             : List<dynamic>.from(wellDoneUrls!.map((x) => x)),
//         "messages":
//             messages == null ? [] : List<dynamic>.from(messages!.map((x) => x)),
//       };
// }
import 'dart:convert';

class InfoResponse {
  final String? message;
  final String? status;
  final Data? data;

  InfoResponse({
    this.message,
    this.status,
    this.data,
  });

  InfoResponse copyWith({
    String? message,
    String? status,
    Data? data,
  }) =>
      InfoResponse(
        message: message ?? this.message,
        status: status ?? this.status,
        data: data ?? this.data,
      );

  factory InfoResponse.fromRawJson(String str) =>
      InfoResponse.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory InfoResponse.fromJson(Map<String, dynamic> json) => InfoResponse(
        message: json["message"],
        status: json["status"],
        data: json["data"] == null ? null : Data.fromJson(json["data"]),
      );

  Map<String, dynamic> toJson() => {
        "message": message,
        "status": status,
        "data": data?.toJson(),
      };
}

class Data {
  final bool? isOnboardingComplete;
  final Miscellaneous? miscellaneous;
  final bool? isExerciseViewed;
  final Cdns? cdns;
  final bool? isReminderScheduled;

  Data({
    this.isOnboardingComplete,
    this.miscellaneous,
    this.isExerciseViewed,
    this.cdns,
    this.isReminderScheduled,
  });

  Data copyWith({
    bool? isOnboardingComplete,
    Miscellaneous? miscellaneous,
    bool? isExerciseViewed,
    Cdns? cdns,
    bool? isReminderScheduled,
  }) =>
      Data(
        isOnboardingComplete: isOnboardingComplete ?? this.isOnboardingComplete,
        miscellaneous: miscellaneous ?? this.miscellaneous,
        isExerciseViewed: isExerciseViewed ?? this.isExerciseViewed,
        cdns: cdns ?? this.cdns,
        isReminderScheduled: isReminderScheduled ?? this.isReminderScheduled,
      );

  factory Data.fromRawJson(String str) => Data.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory Data.fromJson(Map<String, dynamic> json) => Data(
        isOnboardingComplete: json["is_onboarding_complete"],
        miscellaneous: json["miscellaneous"] == null
            ? null
            : Miscellaneous.fromJson(json["miscellaneous"]),
        isExerciseViewed: json["is_exercise_viewed"],
        cdns: json["cdns"] == null ? null : Cdns.fromJson(json["cdns"]),
        isReminderScheduled: json["is_reminder_scheduled"],
      );

  Map<String, dynamic> toJson() => {
        "is_onboarding_complete": isOnboardingComplete,
        "miscellaneous": miscellaneous?.toJson(),
        "is_exercise_viewed": isExerciseViewed,
        "cdns": cdns?.toJson(),
        "is_reminder_scheduled": isReminderScheduled,
      };
}

class Cdns {
  final String? exercise;
  final String? icon;
  final String? category;
  final String? workout;

  Cdns({
    this.exercise,
    this.icon,
    this.category,
    this.workout,
  });

  Cdns copyWith({
    String? exercise,
    String? icon,
    String? category,
    String? workout,
  }) =>
      Cdns(
        exercise: exercise ?? this.exercise,
        icon: icon ?? this.icon,
        category: category ?? this.category,
        workout: workout ?? this.workout,
      );

  factory Cdns.fromRawJson(String str) => Cdns.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory Cdns.fromJson(Map<String, dynamic> json) => Cdns(
        exercise: json["exercise"],
        icon: json["icon"],
        category: json["category"],
        workout: json["workout"],
      );

  Map<String, dynamic> toJson() => {
        "exercise": exercise,
        "icon": icon,
        "category": category,
        "workout": workout,
      };
}

class Miscellaneous {
  final List<String>? onboardingUrls;
  final List<String>? feedbackUrls;
  final List<String>? wellDoneUrls;
  final List<String>? messages;
  final String? minVersionIos;
  final String? latestVersionIos;
  final String? minVersionAndroid;
  final String? latestVersionAndroid;

  Miscellaneous({
    this.onboardingUrls,
    this.feedbackUrls,
    this.wellDoneUrls,
    this.messages,
    this.minVersionIos,
    this.latestVersionIos,
    this.minVersionAndroid,
    this.latestVersionAndroid,
  });

  Miscellaneous copyWith({
    List<String>? onboardingUrls,
    List<String>? feedbackUrls,
    List<String>? wellDoneUrls,
    List<String>? messages,
    String? minVersionIos,
    String? latestVersionIos,
    String? minVersionAndroid,
    String? latestVersionAndroid,
  }) =>
      Miscellaneous(
        onboardingUrls: onboardingUrls ?? this.onboardingUrls,
        feedbackUrls: feedbackUrls ?? this.feedbackUrls,
        wellDoneUrls: wellDoneUrls ?? this.wellDoneUrls,
        messages: messages ?? this.messages,
        minVersionIos: minVersionIos ?? this.minVersionIos,
        latestVersionIos: latestVersionIos ?? this.latestVersionIos,
        minVersionAndroid: minVersionAndroid ?? this.minVersionAndroid,
        latestVersionAndroid: latestVersionAndroid ?? this.latestVersionAndroid,
      );

  factory Miscellaneous.fromRawJson(String str) =>
      Miscellaneous.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory Miscellaneous.fromJson(Map<String, dynamic> json) => Miscellaneous(
        onboardingUrls: json["onboarding_urls"] == null
            ? []
            : List<String>.from(json["onboarding_urls"]!.map((x) => x)),
        feedbackUrls: json["feedback_urls"] == null
            ? []
            : List<String>.from(json["feedback_urls"]!.map((x) => x)),
        wellDoneUrls: json["well_done_urls"] == null
            ? []
            : List<String>.from(json["well_done_urls"]!.map((x) => x)),
        messages: json["messages"] == null
            ? []
            : List<String>.from(json["messages"]!.map((x) => x)),
        minVersionIos: json["min_version_ios"],
        latestVersionIos: json["latest_version_ios"],
        minVersionAndroid: json["min_version_android"],
        latestVersionAndroid: json["latest_version_android"],
      );

  Map<String, dynamic> toJson() => {
        "onboarding_urls": onboardingUrls == null
            ? []
            : List<dynamic>.from(onboardingUrls!.map((x) => x)),
        "feedback_urls": feedbackUrls == null
            ? []
            : List<dynamic>.from(feedbackUrls!.map((x) => x)),
        "well_done_urls": wellDoneUrls == null
            ? []
            : List<dynamic>.from(wellDoneUrls!.map((x) => x)),
        "messages":
            messages == null ? [] : List<dynamic>.from(messages!.map((x) => x)),
        "min_version_ios": minVersionIos,
        "latest_version_ios": latestVersionIos,
        "min_version_android": minVersionAndroid,
        "latest_version_android": latestVersionAndroid,
      };
}
