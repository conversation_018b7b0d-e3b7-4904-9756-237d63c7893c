import 'package:bloc/bloc.dart';
import 'package:gotcha_mfg_app/features/home/<USER>/models/check_in_request.dart';

part 'onboarding_data_state.dart';

class OnboardingDataCubit extends Cubit<OnboardingDataState> {
  OnboardingDataCubit() : super(const OnboardingDataLoaded(questions: []));

  void add(Question question) {
    final updatedQuestions =
        List<Question>.from((state as OnboardingDataLoaded).questions)
          ..add(question);
    emit(OnboardingDataLoaded(questions: updatedQuestions));
  }

  void remove(Question question) {
    // print('other---${question.other}');
    var updatedQuestions =
        List<Question>.from((state as OnboardingDataLoaded).questions)
          ..removeWhere((q) => q.answerId == question.answerId);
    //  updatedQuestions =
    //        List<Question>.from((state as OnboardingDataLoaded).questions)
    //       ..removeWhere((q) => q.other== question.other);
    emit(OnboardingDataLoaded(questions: updatedQuestions));
  }

  void clear() {
    emit(const OnboardingDataLoaded(questions: []));
  }
}
