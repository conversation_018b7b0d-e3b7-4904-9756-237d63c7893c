part of 'onboarding_cubit.dart';

sealed class OnboardingState extends Equatable {
  const OnboardingState();

  @override
  List<Object> get props => [];
}

final class OnboardingInitial extends OnboardingState {}

final class OnboardingLoading extends OnboardingState {}

final class OnboardingDetailLoading extends OnboardingState {}

final class OnboardingError extends OnboardingState {
  final String error;

  const OnboardingError(this.error);
}

final class OnboardingLoaded extends OnboardingState {
  final EmotionsResponse? onboardingResponse;
  final EmotionsDetailResponse? detailResponse;
  const OnboardingLoaded({
    required this.onboardingResponse,
    this.detailResponse,
  });

  OnboardingLoaded copyWith({
    EmotionsResponse? onboardingResponse,
    EmotionsDetailResponse? detailResponse,
  }) {
    return OnboardingLoaded(
      onboardingResponse: onboardingResponse ?? this.onboardingResponse,
      detailResponse: detailResponse ?? this.detailResponse,
    );
  }
}

final class OnboardingSuccess extends OnboardingState {
  final PostOnboardingResponse? onboardingResponse;
  const OnboardingSuccess({
    required this.onboardingResponse,
  });
}
