import 'package:bloc/bloc.dart';
import 'package:equatable/equatable.dart';
import 'package:gotcha_mfg_app/core/usecase/usecase.dart';
import 'package:gotcha_mfg_app/features/home/<USER>/models/emotions_detail_response.dart';
import 'package:gotcha_mfg_app/features/home/<USER>/models/emotions_response.dart';
import 'package:gotcha_mfg_app/features/onboarding/data/models/post_onboarding_response.dart';
// import 'package:gotcha_mfg_app/features/onboarding/data/models/emotion_detail_onboarding.dart';
// import 'package:gotcha_mfg_app/features/onboarding/data/models/get_onboarding.dart';
import 'package:gotcha_mfg_app/features/onboarding/domain/usecases/get_onboarding.dart';
import 'package:gotcha_mfg_app/features/onboarding/domain/usecases/onboarding_detail_emotion.dart';
import 'package:gotcha_mfg_app/features/onboarding/domain/usecases/post_onboarding.dart';

part 'onboarding_state.dart';

class OnboardingCubit extends Cubit<OnboardingState> {
  OnboardingCubit(
    this._getDetailEmotionsUsecase,
    this._onboardingUsecase,
    this._postonboardingUsecase,
  ) : super(OnboardingInitial());

  final OnboardingDetailEmotionsUsecase _getDetailEmotionsUsecase;
  final GetOnboardingUsecase _onboardingUsecase;
  final PostOnboardingUsecase _postonboardingUsecase;

  List<Question> selectedQuestions = [];

  Future<void> getDetailEmotions(String id) async {
    var previousState = state as OnboardingLoaded;
    emit(OnboardingDetailLoading());
    final result =
        await _getDetailEmotionsUsecase(OnboardingEmotionDetailParams(id: id));
    if (result.isSuccess) {
      emit(previousState.copyWith(detailResponse: result.data));
    } else {
      emit(OnboardingError(result.error!));
    }
  }

  Future<void> getPrimaryEmotions() async {
    emit(OnboardingLoading());
    final result = await _onboardingUsecase.call(NoParams());
    if (result.isSuccess) {
      emit(OnboardingLoaded(onboardingResponse: result.data!));
    } else {
      emit(OnboardingError(result.error!));
    }
  }

  Future<void> postOnboarding(OnboardingPostParams params) async {
    emit(OnboardingLoading());
    final result = await _postonboardingUsecase.call(params);
    if (result.isSuccess) {
      emit(OnboardingSuccess(onboardingResponse: result.data));
    } else {
      emit(OnboardingError(result.error!));
    }
  }
}
