// import 'package:flutter/material.dart';
// import 'package:flutter_bloc/flutter_bloc.dart';
// import 'package:gotcha_mfg_app/config/theme/app_colors.dart';
// import 'package:gotcha_mfg_app/core/extensions/app_text_theme_extension.dart';
// import 'package:gotcha_mfg_app/features/home/<USER>/models/check_in_request.dart';
// import 'package:gotcha_mfg_app/features/home/<USER>/models/emotions_detail_response.dart';
// import 'package:gotcha_mfg_app/features/home/<USER>/widgets/check_in_title.dart';
// import 'package:gotcha_mfg_app/features/onboarding/presentation/blocs/onboarding_data/onboarding_data_cubit.dart';

// class OnboardingSubEmotionSection extends StatefulWidget {
//   const OnboardingSubEmotionSection({
//     required this.title,
//     required this.items,
//     this.checkInTypeId,
//     this.questionId,
//     super.key,
//     required this.optional,
//   });

//   final String title;
//   final bool optional;
//   final List<Answer> items;
//   final String? checkInTypeId;
//   final String? questionId;

//   @override
//   State<OnboardingSubEmotionSection> createState() =>
//       _OnboardingSubEmotionSectionState();
// }

// class _OnboardingSubEmotionSectionState
//     extends State<OnboardingSubEmotionSection> {
//   final TextEditingController _textController = TextEditingController();
//   final ValueNotifier<bool> _isOtherVisible = ValueNotifier<bool>(false);
//   Answer _downloadingAppAnswer = Answer(); // Initialize to a default Answer
//   String removeEmoji(String text) {
//     if (text.isEmpty) {
//       return text; // Return empty string if input is empty
//     }

//     // Regular expression to match most common emojis.
//     // This is a broad range and might need adjustments based on your specific needs.
//     final emojiRegex = RegExp(
//         r'(\u00a9|\u00ae|[\u2000-\u3300]|\ud83c[\ud000-\udfff]|\ud83d[\ud000-\udfff]|\ud83e[\ud000-\udfff])');

//     return text.replaceAll(emojiRegex, '');
//   }

//   @override
//   void initState() {
//     super.initState();
//     _downloadingAppAnswer = widget.items.firstWhere(
//       (item) {
//         var extractedString = removeEmoji(item.answer.toString());
//         print('eeee-------$extractedString--------- Downloading the MFG App');
//         return extractedString == 'Downloading the Mental Fitness Gym';
//       },
//       orElse: () => Answer(), // Return default if not found
//     );

//     // Pre-select "Downloading the MFG App" if found
//     if (_downloadingAppAnswer.answerId != null) {
//       WidgetsBinding.instance.addPostFrameCallback((_) {
//         final cubit = context.read<OnboardingDataCubit>();
//         cubit.add(
//           Question(
//             questionId: widget.questionId,
//             answerId: _downloadingAppAnswer.answerId,
//             checkInTypesId: widget.checkInTypeId,
//             answer: _downloadingAppAnswer.answer,
//           ),
//         );
//       });
//     }
//   }

//   @override
//   void dispose() {
//     _textController.dispose();
//     _isOtherVisible.dispose();
//     super.dispose();
//   }

//   @override
//   Widget build(BuildContext context) {
//     final cubit = context.read<OnboardingDataCubit>();
//     var textTheme = Theme.of(context).textTheme;
//     return BlocBuilder<OnboardingDataCubit, OnboardingDataState>(
//       builder: (context, state) {
//         final cubit = context.read<OnboardingDataCubit>();
//         if (state is! OnboardingDataLoaded) {
//           return const Center(child: CircularProgressIndicator());
//         }

//         final selectedQuestions = state.questions;

//         bool isSelected(Question question) {
//           return selectedQuestions.any((q) =>
//               q.answerId == question.answerId &&
//               q.questionId ==
//                   widget.questionId); // Ensure questionId is also checked
//         }

//         bool hasOther() {
//           return selectedQuestions.any(
//               (q) => (q.other != null && (q.questionId == widget.questionId)));
//         }

//         Question? getOther() {
//           return selectedQuestions.firstWhere(
//               (q) => q.other != null && (q.questionId == widget.questionId),
//               orElse: () => Question());
//         }

//         void toggleQuestionSelection(Question question) {
//           if (isSelected(question)) {
//             cubit.remove(question);
//           } else {
//             cubit.add(question);
//           }
//         }

//         void addOtherQuestion(String input) {
//           if (input.isEmpty) {
//             if (hasOther()) {
//               cubit.remove(getOther()!);
//             }
//             return;
//           }
//           if (hasOther()) {
//             cubit.remove(getOther()!);
//           }

//           cubit.add(
//             Question(
//               questionId: widget.questionId,
//               checkInTypesId: widget.checkInTypeId,
//               other: input,
//             ),
//           );
//         }

//         Widget buildEmotionChip(Answer item) {
//           final question = Question(
//             questionId: widget.questionId,
//             answerId: item.answerId,
//             checkInTypesId: widget.checkInTypeId,
//             answer: item.answer,
//           );

//           final selected = isSelected(question);

//           return Padding(
//             padding: const EdgeInsets.symmetric(vertical: 4),
//             child: ActionChip(
//               shape: StadiumBorder(
//                 side: BorderSide(
//                   color: selected ? AppColors.coral : Colors.white,
//                 ),
//               ),
//               onPressed: () => toggleQuestionSelection(question),
//               label: Text(
//                 item.answer ?? 'N/A',
//                 style: Theme.of(context).textTheme.ralewaySemiBold.copyWith(
//                       fontSize: 14,
//                       color: AppColors.navy,
//                     ),
//               ),
//               backgroundColor: selected ? AppColors.lightRed : Colors.white,
//             ),
//           );
//         }

//         Widget buildOtherChip() {
//           final otherText =
//               hasOther() ? getOther()?.other ?? '+ Other' : '+ Other';
//           return Padding(
//             padding: const EdgeInsets.only(top: 4.0),
//             child: ActionChip(
//               label: Text(
//                 otherText,
//                 style: otherText != '+ Other'
//                     ? Theme.of(context).textTheme.ralewaySemiBold.copyWith(
//                           fontSize: 14,
//                           color: AppColors.navy,
//                         )
//                     : Theme.of(context)
//                         .textTheme
//                         .ralewayLight
//                         .copyWith(fontSize: 14, color: Colors.grey),
//               ),
//               onPressed: () => _isOtherVisible.value = true,
//               shape: StadiumBorder(
//                 side: BorderSide(
//                   color: hasOther() ? AppColors.coral : AppColors.midBlue,
//                 ),
//               ),
//               backgroundColor: hasOther() ? AppColors.lightRed : Colors.white,
//             ),
//           );
//         }

//         Widget buildOtherTextField() {
//           return ValueListenableBuilder<bool>(
//             valueListenable: _isOtherVisible,
//             builder: (context, isVisible, _) {
//               if (!isVisible) return buildOtherChip();

//               var border = OutlineInputBorder(
//                 borderRadius: BorderRadius.circular(30),
//                 borderSide: const BorderSide(
//                   color: AppColors.midBlue,
//                   width: 0.5,
//                 ),
//               );

//               return Container(
//                 margin: const EdgeInsets.only(top: 4),
//                 height: 42,
//                 decoration: BoxDecoration(
//                   borderRadius: BorderRadius.circular(30),
//                   color: Colors.white,
//                 ),
//                 child: TextField(
//                   controller: _textController,
//                   style: Theme.of(context).textTheme.ralewayBold.copyWith(
//                         fontSize: 14,
//                         color: AppColors.navy,
//                       ),
//                   decoration: InputDecoration(
//                     hintText: 'Type here...',
//                     isDense: true,
//                     contentPadding: const EdgeInsets.symmetric(horizontal: 16),
//                     enabledBorder: border,
//                     focusedBorder: border,
//                     suffixIcon: IconButton(
//                       icon: const Icon(Icons.check, color: AppColors.coral),
//                       onPressed: () {
//                         final input = _textController.text.trim();
//                         addOtherQuestion(input);
//                         _isOtherVisible.value = false;
//                         _textController.clear();
//                       },
//                     ),
//                   ),
//                 ),
//               );
//             },
//           );
//         }

//         return Column(
//           crossAxisAlignment: CrossAxisAlignment.start,
//           children: [
//             Padding(
//               padding: widget.optional == true
//                   ? const EdgeInsets.only(bottom: 8.0)
//                   : const EdgeInsets.symmetric(vertical: 0),
//               child: CheckInTitle(
//                 onboarding: true,
//                 optionaltext: widget.optional,
//                 text: widget.title,
//                 mainTextStyle: textTheme.ralewayMedium.copyWith(
//                   fontSize: 14,
//                   color: AppColors.navy,
//                 ),
//                 optionalTextStyle: textTheme.ralewayRegular.copyWith(
//                   fontSize: 14,
//                   color: AppColors.navy,
//                 ),
//               ),
//             ),
//             // const SizedBox(height: 8),
//             Wrap(
//               spacing: 15,
//               children: [
//                 for (final item in widget.items) buildEmotionChip(item),
//                 buildOtherTextField(),
//               ],
//             ),
//           ],
//         );
//       },
//     );
//   }
// }
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:gotcha_mfg_app/config/theme/app_colors.dart';
import 'package:gotcha_mfg_app/core/extensions/app_text_theme_extension.dart';
import 'package:gotcha_mfg_app/core/extensions/url_extensions.dart';
import 'package:gotcha_mfg_app/core/utils/app_print.dart';
import 'package:gotcha_mfg_app/features/home/<USER>/models/check_in_request.dart';
import 'package:gotcha_mfg_app/features/home/<USER>/models/emotions_detail_response.dart';
import 'package:gotcha_mfg_app/features/home/<USER>/widgets/check_in_title.dart';
import 'package:gotcha_mfg_app/features/onboarding/presentation/blocs/onboarding_data/onboarding_data_cubit.dart';
import 'package:gotcha_mfg_app/shared/widgets/loading_widget.dart';
import 'package:gotcha_mfg_app/shared/widgets/network_image_indicator.dart';

class OnboardingSubEmotionSection extends StatefulWidget {
  const OnboardingSubEmotionSection({
    required this.title,
    required this.items,
    this.checkInTypeId,
    this.questionId,
    super.key,
    required this.optional,
  });

  final String title;
  final bool optional;
  final List<Answer> items;
  final String? checkInTypeId;
  final String? questionId;

  @override
  State<OnboardingSubEmotionSection> createState() =>
      _OnboardingSubEmotionSectionState();
}

class _OnboardingSubEmotionSectionState
    extends State<OnboardingSubEmotionSection> {
  final TextEditingController _textController = TextEditingController();
  final ValueNotifier<bool> _isOtherVisible = ValueNotifier<bool>(false);
  Answer _downloadingAppAnswer = Answer(); // Initialize to a default Answer
  late List<Answer> _sortedItems; // Add this variable to store sorted items

  String removeEmoji(String text) {
    if (text.isEmpty) {
      return text; // Return empty string if input is empty
    }

    // Regular expression to match most common emojis.
    // This is a broad range and might need adjustments based on your specific needs.
    final emojiRegex = RegExp(
        r'(\u00a9|\u00ae|[\u2000-\u3300]|\ud83c[\ud000-\udfff]|\ud83d[\ud000-\udfff]|\ud83e[\ud000-\udfff])');

    return text.replaceAll(emojiRegex, '');
  }

  @override
  void initState() {
    super.initState();

    // Find the downloading app answer
    _downloadingAppAnswer = widget.items.firstWhere(
      (item) {
        var extractedString = removeEmoji(item.answer.toString());
        info('eeee-------$extractedString--------- Downloading the MFG App');
        return extractedString == 'Downloading the Mental Fitness Gym';
      },
      orElse: () => Answer(), // Return default if not found
    );

    // Sort the items list to put the downloading app answer first
    _sortedItems = List.from(widget.items);
    if (_downloadingAppAnswer.answerId != null) {
      // Remove the downloading app answer from its current position
      _sortedItems.removeWhere(
          (item) => item.answerId == _downloadingAppAnswer.answerId);
      // Add it to the beginning of the list
      _sortedItems.insert(0, _downloadingAppAnswer);

      // Auto-select the downloading app answer
      WidgetsBinding.instance.addPostFrameCallback((_) {
        final cubit = context.read<OnboardingDataCubit>();
        cubit.add(
          Question(
            questionId: widget.questionId,
            answerId: _downloadingAppAnswer.answerId,
            checkInTypesId: widget.checkInTypeId,
            answer: _downloadingAppAnswer.answer,
          ),
        );
      });
    }
  }

  @override
  void dispose() {
    _textController.dispose();
    _isOtherVisible.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final cubit = context.read<OnboardingDataCubit>();
    var textTheme = Theme.of(context).textTheme;
    return BlocBuilder<OnboardingDataCubit, OnboardingDataState>(
      builder: (context, state) {
        final cubit = context.read<OnboardingDataCubit>();
        if (state is! OnboardingDataLoaded) {
          return const Center(child: Loader());
        }

        final selectedQuestions = state.questions;

        bool isSelected(Question question) {
          return selectedQuestions.any((q) =>
              q.answerId == question.answerId &&
              q.questionId ==
                  widget.questionId); // Ensure questionId is also checked
        }

        bool hasOther() {
          return selectedQuestions.any(
              (q) => (q.other != null && (q.questionId == widget.questionId)));
        }

        Question? getOther() {
          return selectedQuestions.firstWhere(
              (q) => q.other != null && (q.questionId == widget.questionId),
              orElse: () => Question());
        }

        void toggleQuestionSelection(Question question) {
          if (isSelected(question)) {
            cubit.remove(question);
          } else {
            cubit.add(question);
          }
        }

        void addOtherQuestion(String input) {
          if (input.isEmpty) {
            if (hasOther()) {
              cubit.remove(getOther()!);
            }
            return;
          }
          if (hasOther()) {
            cubit.remove(getOther()!);
          }

          cubit.add(
            Question(
              questionId: widget.questionId,
              checkInTypesId: widget.checkInTypeId,
              other: input,
            ),
          );
        }

        Widget buildEmotionChip(Answer item) {
          final question = Question(
            questionId: widget.questionId,
            answerId: item.answerId,
            checkInTypesId: widget.checkInTypeId,
            answer: item.answer,
          );

          final selected = isSelected(question);

          return Padding(
            padding: const EdgeInsets.symmetric(vertical: 4),
            child: ActionChip(
              avatar: item.iconUrl == null
                  ? null
                  : ClipRRect(
                      borderRadius: BorderRadius.circular(100),
                      child: NetworkImageWithIndicatorSmall(
                        imageUrl: item.iconUrl?.iconUrl ?? '',
                      ),
                    ),
              shape: StadiumBorder(
                side: BorderSide(
                  width: 1.5,
                  color: selected ? AppColors.coral : Colors.white,
                ),
              ),
              onPressed: () => toggleQuestionSelection(question),
              label: Text(
                item.answer ?? 'N/A',
                style: Theme.of(context).textTheme.ralewaySemiBold.copyWith(
                      fontSize: 14,
                      color: AppColors.navy,
                    ),
              ),
              backgroundColor: selected ? AppColors.lightRed : Colors.white,
            ),
          );
        }

        Widget buildOtherChip() {
          final otherText =
              hasOther() ? getOther()?.other ?? '+ Other' : '+ Other';
          return Padding(
            padding: const EdgeInsets.only(top: 4.0),
            child: ActionChip(
              label: Text(
                otherText,
                style: otherText != '+ Other'
                    ? Theme.of(context).textTheme.ralewaySemiBold.copyWith(
                          fontSize: 14,
                          color: AppColors.navy,
                        )
                    : Theme.of(context)
                        .textTheme
                        .ralewayLight
                        .copyWith(fontSize: 14, color: Colors.grey),
              ),
              onPressed: () => _isOtherVisible.value = true,
              shape: StadiumBorder(
                side: BorderSide(
                  width: 1.5,
                  color: hasOther() ? AppColors.coral : AppColors.midBlue,
                ),
              ),
              backgroundColor: hasOther() ? AppColors.lightRed : Colors.white,
            ),
          );
        }

        Widget buildOtherTextField() {
          return ValueListenableBuilder<bool>(
            valueListenable: _isOtherVisible,
            builder: (context, isVisible, _) {
              if (!isVisible) return buildOtherChip();

              var border = OutlineInputBorder(
                borderRadius: BorderRadius.circular(30),
                borderSide: const BorderSide(
                  color: AppColors.midBlue,
                  width: 1.5,
                ),
              );

              return Container(
                margin: const EdgeInsets.only(top: 4),
                height: 44,
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(30),
                  color: Colors.white,
                ),
                child: TextField(
                  controller: _textController,
                  style: Theme.of(context).textTheme.ralewayBold.copyWith(
                        fontSize: 14,
                        color: AppColors.navy,
                      ),
                  decoration: InputDecoration(
                    hintText: 'Type here...',
                    isDense: true,
                    contentPadding: const EdgeInsets.symmetric(horizontal: 16),
                    enabledBorder: border,
                    focusedBorder: border,
                    suffixIcon: IconButton(
                      icon: const Icon(Icons.check, color: AppColors.coral),
                      onPressed: () {
                        final input = _textController.text.trim();
                        addOtherQuestion(input);
                        _isOtherVisible.value = false;
                        _textController.clear();
                      },
                    ),
                  ),
                ),
              );
            },
          );
        }

        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Padding(
              padding: widget.optional == true
                  ? const EdgeInsets.only(bottom: 8.0)
                  : const EdgeInsets.symmetric(vertical: 0),
              child: CheckInTitle(
                onboarding: true,
                optionaltext: widget.optional,
                text: widget.title,
                mainTextStyle: textTheme.ralewayMedium.copyWith(
                  fontSize: 14,
                  color: AppColors.navy,
                ),
                optionalTextStyle: textTheme.ralewayRegular.copyWith(
                  fontSize: 14,
                  color: AppColors.navy,
                ),
              ),
            ),
            // const SizedBox(height: 8),
            Wrap(
              spacing: 15,
              children: [
                // Use the sorted items list instead of widget.items
                for (final item in _sortedItems) buildEmotionChip(item),
                buildOtherTextField(),
              ],
            ),
          ],
        );
      },
    );
  }
}
