import 'package:auto_route/auto_route.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_timezone/flutter_timezone.dart';
import 'package:gap/gap.dart';
import 'package:gotcha_mfg_app/config/router/app_router.gr.dart';
import 'package:gotcha_mfg_app/config/theme/app_colors.dart';
import 'package:gotcha_mfg_app/core/extensions/app_text_theme_extension.dart';
import 'package:gotcha_mfg_app/core/firebase_push.dart';
import 'package:gotcha_mfg_app/core/storage/token_manager.dart';
import 'package:gotcha_mfg_app/core/utils/app_print.dart';
import 'package:gotcha_mfg_app/core/utils/device_info.dart';
import 'package:gotcha_mfg_app/core/utils/snackbar_service.dart';
import 'package:gotcha_mfg_app/features/home/<USER>/models/check_in_request.dart';
import 'package:gotcha_mfg_app/features/home/<USER>/models/emotions_detail_response.dart';
import 'package:gotcha_mfg_app/features/onboarding/domain/usecases/post_onboarding.dart';
import 'package:gotcha_mfg_app/features/onboarding/presentation/blocs/onboard/onboarding_cubit.dart';
import 'package:gotcha_mfg_app/features/onboarding/presentation/blocs/onboarding_data/onboarding_data_cubit.dart';
import 'package:gotcha_mfg_app/features/onboarding/presentation/widgets/onboarding_sub_emotion_section.dart';
import 'package:gotcha_mfg_app/locator.dart';
import 'package:gotcha_mfg_app/shared/widgets/app_header.dart';
import 'package:gotcha_mfg_app/shared/widgets/loading_widget.dart';
import 'package:gotcha_mfg_app/shared/widgets/primary_button.dart';

import '../../../../core/mixpanel_service.dart';
import '../../../../core/utils/platform_utils.dart';

@RoutePage()
class OnboardingMentalBoostsPage extends StatefulWidget {
  final EmotionsDetailResponse? data;
  final String checkInTypeId;

  const OnboardingMentalBoostsPage({
    super.key,
    this.data,
    required this.checkInTypeId,
  });

  @override
  State<OnboardingMentalBoostsPage> createState() =>
      _OnboardingMentalBoostsPageState();
}

class _OnboardingMentalBoostsPageState
    extends State<OnboardingMentalBoostsPage> {
  var uniqueId;
  _post() async {
    uniqueId = await sl<TokenManager>().generateUniqueId();
  }

  void _onSelectionChanged(List<Question> selectedQuestions) {
    // Print selected answers (for demo purposes)
    for (var question in selectedQuestions) {
      info('Selected Answer: ${question.answer}, Other: ${question.other}');
    }
  }

  @override
  void initState() {
    super.initState();
    sl<MixpanelService>().trackScreenView(
      'Onboarding Mental Boosts Page',
      properties: {'Code': 'screen_view.onboarding_mental_boost_page'},
    );
  }

  @override
  Widget build(BuildContext context) {
    var selectedItem =
        widget.data?.data?.firstWhere((item) => item.orderOfQuestion == 3);

    final textTheme = Theme.of(context).textTheme;
    final size = MediaQuery.sizeOf(context);
    final isKeyboardOpen = MediaQuery.of(context).viewInsets.bottom != 0;

    return BlocConsumer<OnboardingCubit, OnboardingState>(
        listener: (context, state) {
      if (state is OnboardingSuccess) {
        // SnackBarService.info(
        //   context: context,
        //   message: state.onboardingResponse?.message ?? "",
        // );
        context.router.replaceAll(
          [const WelcomeFeedRoute()],
          updateExistingRoutes: false,
        );
      }
      if (state is OnboardingError) {
        SnackBarService.error(
          context: context,
          message: state.error,
        );
      }
    }, builder: (context, state) {
      if (state is OnboardingLoading) {
        return const LoadingWidget(
          color: AppColors.grey,
        );
      }
      return Scaffold(
        appBar: AppBar(
          toolbarHeight: 0,
          elevation: 0,
          systemOverlayStyle: const SystemUiOverlayStyle(
            statusBarColor: Colors.white,
            systemNavigationBarIconBrightness: Brightness.dark,
            statusBarBrightness: Brightness.light,
            statusBarIconBrightness: Brightness.dark,
            systemNavigationBarColor: AppColors.grey,
          ),
        ),
        body: GestureDetector(
          onTap: () {
            FocusScope.of(context).unfocus();
          },
          behavior: HitTestBehavior.translucent,
          child: Padding(
            padding: EdgeInsets.only(
              top: isIos ? 4 : 8,
              left: 8,
              right: 8,
            ),
            child: Column(
              children: [
                const AppHeader(
                  title: 'Getting started',
                  currentStep: 3,
                  totalSteps: 3,
                ),
                Expanded(
                  child: Container(
                    color: AppColors.navy,
                    child: Container(
                      width: size.width,
                      padding: EdgeInsets.only(bottom: isIos ? 80 : 56),
                      decoration: const BoxDecoration(
                        borderRadius: BorderRadius.only(
                          topLeft: Radius.circular(30),
                          topRight: Radius.circular(30),
                        ),
                        color: AppColors.grey,
                      ),
                      child: SingleChildScrollView(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            const Gap(12),
                            Padding(
                              padding: const EdgeInsets.only(
                                left: 24,
                                right: 24,
                                top: 16,
                              ),
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Text(
                                    selectedItem?.description ?? 'Optional',
                                    style: textTheme.ralewayRegular
                                        .copyWith(fontSize: 14),
                                  ),
                                  const Gap(4),
                                  Text(
                                    selectedItem?.question ?? '',
                                    style: textTheme.ralewaySemiBold.copyWith(
                                      fontSize: 17,
                                    ),
                                  ),
                                ],
                              ),
                            ),
                            const Gap(4),
                            Padding(
                              padding:
                                  const EdgeInsets.symmetric(horizontal: 24),
                              child: OnboardingSubEmotionSection(
                                optional: false,
                                title: '',
                                items: selectedItem?.answers ?? [],
                                checkInTypeId: widget.checkInTypeId,
                                questionId: selectedItem?.questionId ?? '',
                              ),
                            ),
                            const Gap(120),
                          ],
                        ),
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
        floatingActionButtonLocation: FloatingActionButtonLocation.centerDocked,
        floatingActionButton: isKeyboardOpen
            ? const SizedBox()
            : Container(
                width: double.infinity,
                margin: const EdgeInsets.symmetric(
                  horizontal: 32,
                  vertical: 24,
                ),
                child: PrimaryButton(
                  text: 'Next',
                  isEnabled: true,
                  onPressed: () async {
                    String? token;

                    token = await MFGPushNotification.messaging.getToken();

                    String? deviceId = await getDeviceId();
                    sl<MixpanelService>().trackButtonClick('Next', properties: {
                      'Page': 'Onboarding Mental Boosts Page',
                      'Code': 'click.onboarding_mental_boosts_page.next'
                    });

                    final onboardingDataCubit =
                        context.read<OnboardingDataCubit>();
                    final onboardingCubit = context.read<OnboardingCubit>();
                    var onboardingData =
                        onboardingDataCubit.state as OnboardingDataLoaded;
                    var questions = onboardingData.questions;
                    var uniqueId = await sl<TokenManager>().getUniqueId() ?? "";
                    sl<MixpanelService>().trackCheckInWithOptions(
                      'Onboarding Mental Boosts Page',
                      questions: questions,
                    );
                    final String currentTimeZone =
                        await FlutterTimezone.getLocalTimezone();
                    if (mounted) {
                      onboardingCubit.postOnboarding(
                        OnboardingPostParams(
                          uniqueId,
                          widget.checkInTypeId,
                          '$deviceId',
                          currentTimeZone,
                          questions: questions,
                        ),
                      );
                    }
                  },
                ),
              ),
      );
    });
  }
}
