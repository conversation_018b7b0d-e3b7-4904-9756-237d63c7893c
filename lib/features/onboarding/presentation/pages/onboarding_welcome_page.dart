// import 'dart:math';
// import 'dart:async';

// import 'package:auto_route/auto_route.dart';
// import 'package:flutter/gestures.dart';
// import 'package:flutter/material.dart';
// import 'package:flutter/services.dart';
// import 'package:flutter_bloc/flutter_bloc.dart';
// import 'package:gap/gap.dart';
// import 'package:gotcha_mfg_app/config/router/app_router.gr.dart';
// import 'package:gotcha_mfg_app/config/theme/app_colors.dart';
// import 'package:gotcha_mfg_app/core/extensions/app_text_theme_extension.dart';
// import 'package:gotcha_mfg_app/core/utils/data_cache.dart';
// import 'package:gotcha_mfg_app/features/onboarding/presentation/blocs/onboard/onboarding_cubit.dart';
// import 'package:gotcha_mfg_app/shared/widgets/app_safe_area.dart';
// import 'package:gotcha_mfg_app/shared/widgets/loading_widget.dart';
// import 'package:gotcha_mfg_app/shared/widgets/primary_button.dart';
// import 'package:video_player/video_player.dart';
// import 'package:volume_controller/volume_controller.dart';

// import '../../../../core/mixpanel_service.dart';
// import '../../../../core/utils/platform_utils.dart';
// import '../../../../locator.dart';

// @RoutePage()
// class OnboardingWelcomePage extends StatefulWidget {
//   const OnboardingWelcomePage({super.key});

//   @override
//   State<OnboardingWelcomePage> createState() => _OnboardingWelcomePageState();
// }

// class _OnboardingWelcomePageState extends State<OnboardingWelcomePage> {
//   VideoPlayerController? _controller; // Nullable controller
//   bool _isInitialized = false; // Track initialization
//   bool _hasError = false; // Track errors
//   bool _isVideoComplete = false; // Track video completion

//   @override
//   void initState() {
//     super.initState();
//     sl<MixpanelService>().trackScreenView(
//       'Onboarding Welcome Page',
//       properties: {'Code': 'screen_view.onboarding_welcome_page'},
//     );
//     _initializeVideoController();
//     _initVolume();
//   }

//   void _initVolume() {
//     WidgetsBinding.instance.addPostFrameCallback((_) {
//       unawaited(_checkAndSetVolume());
//     });
//   }

//   String? getVideo() {
//     final urls = DataCache().splashData?.data?.miscellaneous?.onboardingUrls;
//     if (urls == null || urls.isEmpty) return null;
//     final random = Random();
//     return urls[random.nextInt(urls.length)];
//     //return 'https://cdn.staging.app.gotcha4life.org/videos/sample_video.mp4';
//   }

//   Future<void> _initializeVideoController() async {
//     String? videoUrl = getVideo();
//     if (videoUrl == null) {
//       setState(() {
//         _hasError = true;
//       });
//       return;
//     }
//     _controller = VideoPlayerController.networkUrl(
//         Uri.parse(videoUrl)); // Use .networkUrl

//     try {
//       await _controller!.initialize();
//       _controller!.setLooping(false); // Loop the video
//       _controller!.addListener(_videoPositionListener);
//       await _controller!.play(); // Start playing after initialization
//       // await _controller!.setVolume(0.5); //initial volume

//       setState(() {
//         _isInitialized = true; // Set flag to true when initialized
//       });
//     } catch (e) {
//       print("Error initializing video: $e");
//       setState(() {
//         _hasError = true; // Set error flag
//       });
//     }
//   }

//   void _videoPositionListener() {
//     if (_controller != null &&
//         _controller!.value.position >= _controller!.value.duration) {
//       setState(() {
//         _isVideoComplete = true;
//         _controller?.seekTo(Duration.zero);
//       });
//     }
//   }

//   Future<void> _checkAndSetVolume() async {
//     try {
//       final volume = await VolumeController().getVolume();
//       if (volume < 0.5) {
//         VolumeController().setVolume(0.5);
//       }
//     } catch (e) {
//       debugPrint('Error setting volume: $e');
//     }
//   }

//   @override
//   void dispose() {
//     _controller?.removeListener(_videoPositionListener);
//     _controller?.dispose(); // Dispose if not null
//     super.dispose();
//   }

//   // Pause the video when the page is not visible
//   @override
//   void deactivate() {
//     _controller?.pause();
//     super.deactivate();
//   }

//   @override
//   Widget build(BuildContext context) {
//     final textTheme = Theme.of(context).textTheme;
//     final size = MediaQuery.of(context).size;
//     return BlocBuilder<OnboardingCubit, OnboardingState>(
//       builder: (context, state) {
//         return Scaffold(
//           backgroundColor: Colors.white,
//           appBar: AppBar(
//             toolbarHeight: 0,
//             elevation: 0,
//             systemOverlayStyle: const SystemUiOverlayStyle(
//               statusBarColor: Colors.white,
//               systemNavigationBarIconBrightness: Brightness.dark,
//               statusBarBrightness: Brightness.light,
//               statusBarIconBrightness: Brightness.dark,
//               systemNavigationBarColor: AppColors.lightBlue,
//             ),
//           ),
//           body: AppSafeArea(
//             child: Padding(
//               padding: EdgeInsets.only(
//                 top: isIos ? 4 : 8,
//                 left: 8,
//                 right: 8,
//               ),
//               child: Container(
//                 width: size.width,
//                 height: size.height,
//                 decoration: const BoxDecoration(
//                   color: AppColors.lightBlue,
//                   borderRadius: BorderRadius.only(
//                     topLeft: Radius.circular(30),
//                     topRight: Radius.circular(30),
//                   ),
//                 ),
//                 child: Padding(
//                   padding: const EdgeInsets.symmetric(horizontal: 32),
//                   child: Column(
//                     crossAxisAlignment: CrossAxisAlignment.center,
//                     mainAxisSize: MainAxisSize.max,
//                     children: [
//                       const Gap(40),
//                       Text(
//                         'Welcome to the\nMental Fitness Gym!',
//                         style: textTheme.primaryHeading,
//                         textAlign: TextAlign.center,
//                       ),
//                       const Gap(20),
//                       Expanded(
//                         child: Center(
//                           child: ClipRRect(
//                             borderRadius: BorderRadius.circular(30),
//                             child: Container(
//                               width: size.width * 0.8,
//                               decoration: BoxDecoration(
//                                 borderRadius: BorderRadius.circular(30),
//                               ),
//                               child: Stack(
//                                 fit: StackFit.expand,
//                                 children: [
//                                   // Background image to show after video ends
//                                   // AnimatedOpacity(
//                                   //   opacity: _isVideoComplete ? 1.0 : 0.0,
//                                   //   duration: const Duration(milliseconds: 800),
//                                   //   child: Container(
//                                   //     color: AppColors.navy,
//                                   //     width: size.width,
//                                   //     child: Column(
//                                   //       mainAxisAlignment:
//                                   //           MainAxisAlignment.center,
//                                   //       children: [
//                                   //         Image.asset(
//                                   //           AppAssets.g4lMfg,
//                                   //           fit: BoxFit.cover,
//                                   //           width: size.width / 2,
//                                   //         ),
//                                   //       ],
//                                   //     ),
//                                   //   ),
//                                   // ),
//                                   // // Video player with fade out animation
//                                   // AnimatedOpacity(
//                                   //   opacity: _isVideoComplete ? 1.0 : 1.0,
//                                   //   duration: const Duration(milliseconds: 800),
//                                   //   child:
//                                   _buildVideoPlayer(),
//                                   // ),
//                                 ],
//                               ),
//                             ),
//                           ),
//                         ),
//                       ),
//                       const Gap(32),
//                       SizedBox(
//                         width: double.infinity,
//                         child: PrimaryButton(
//                           text: 'Start exercising',
//                           onPressed: () {
//                             _controller?.pause();
//                             sl<MixpanelService>()
//                                 .trackButtonClick('Get Started', properties: {
//                               'Page': 'Onboarding Welcome Page',
//                               'Code':
//                                   'click.onboarding_welcome_page.get_started'
//                             });
//                             context
//                                 .replaceRoute(const OnboardingFeelingRoute())
//                                 .then((_) {
//                               //re-initialize after returning to this page.
//                               if (_controller != null && mounted) {
//                                 _controller!.play();
//                               }
//                             });
//                           },
//                         ),
//                       ),
//                       const Gap(16),
//                       Text(
//                         'Completely free. No signup required.',
//                         style: textTheme.labels.copyWith(),
//                       ),
//                       const Gap(16),
//                       RichText(
//                         textAlign: TextAlign.center,
//                         text: TextSpan(
//                           style: textTheme.labels.copyWith(),
//                           children: [
//                             const TextSpan(
//                               text:
//                                   'Your gym activity will be stored anonymously and cannot be linked to you unless you create an account. By continuing you agree to our ',
//                             ),
//                             TextSpan(
//                                 text: 'PRIVACY POLICY',
//                                 style: textTheme.labelsBold.copyWith(
//                                   color: AppColors.coral,
//                                 ),
//                                 recognizer: TapGestureRecognizer()
//                                   ..onTap = () {
//                                     _controller
//                                         ?.pause(); //pause video before navigating
//                                     context
//                                         .pushRoute(
//                                           GotchaWebViewRoute(
//                                             url:
//                                                 'https://www.gym.gotcha4life.org/privacy-policy-mfg-app/',
//                                             title: 'Privacy policy',
//                                           ),
//                                         )
//                                         .then((value) => _controller?.play());
//                                   }),
//                             const TextSpan(
//                               text: ' and ',
//                             ),
//                             TextSpan(
//                               text: 'TERMS OF USE.',
//                               style: textTheme.labelsBold.copyWith(
//                                 color: AppColors.coral,
//                               ),
//                               recognizer: TapGestureRecognizer()
//                                 ..onTap = () {
//                                   _controller
//                                       ?.pause(); //pause video before navigating
//                                   context
//                                       .pushRoute(
//                                         GotchaWebViewRoute(
//                                           url:
//                                               'https://www.gym.gotcha4life.org/terms-conditions-mfg-app/',
//                                           title: 'Terms of use',
//                                         ),
//                                       )
//                                       .then((value) => _controller
//                                           ?.play()); //play after navigating back.
//                                 },
//                             ),
//                           ],
//                         ),
//                       ),
//                       isIos ? const Gap(36) : const Gap(24),
//                     ],
//                   ),
//                 ),
//               ),
//             ),
//           ),
//         );
//       },
//     );
//   }

//   Widget _buildVideoPlayer() {
//     if (_hasError) {
//       return Container(
//         color: AppColors.grey,
//         child: Center(
//           child: Text(
//             "Error loading video.",
//             style: Theme.of(context).textTheme.bodyEmphasis,
//           ),
//         ),
//       );
//     } else if (_isInitialized && _controller != null) {
//       return FittedBox(
//         fit: BoxFit.cover,
//         clipBehavior: Clip.hardEdge,
//         child: SizedBox(
//           width: _controller!.value.size.width,
//           height: _controller!.value.size.height,
//           child: VideoPlayer(_controller!),
//         ),
//       );
//     } else {
//       return Container(
//         color: AppColors.grey,
//         child: const Center(
//           child: Loader(),
//         ),
//       ); //show loading indicator
//     }
//   }
// }

import 'dart:math';
import 'dart:async';
import 'package:auto_route/auto_route.dart';
import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:gap/gap.dart';
import 'package:gotcha_mfg_app/config/router/app_router.gr.dart';
import 'package:gotcha_mfg_app/config/theme/app_colors.dart';
import 'package:gotcha_mfg_app/core/extensions/app_text_theme_extension.dart';
import 'package:gotcha_mfg_app/core/utils/app_print.dart';
import 'package:gotcha_mfg_app/core/utils/data_cache.dart';
import 'package:gotcha_mfg_app/features/onboarding/presentation/blocs/onboard/onboarding_cubit.dart';
import 'package:gotcha_mfg_app/shared/widgets/app_safe_area.dart';
import 'package:gotcha_mfg_app/shared/widgets/loading_widget.dart';
import 'package:gotcha_mfg_app/shared/widgets/primary_button.dart';
import 'package:video_player/video_player.dart';
import 'package:volume_controller/volume_controller.dart';
import '../../../../core/mixpanel_service.dart';
import '../../../../core/utils/platform_utils.dart';
import '../../../../locator.dart';

@RoutePage()
class OnboardingWelcomePage extends StatefulWidget {
  const OnboardingWelcomePage({super.key});

  @override
  State<OnboardingWelcomePage> createState() => _OnboardingWelcomePageState();
}

class _OnboardingWelcomePageState extends State<OnboardingWelcomePage> {
  VideoPlayerController? _controller; // Nullable controller
  bool _isInitialized = false; // Track initialization
  bool _hasError = false; // Track errors
  bool _isVideoComplete = false; // Track video completion

  @override
  void initState() {
    super.initState();
    sl<MixpanelService>().trackScreenView(
      'Onboarding Welcome Page',
      properties: {'Code': 'screen_view.onboarding_welcome_page'},
    );
    _initializeVideoController();
    _initVolume();
  }

  void _initVolume() {
    WidgetsBinding.instance.addPostFrameCallback((_) {
      unawaited(_checkAndSetVolume());
    });
  }

  String? getVideo() {
    final urls = DataCache().splashData?.data?.miscellaneous?.onboardingUrls;
    if (urls == null || urls.isEmpty) return null;
    final random = Random();
    return urls[random.nextInt(urls.length)];
    //return 'https://cdn.staging.app.gotcha4life.org/videos/sample_video.mp4';
  }

  Future<void> _initializeVideoController() async {
    String? videoUrl = getVideo();
    if (videoUrl == null) {
      setState(() {
        _hasError = true;
      });
      return;
    }
    _controller = VideoPlayerController.networkUrl(
        Uri.parse(videoUrl)); // Use .networkUrl

    try {
      await _controller!.initialize();
      _controller!.setLooping(false); // Loop the video
      _controller!.addListener(_videoPositionListener);
      await _controller!.play(); // Start playing after initialization
      // await _controller!.setVolume(0.5); //initial volume

      setState(() {
        _isInitialized = true; // Set flag to true when initialized
      });
    } catch (e) {
      info("Error initializing video: $e");
      setState(() {
        _hasError = true; // Set error flag
      });
    }
  }

  void _videoPositionListener() {
    if (_controller != null &&
        _controller!.value.position >= _controller!.value.duration) {
      setState(() {
        _isVideoComplete = true;
        _controller?.seekTo(const Duration(seconds: 3));
      });
    }
  }

  Future<void> _checkAndSetVolume() async {
    try {
      final volume = await VolumeController().getVolume();
      if (volume < 0.5) {
        VolumeController().setVolume(0.5);
      }
    } catch (e) {
      info('Error setting volume: $e');
    }
  }

  @override
  void dispose() {
    _controller?.removeListener(_videoPositionListener);
    _controller?.dispose(); // Dispose if not null
    super.dispose();
  }

  // Pause the video when the page is not visible
  @override
  void deactivate() {
    _controller?.pause();
    super.deactivate();
  }

  @override
  Widget build(BuildContext context) {
    final textTheme = Theme.of(context).textTheme;
    final size = MediaQuery.of(context).size;
    return BlocBuilder<OnboardingCubit, OnboardingState>(
      builder: (context, state) {
        return Scaffold(
          backgroundColor: Colors.white,
          appBar: AppBar(
            toolbarHeight: 0,
            elevation: 0,
            systemOverlayStyle: const SystemUiOverlayStyle(
              statusBarColor: Colors.white,
              systemNavigationBarIconBrightness: Brightness.dark,
              statusBarBrightness: Brightness.light,
              statusBarIconBrightness: Brightness.dark,
              systemNavigationBarColor: AppColors.lightBlue,
            ),
          ),
          body: AppSafeArea(
            child: Padding(
              padding: EdgeInsets.only(
                top: isIos ? 4 : 8,
                left: 8,
                right: 8,
              ),
              child: Container(
                width: size.width,
                height: size.height,
                decoration: const BoxDecoration(
                  color: AppColors.lightBlue,
                  borderRadius: BorderRadius.only(
                    topLeft: Radius.circular(30),
                    topRight: Radius.circular(30),
                  ),
                ),
                child: Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 32),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.center,
                    mainAxisSize: MainAxisSize.max,
                    children: [
                      const Gap(32),
                      // Text(
                      //   'Welcome to the\nMental Fitness Gym!',
                      //   style: textTheme.primaryHeading,
                      //   textAlign: TextAlign.center,
                      // ),
                      // const Gap(12),
                      Expanded(
                        child: Center(
                          child: ClipRRect(
                            borderRadius: BorderRadius.circular(30),
                            child: Container(
                              width: size.width * 0.8,
                              decoration: BoxDecoration(
                                borderRadius: BorderRadius.circular(30),
                              ),
                              child: Stack(
                                fit: StackFit.expand,
                                children: [
                                  // Background image to show after video ends
                                  // AnimatedOpacity(
                                  //   opacity: _isVideoComplete ? 1.0 : 0.0,
                                  //   duration: const Duration(milliseconds: 800),
                                  //   child: Container(
                                  //     color: AppColors.navy,
                                  //     width: size.width,
                                  //     child: Column(
                                  //       mainAxisAlignment:
                                  //           MainAxisAlignment.center,
                                  //       children: [
                                  //         Image.asset(
                                  //           AppAssets.g4lMfg,
                                  //           fit: BoxFit.cover,
                                  //           width: size.width / 2,
                                  //         ),
                                  //       ],
                                  //     ),
                                  //   ),
                                  // ),
                                  // // Video player with fade out animation
                                  // AnimatedOpacity(
                                  //   opacity: _isVideoComplete ? 1.0 : 1.0,
                                  //   duration: const Duration(milliseconds: 800),
                                  //   child:
                                  _buildVideoPlayer(),
                                  // ),
                                ],
                              ),
                            ),
                          ),
                        ),
                      ),
                      const Gap(24),
                      SizedBox(
                        width: double.infinity,
                        child: PrimaryButton(
                          text: 'Start exercising',
                          onPressed: () {
                            _controller?.pause();
                            sl<MixpanelService>()
                                .trackButtonClick('Get Started', properties: {
                              'Page': 'Onboarding Welcome Page',
                              'Code':
                                  'click.onboarding_welcome_page.get_started'
                            });
                            context
                                .replaceRoute(const OnboardingFeelingRoute())
                                .then((_) {
                              //re-initialize after returning to this page.
                              if (_controller != null && mounted) {
                                _controller!.play();
                              }
                            });
                          },
                        ),
                      ),
                      const Gap(12),
                      Text(
                        'Completely free. No signup required.',
                        style: textTheme.labels.copyWith(),
                      ),
                      const Gap(8),
                      RichText(
                        textAlign: TextAlign.center,
                        text: TextSpan(
                          style: textTheme.labels.copyWith(),
                          children: [
                            const TextSpan(
                              text:
                                  'Your gym activity will be stored anonymously and cannot be linked to you unless you create an account. By continuing you agree to our ',
                            ),
                            TextSpan(
                              text: 'PRIVACY POLICY',
                              style: textTheme.labelsBold.copyWith(
                                color: AppColors.coral,
                              ),
                              recognizer: TapGestureRecognizer()
                                ..onTap = () {
                                  _controller
                                      ?.pause(); //pause video before navigating
                                  context
                                      .pushRoute(
                                        GotchaWebViewRoute(
                                          url:
                                              'https://www.gym.gotcha4life.org/privacy-policy-mfg-app/',
                                          title: 'Privacy policy',
                                        ),
                                      )
                                      .then((value) => _controller?.play());
                                },
                            ),
                            const TextSpan(
                              text: ' and ',
                            ),
                            TextSpan(
                              text: 'TERMS OF USE.',
                              style: textTheme.labelsBold.copyWith(
                                color: AppColors.coral,
                              ),
                              recognizer: TapGestureRecognizer()
                                ..onTap = () {
                                  _controller
                                      ?.pause(); //pause video before navigating
                                  context
                                      .pushRoute(
                                        GotchaWebViewRoute(
                                          url:
                                              'https://www.gym.gotcha4life.org/terms-conditions-mfg-app/',
                                          title: 'Terms of use',
                                        ),
                                      )
                                      .then((value) => _controller
                                          ?.play()); //play after navigating back.
                                },
                            ),
                          ],
                        ),
                      ),
                      isIos ? const Gap(36) : const Gap(24),
                    ],
                  ),
                ),
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildVideoPlayer() {
    if (_hasError) {
      return Container(
        color: AppColors.grey,
        child: Center(
          child: Text(
            "Error loading video.",
            style: Theme.of(context).textTheme.bodyEmphasis,
          ),
        ),
      );
    } else if (_isInitialized && _controller != null) {
      return FittedBox(
        fit: BoxFit.cover,
        clipBehavior: Clip.hardEdge,
        child: SizedBox(
          width: _controller!.value.size.width,
          height: _controller!.value.size.height,
          child: VideoPlayer(_controller!),
        ),
      );
    } else {
      return Container(
        color: AppColors.grey,
        child: const Center(
          child: Loader(),
        ),
      ); //show loading indicator
    }
  }
}
