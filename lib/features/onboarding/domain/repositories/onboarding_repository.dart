import 'package:gotcha_mfg_app/features/home/<USER>/models/emotions_detail_response.dart';
import 'package:gotcha_mfg_app/features/home/<USER>/models/emotions_response.dart';
import 'package:gotcha_mfg_app/features/onboarding/data/models/post_onboarding_response.dart';
import 'package:gotcha_mfg_app/features/onboarding/domain/usecases/onboarding_detail_emotion.dart';
import 'package:gotcha_mfg_app/features/onboarding/domain/usecases/post_onboarding.dart';
import 'package:gotcha_mfg_app/shared/models/result.dart';

abstract class OnBoardingRepository {
  Future<Result<EmotionsResponse>> getOnboarding();
  Future<Result<EmotionsDetailResponse>> getEmotionsDetail(
      OnboardingEmotionDetailParams params);
  Future<Result<PostOnboardingResponse>> postOnboarding(
      OnboardingPostParams params);
}
