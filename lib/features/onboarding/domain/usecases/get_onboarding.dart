// class GetOnboardingUsecase
//     implements UseCase<Result<CheckInResponse>, CheckInRequest> {
//   final HomeRepository repository;
//   CheckInUsecase(this.repository);

//   @override
//   Future<Result<GetOnboardingResponse>> call(CheckInRequest params) {
//     return repository.checkIn(params);
//   }
// }
import 'package:gotcha_mfg_app/core/usecase/usecase.dart';
import 'package:gotcha_mfg_app/features/home/<USER>/models/emotions_response.dart';
import 'package:gotcha_mfg_app/features/onboarding/domain/repositories/onboarding_repository.dart';
import 'package:gotcha_mfg_app/shared/models/result.dart';

class GetOnboardingUsecase
    implements UseCase<Result<EmotionsResponse>, NoParams> {
  GetOnboardingUsecase(this._repository);

  final OnBoardingRepository _repository;

  @override
  Future<Result<EmotionsResponse>> call(NoParams params) async {
    return _repository.getOnboarding();
  }
}
