import 'package:gotcha_mfg_app/core/usecase/usecase.dart';
import 'package:gotcha_mfg_app/features/home/<USER>/models/check_in_request.dart';
import 'package:gotcha_mfg_app/features/onboarding/data/models/post_onboarding_response.dart';
import 'package:gotcha_mfg_app/features/onboarding/domain/repositories/onboarding_repository.dart';
import 'package:gotcha_mfg_app/shared/models/result.dart';

class PostOnboardingUsecase
    implements UseCase<Result<PostOnboardingResponse>, OnboardingPostParams> {
  PostOnboardingUsecase(this._repository);

  final OnBoardingRepository _repository;

  @override
  Future<Result<PostOnboardingResponse>> call(
      OnboardingPostParams params) async {
    return _repository.postOnboarding(params);
  }
}

class OnboardingPostParams {
  final String timezone;
  final String deviceId;
  final String checkInTypesId;
  final String fcmDeviceId;
  final List<Question> questions;
  OnboardingPostParams(
      this.deviceId, this.checkInTypesId, this.fcmDeviceId, this.timezone,
      {required this.questions});
  Map<String, dynamic> toJson() {
    return {
      "time_zone": timezone,
      'device_id': deviceId,
      'fcm_device_id': fcmDeviceId,
      'check_in_types_id': checkInTypesId,
      'questions': questions.map((question) => question.toJson()).toList(),
    };
  }
}
