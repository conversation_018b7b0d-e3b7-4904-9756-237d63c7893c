import 'package:gotcha_mfg_app/core/usecase/usecase.dart';
import 'package:gotcha_mfg_app/features/home/<USER>/models/emotions_detail_response.dart';
import 'package:gotcha_mfg_app/features/onboarding/domain/repositories/onboarding_repository.dart';
import 'package:gotcha_mfg_app/shared/models/result.dart';

class OnboardingDetailEmotionsUsecase
    implements
        UseCase<Result<EmotionsDetailResponse>, OnboardingEmotionDetailParams> {
  OnboardingDetailEmotionsUsecase(this._repository);

  final OnBoardingRepository _repository;

  @override
  Future<Result<EmotionsDetailResponse>> call(
      OnboardingEmotionDetailParams params) async {
    return _repository.getEmotionsDetail(params);
  }
}

class OnboardingEmotionDetailParams {
  final String id;
  OnboardingEmotionDetailParams({required this.id});
}
