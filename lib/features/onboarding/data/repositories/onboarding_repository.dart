import 'package:gotcha_mfg_app/features/home/<USER>/models/emotions_detail_response.dart';
import 'package:gotcha_mfg_app/features/home/<USER>/models/emotions_response.dart';
import 'package:gotcha_mfg_app/features/onboarding/data/data_sources/onboarding_data_source.dart';
import 'package:gotcha_mfg_app/features/onboarding/data/models/post_onboarding_response.dart';
import 'package:gotcha_mfg_app/features/onboarding/domain/repositories/onboarding_repository.dart';
import 'package:gotcha_mfg_app/features/onboarding/domain/usecases/onboarding_detail_emotion.dart';
import 'package:gotcha_mfg_app/features/onboarding/domain/usecases/post_onboarding.dart';
import 'package:gotcha_mfg_app/shared/models/result.dart';

class OnboardRepositoryImpl extends OnBoardingRepository {
  final OnBoardingDataSource _dataSource;

  OnboardRepositoryImpl(this._dataSource);
  @override
  Future<Result<EmotionsResponse>> getOnboarding() async {
    return await _dataSource.getOnboarding();
  }

  @override
  Future<Result<EmotionsDetailResponse>> getEmotionsDetail(
      OnboardingEmotionDetailParams params) async {
    return await _dataSource.getEmotionsDetail(params);
  }

  @override
  Future<Result<PostOnboardingResponse>> postOnboarding(
      OnboardingPostParams params) async {
    return await _dataSource.postOnboarding(params);
  }
}
