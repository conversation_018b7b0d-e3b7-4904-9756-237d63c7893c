import 'package:dio/dio.dart';
import 'package:firebase_crashlytics/firebase_crashlytics.dart';
import 'package:gotcha_mfg_app/core/storage/token_manager.dart';
import 'package:gotcha_mfg_app/core/utils/app_print.dart';
import 'package:gotcha_mfg_app/features/home/<USER>/models/emotions_detail_response.dart';
import 'package:gotcha_mfg_app/features/home/<USER>/models/emotions_response.dart';
import 'package:gotcha_mfg_app/features/onboarding/data/models/post_onboarding_response.dart';
import 'package:gotcha_mfg_app/features/onboarding/domain/usecases/onboarding_detail_emotion.dart';
import 'package:gotcha_mfg_app/features/onboarding/domain/usecases/post_onboarding.dart';
import 'package:gotcha_mfg_app/locator.dart';
import 'package:gotcha_mfg_app/shared/models/result.dart';

abstract class OnBoardingDataSource {
  Future<Result<EmotionsResponse>> getOnboarding();
  Future<Result<EmotionsDetailResponse>> getEmotionsDetail(
      OnboardingEmotionDetailParams params);
  Future<Result<PostOnboardingResponse>> postOnboarding(
      OnboardingPostParams params);
}

class OnboardingDataSourceImpl implements OnBoardingDataSource {
  final Dio _dio;

  OnboardingDataSourceImpl(this._dio);

  @override
  Future<Result<EmotionsResponse>> getOnboarding() async {
    try {
      final response = await _dio.get('/app/onboarding/types/true');
      if (response.statusCode == 200) {
        final data = response.data;
        final onboardingResponse = EmotionsResponse.fromJson(data);
        return Result.success(onboardingResponse);
      } else {
        return Result.failure(
            '${response.statusCode} - ${response.data['message']}');
      }
    } catch (e) {
      FirebaseCrashlytics.instance.recordError(e, StackTrace.current);
      info('error----${e.toString()}');
      return Result.failure(
          'An error occurred during getting feelings: ${e.toString()}');
    }
  }

  @override
  Future<Result<EmotionsDetailResponse>> getEmotionsDetail(
      OnboardingEmotionDetailParams params) async {
    try {
      final response =
          await _dio.get('/app/onboarding/questions/${params.id}/true');
      if (response.statusCode == 200) {
        final data = response.data;
        final emotionDeatilResponse = EmotionsDetailResponse.fromJson(data);
        return Result.success(emotionDeatilResponse);
      } else {
        return Result.failure(
            '${response.statusCode} - ${response.data['message']}');
      }
    } catch (e) {
      FirebaseCrashlytics.instance.recordError(e, StackTrace.current);
      return Result.failure(
          'An error occurred during getting emotions: ${e.toString()}');
    }
  }

  @override
  Future<Result<PostOnboardingResponse>> postOnboarding(
      OnboardingPostParams params) async {
    try {
      info('params----${params.toJson()}');
      info('params----${_dio.options.headers.entries}');
      final response = await _dio.post('/app/onboarding/', data: params);
      if (response.statusCode == 200) {
        final data = response.data;
        final onboardingResponse = PostOnboardingResponse.fromJson(data);
        sl<TokenManager>().saveTokens(
          accessToken: onboardingResponse.data?.accessToken ?? '',
          refreshToken: onboardingResponse.data?.refreshToken ?? '',
        );
        sl<TokenManager>().saveTokens(
          accessToken: onboardingResponse.data?.accessToken ?? '',
          refreshToken: onboardingResponse.data?.refreshToken ?? '',
        );
        var uniqueId = await sl<TokenManager>().getAccessToken();
        info('uniqueId----$uniqueId');
        return Result.success(onboardingResponse);
      } else {
        return Result.failure(
            '${response.statusCode} - ${response.data['message']}');
      }
    } catch (e) {
      info('error----${e.toString()}');
      FirebaseCrashlytics.instance.recordError(e, StackTrace.current);
      return Result.failure(
          'An error occurred during onboarding: ${e.toString()}');
    }
  }
}
