// To parse this JSON data, do
//
//     final postOnboardingResponse = postOnboardingResponseFromJson(jsonString);

import 'dart:convert';

PostOnboardingResponse postOnboardingResponseFromJson(String str) =>
    PostOnboardingResponse.fromJson(json.decode(str));

String postOnboardingResponseToJson(PostOnboardingResponse data) =>
    json.encode(data.toJson());

class PostOnboardingResponse {
  final String? message;
  final String? status;
  final Data? data;

  PostOnboardingResponse({
    this.message,
    this.status,
    this.data,
  });

  PostOnboardingResponse copyWith({
    String? message,
    String? status,
    Data? data,
  }) =>
      PostOnboardingResponse(
        message: message ?? this.message,
        status: status ?? this.status,
        data: data ?? this.data,
      );

  factory PostOnboardingResponse.fromJson(Map<String, dynamic> json) =>
      PostOnboardingResponse(
        message: json["message"],
        status: json["status"],
        data: json["data"] == null ? null : Data.fromJson(json["data"]),
      );

  Map<String, dynamic> toJson() => {
        "message": message,
        "status": status,
        "data": data?.toJson(),
      };
}

class Data {
  final String? accessToken;
  final String? refreshToken;
  final String? userId;

  Data({
    this.accessToken,
    this.refreshToken,
    this.userId,
  });

  Data copyWith({
    String? accessToken,
    String? refreshToken,
    String? userId,
  }) =>
      Data(
        accessToken: accessToken ?? this.accessToken,
        refreshToken: refreshToken ?? this.refreshToken,
        userId: userId ?? this.userId,
      );

  factory Data.fromJson(Map<String, dynamic> json) => Data(
        accessToken: json["access_token"],
        refreshToken: json["refresh_token"],
        userId: json["user_id"],
      );

  Map<String, dynamic> toJson() => {
        "access_token": accessToken,
        "refresh_token": refreshToken,
        "user_id": userId,
      };
}
