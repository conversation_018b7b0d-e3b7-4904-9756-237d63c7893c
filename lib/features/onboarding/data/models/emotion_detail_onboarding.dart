// // To parse this JSON data, do
// //
// //     final onboardingEmotionResponse = onboardingEmotionResponseFromJson(jsonString);

// import 'dart:convert';

// OnboardingEmotionResponse onboardingEmotionResponseFromJson(String str) => OnboardingEmotionResponse.fromJson(json.decode(str));

// String onboardingEmotionResponseToJson(OnboardingEmotionResponse data) => json.encode(data.toJson());

// class OnboardingEmotionResponse {
//     final String? message;
//     final String? status;
//     final List<Datum>? data;

//     OnboardingEmotionResponse({
//         this.message,
//         this.status,
//         this.data,
//     });

//     OnboardingEmotionResponse copyWith({
//         String? message,
//         String? status,
//         List<Datum>? data,
//     }) =>
//         OnboardingEmotionResponse(
//             message: message ?? this.message,
//             status: status ?? this.status,
//             data: data ?? this.data,
//         );

//     factory OnboardingEmotionResponse.fromJson(Map<String, dynamic> json) => OnboardingEmotionResponse(
//         message: json["message"],
//         status: json["status"],
//         data: json["data"] == null ? [] : List<Datum>.from(json["data"]!.map((x) => Datum.fromJson(x))),
//     );

//     Map<String, dynamic> toJson() => {
//         "message": message,
//         "status": status,
//         "data": data == null ? [] : List<dynamic>.from(data!.map((x) => x.toJson())),
//     };
// }

// class Datum {
//     final String? questionId;
//     final String? question;
//     final List<Answer>? answers;
//     final bool? isOptional;
//     final bool? isNote;
//     final bool? isOther;
//     final String? description;
//     final String? type;
//     final int? orderOfQuestion;

//     Datum({
//         this.questionId,
//         this.question,
//         this.answers,
//         this.isOptional,
//         this.isNote,
//         this.isOther,
//         this.description,
//         this.type,
//         this.orderOfQuestion,
//     });

//     Datum copyWith({
//         String? questionId,
//         String? question,
//         List<Answer>? answers,
//         bool? isOptional,
//         bool? isNote,
//         bool? isOther,
//         String? description,
//         String? type,
//         int? orderOfQuestion,
//     }) =>
//         Datum(
//             questionId: questionId ?? this.questionId,
//             question: question ?? this.question,
//             answers: answers ?? this.answers,
//             isOptional: isOptional ?? this.isOptional,
//             isNote: isNote ?? this.isNote,
//             isOther: isOther ?? this.isOther,
//             description: description ?? this.description,
//             type: type ?? this.type,
//             orderOfQuestion: orderOfQuestion ?? this.orderOfQuestion,
//         );

//     factory Datum.fromJson(Map<String, dynamic> json) => Datum(
//         questionId: json["question_id"],
//         question: json["question"],
//         answers: json["answers"] == null ? [] : List<Answer>.from(json["answers"]!.map((x) => Answer.fromJson(x))),
//         isOptional: json["is_optional"],
//         isNote: json["is_note"],
//         isOther: json["is_other"],
//         description: json["description"],
//         type: json["type"],
//         orderOfQuestion: json["order_of_question"],
//     );

//     Map<String, dynamic> toJson() => {
//         "question_id": questionId,
//         "question": question,
//         "answers": answers == null ? [] : List<dynamic>.from(answers!.map((x) => x.toJson())),
//         "is_optional": isOptional,
//         "is_note": isNote,
//         "is_other": isOther,
//         "description": description,
//         "type": type,
//         "order_of_question": orderOfQuestion,
//     };
// }

// class Answer {
//     final String? answerId;
//     final String? answer;

//     Answer({
//         this.answerId,
//         this.answer,
//     });

//     Answer copyWith({
//         String? answerId,
//         String? answer,
//     }) =>
//         Answer(
//             answerId: answerId ?? this.answerId,
//             answer: answer ?? this.answer,
//         );

//     factory Answer.fromJson(Map<String, dynamic> json) => Answer(
//         answerId: json["answer_id"],
//         answer: json["answer"],
//     );

//     Map<String, dynamic> toJson() => {
//         "answer_id": answerId,
//         "answer": answer,
//     };
// }
