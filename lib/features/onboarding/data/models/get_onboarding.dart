// To parse this JSON data, do
//
//     final getOnboardingResponse = getOnboardingResponseFromJson(jsonString);

import 'dart:convert';

GetOnboardingResponse getOnboardingResponseFromJson(String str) =>
    GetOnboardingResponse.fromJson(json.decode(str));

String getOnboardingResponseToJson(GetOnboardingResponse data) =>
    json.encode(data.toJson());

class GetOnboardingResponse {
  final String? message;
  final String? status;
  final Data? data;

  GetOnboardingResponse({
    this.message,
    this.status,
    this.data,
  });

  GetOnboardingResponse copyWith({
    String? message,
    String? status,
    Data? data,
  }) =>
      GetOnboardingResponse(
        message: message ?? this.message,
        status: status ?? this.status,
        data: data ?? this.data,
      );

  factory GetOnboardingResponse.fromJson(Map<String, dynamic> json) =>
      GetOnboardingResponse(
        message: json["message"],
        status: json["status"],
        data: json["data"] == null ? null : Data.fromJson(json["data"]),
      );

  Map<String, dynamic> toJson() => {
        "message": message,
        "status": status,
        "data": data?.toJson(),
      };
}

class Data {
  final List<Type>? type;
  final bool? isCheckInToday;
  final dynamic latestCheckInType;
  final dynamic latestCheckInTypeId;
  final List<Question>? question;

  Data({
    this.type,
    this.isCheckInToday,
    this.latestCheckInType,
    this.latestCheckInTypeId,
    this.question,
  });

  Data copyWith({
    List<Type>? type,
    bool? isCheckInToday,
    dynamic latestCheckInType,
    dynamic latestCheckInTypeId,
    List<Question>? question,
  }) =>
      Data(
        type: type ?? this.type,
        isCheckInToday: isCheckInToday ?? this.isCheckInToday,
        latestCheckInType: latestCheckInType ?? this.latestCheckInType,
        latestCheckInTypeId: latestCheckInTypeId ?? this.latestCheckInTypeId,
        question: question ?? this.question,
      );

  factory Data.fromJson(Map<String, dynamic> json) => Data(
        type: json["type"] == null
            ? []
            : List<Type>.from(json["type"]!.map((x) => Type.fromJson(x))),
        isCheckInToday: json["is_check_in_today"],
        latestCheckInType: json["latest_check_in_type"],
        latestCheckInTypeId: json["latest_check_in_type_id"],
        question: json["question"] == null
            ? []
            : List<Question>.from(
                json["question"]!.map((x) => Question.fromJson(x))),
      );

  Map<String, dynamic> toJson() => {
        "type": type == null
            ? []
            : List<dynamic>.from(type!.map((x) => x.toJson())),
        "is_check_in_today": isCheckInToday,
        "latest_check_in_type": latestCheckInType,
        "latest_check_in_type_id": latestCheckInTypeId,
        "question": question == null
            ? []
            : List<dynamic>.from(question!.map((x) => x.toJson())),
      };
}

class Question {
  final String? id;
  final String? question;
  final DateTime? createdAt;
  final DateTime? updatedAt;
  final bool? isOptional;
  final bool? isNote;
  final bool? isOther;
  final String? description;
  final String? type;
  final int? orderOfQuestion;

  Question({
    this.id,
    this.question,
    this.createdAt,
    this.updatedAt,
    this.isOptional,
    this.isNote,
    this.isOther,
    this.description,
    this.type,
    this.orderOfQuestion,
  });

  Question copyWith({
    String? id,
    String? question,
    DateTime? createdAt,
    DateTime? updatedAt,
    bool? isOptional,
    bool? isNote,
    bool? isOther,
    String? description,
    String? type,
    int? orderOfQuestion,
  }) =>
      Question(
        id: id ?? this.id,
        question: question ?? this.question,
        createdAt: createdAt ?? this.createdAt,
        updatedAt: updatedAt ?? this.updatedAt,
        isOptional: isOptional ?? this.isOptional,
        isNote: isNote ?? this.isNote,
        isOther: isOther ?? this.isOther,
        description: description ?? this.description,
        type: type ?? this.type,
        orderOfQuestion: orderOfQuestion ?? this.orderOfQuestion,
      );

  factory Question.fromJson(Map<String, dynamic> json) => Question(
        id: json["id"],
        question: json["question"],
        createdAt: json["created_at"] == null
            ? null
            : DateTime.parse(json["created_at"]),
        updatedAt: json["updated_at"] == null
            ? null
            : DateTime.parse(json["updated_at"]),
        isOptional: json["is_optional"],
        isNote: json["is_note"],
        isOther: json["is_other"],
        description: json["description"],
        type: json["type"],
        orderOfQuestion: json["order_of_question"],
      );

  Map<String, dynamic> toJson() => {
        "id": id,
        "question": question,
        "created_at": createdAt?.toIso8601String(),
        "updated_at": updatedAt?.toIso8601String(),
        "is_optional": isOptional,
        "is_note": isNote,
        "is_other": isOther,
        "description": description,
        "type": type,
        "order_of_question": orderOfQuestion,
      };
}

class Type {
  final String? id;
  final String? type;
  final DateTime? createdAt;
  final DateTime? updatedAt;
  final String? description;

  Type({
    this.id,
    this.type,
    this.createdAt,
    this.updatedAt,
    this.description,
  });

  Type copyWith({
    String? id,
    String? type,
    DateTime? createdAt,
    DateTime? updatedAt,
    String? description,
  }) =>
      Type(
        id: id ?? this.id,
        type: type ?? this.type,
        createdAt: createdAt ?? this.createdAt,
        updatedAt: updatedAt ?? this.updatedAt,
        description: description ?? this.description,
      );

  factory Type.fromJson(Map<String, dynamic> json) => Type(
        id: json["id"],
        type: json["type"],
        createdAt: json["created_at"] == null
            ? null
            : DateTime.parse(json["created_at"]),
        updatedAt: json["updated_at"] == null
            ? null
            : DateTime.parse(json["updated_at"]),
        description: json["description"],
      );

  Map<String, dynamic> toJson() => {
        "id": id,
        "type": type,
        "created_at": createdAt?.toIso8601String(),
        "updated_at": updatedAt?.toIso8601String(),
        "description": description,
      };
}
