import 'package:gotcha_mfg_app/features/explore/data/data_sources/explore_remote_data_source.dart';
import 'package:gotcha_mfg_app/features/explore/data/models/categories_response.dart';
import 'package:gotcha_mfg_app/features/explore/data/models/filtered_response.dart';
import 'package:gotcha_mfg_app/features/explore/data/models/workouts_response.dart';
import 'package:gotcha_mfg_app/features/explore/domain/repositories/explore_repository.dart';
import 'package:gotcha_mfg_app/features/explore/domain/usecases/get_filtered_response_use_case.dart';
import 'package:gotcha_mfg_app/shared/models/result.dart';

class ExploreRepositoryImpl implements ExploreRepository {
  ExploreRepositoryImpl(this._remoteDataSource);

  final ExploreRemoteDataSource _remoteDataSource;

  @override
  Future<Result<CategoriesResponse>> getCategories() async {
    return await _remoteDataSource.getCategories();
  }

  @override
  Future<Result<FilteredResponse>> getFilteredExercises(
      FilteredResponseParams params) async {
    return await _remoteDataSource.getFilteredExercises(params);
  }

  @override
  Future<Result<WorkoutsResponse>> getWorkouts() async {
    return await _remoteDataSource.getWorkouts();
  }
}
