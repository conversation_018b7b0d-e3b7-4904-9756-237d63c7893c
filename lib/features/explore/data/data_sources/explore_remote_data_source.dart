import 'package:dio/dio.dart';
import 'package:firebase_crashlytics/firebase_crashlytics.dart';
import 'package:gotcha_mfg_app/features/explore/data/models/categories_response.dart';
import 'package:gotcha_mfg_app/features/explore/data/models/filtered_response.dart';
import 'package:gotcha_mfg_app/features/explore/data/models/workouts_response.dart';
import 'package:gotcha_mfg_app/features/explore/domain/usecases/get_filtered_response_use_case.dart';
import 'package:gotcha_mfg_app/shared/models/result.dart';

abstract class ExploreRemoteDataSource {
  Future<Result<CategoriesResponse>> getCategories();
  Future<Result<FilteredResponse>> getFilteredExercises(
      FilteredResponseParams params);
  Future<Result<WorkoutsResponse>> getWorkouts();
}

class ExploreRemoteDataSourceImpl implements ExploreRemoteDataSource {
  ExploreRemoteDataSourceImpl(this._dio);

  final Dio _dio;

  @override
  Future<Result<CategoriesResponse>> getCategories() async {
    try {
      final response = await _dio.get('/app/exercises/category');
      if (response.statusCode == 200) {
        final data = response.data;
        final categoriesResponse = CategoriesResponse.fromJson(data);
        return Result.success(categoriesResponse);
      } else {
        return Result.failure(
            '${response.statusCode} - ${response.data['message']}');
      }
    } catch (e) {
      FirebaseCrashlytics.instance.recordError(e, StackTrace.current);
      return Result.failure(
          'An error occurred during getting categories: ${e.toString()}');
    }
  }

  @override
  Future<Result<FilteredResponse>> getFilteredExercises(
      FilteredResponseParams params) async {
    try {
      final response = await _dio.get(
        '/app/exercise/filter',
        queryParameters: params.toJson(),
      );
      if (response.statusCode == 200) {
        final data = response.data;
        final filteredResponse = FilteredResponse.fromJson(data);
        return Result.success(filteredResponse);
      } else {
        return Result.failure(
            '${response.statusCode} - ${response.data['message']}');
      }
    } catch (e) {
      FirebaseCrashlytics.instance.recordError(e, StackTrace.current);
      return Result.failure(
          'An error occurred during getting filtered exercises: ${e.toString()}');
    }
  }

  @override
  Future<Result<WorkoutsResponse>> getWorkouts() async {
    try {
      final response = await _dio.get('/app/workout');
      if (response.statusCode == 200) {
        final data = response.data;
        final workoutsResponse = WorkoutsResponse.fromJson(data);
        return Result.success(workoutsResponse);
      } else {
        return Result.failure(
            '${response.statusCode} - ${response.data['message']}');
      }
    } catch (e) {
      FirebaseCrashlytics.instance.recordError(e, StackTrace.current);
      return Result.failure(
          'An error occurred during getting workouts: ${e.toString()}');
    }
  }
}
