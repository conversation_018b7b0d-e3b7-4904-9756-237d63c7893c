import 'dart:convert';

class FilteredResponse {
  final String? message;
  final String? status;
  final Data? data;

  FilteredResponse({
    this.message,
    this.status,
    this.data,
  });

  FilteredResponse copyWith({
    String? message,
    String? status,
    Data? data,
  }) =>
      FilteredResponse(
        message: message ?? this.message,
        status: status ?? this.status,
        data: data ?? this.data,
      );

  factory FilteredResponse.fromRawJson(String str) =>
      FilteredResponse.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory FilteredResponse.fromJson(Map<String, dynamic> json) =>
      FilteredResponse(
        message: json["message"],
        status: json["status"],
        data: json["data"] == null ? null : Data.fromJson(json["data"]),
      );

  Map<String, dynamic> toJson() => {
        "message": message,
        "status": status,
        "data": data?.toJson(),
      };
}

class Data {
  final List<Exercise>? exercises;
  final List<DataWorkout>? workouts;

  Data({
    this.exercises,
    this.workouts,
  });

  Data copyWith({
    List<Exercise>? exercises,
    List<DataWorkout>? workouts,
  }) =>
      Data(
        exercises: exercises ?? this.exercises,
        workouts: workouts ?? this.workouts,
      );

  factory Data.fromRawJson(String str) => Data.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory Data.fromJson(Map<String, dynamic> json) => Data(
        exercises: json["exercises"] == null
            ? []
            : List<Exercise>.from(
                json["exercises"]!.map((x) => Exercise.fromJson(x))),
        workouts: json["workouts"] == null
            ? []
            : List<DataWorkout>.from(
                json["workouts"]!.map((x) => DataWorkout.fromJson(x))),
      );

  Map<String, dynamic> toJson() => {
        "exercises": exercises == null
            ? []
            : List<dynamic>.from(exercises!.map((x) => x.toJson())),
        "workouts": workouts == null
            ? []
            : List<dynamic>.from(workouts!.map((x) => x.toJson())),
      };
}

class Exercise {
  final String? id;
  final String? title;
  final String? mediaDuration;
  final String? thumbnailUrl;
  final String? mediaType;
  final String? mediaUrl;
  final String? exerciseType;
  final bool? isFavorite;

  Exercise({
    this.id,
    this.title,
    this.mediaDuration,
    this.thumbnailUrl,
    this.mediaType,
    this.mediaUrl,
    this.exerciseType,
    this.isFavorite,
  });

  Exercise copyWith({
    String? id,
    String? title,
    String? mediaDuration,
    String? thumbnailUrl,
    String? mediaType,
    String? mediaUrl,
    String? exerciseType,
    bool? isFavorite,
  }) =>
      Exercise(
        id: id ?? this.id,
        title: title ?? this.title,
        mediaDuration: mediaDuration ?? this.mediaDuration,
        thumbnailUrl: thumbnailUrl ?? this.thumbnailUrl,
        mediaType: mediaType ?? this.mediaType,
        mediaUrl: mediaUrl ?? this.mediaUrl,
        exerciseType: exerciseType ?? this.exerciseType,
        isFavorite: isFavorite ?? this.isFavorite,
      );

  factory Exercise.fromRawJson(String str) =>
      Exercise.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory Exercise.fromJson(Map<String, dynamic> json) => Exercise(
        id: json["id"],
        title: json["title"],
        mediaDuration: json["media_duration"],
        thumbnailUrl: json["thumbnail_url"],
        mediaType: json["media_type"],
        mediaUrl: json["media_url"],
        exerciseType: json["exercise_type"],
        isFavorite: json["is_favorite"],
      );

  Map<String, dynamic> toJson() => {
        "id": id,
        "title": title,
        "media_duration": mediaDuration,
        "thumbnail_url": thumbnailUrl,
        "media_type": mediaType,
        "media_url": mediaUrl,
        "exercise_type": exerciseType,
        "is_favorite": isFavorite,
      };
}

class DataWorkout {
  final String? seriesId;
  final String? seriesTitle;
  final String? seriesDescription;
  final String? seriesDuration;
  final String? seriesImageUrl;
  final List<WorkoutWorkout>? workouts;
  final bool? isFavorite;
  final String? exerciseType;

  DataWorkout({
    this.seriesId,
    this.seriesTitle,
    this.seriesDescription,
    this.seriesDuration,
    this.seriesImageUrl,
    this.workouts,
    this.isFavorite,
    this.exerciseType,
  });

  DataWorkout copyWith({
    String? seriesId,
    String? seriesTitle,
    String? seriesDescription,
    String? seriesDuration,
    String? seriesImageUrl,
    List<WorkoutWorkout>? workouts,
    bool? isFavorite,
    String? exerciseType,
  }) =>
      DataWorkout(
        seriesId: seriesId ?? this.seriesId,
        seriesTitle: seriesTitle ?? this.seriesTitle,
        seriesDescription: seriesDescription ?? this.seriesDescription,
        seriesDuration: seriesDuration ?? this.seriesDuration,
        seriesImageUrl: seriesImageUrl ?? this.seriesImageUrl,
        workouts: workouts ?? this.workouts,
        isFavorite: isFavorite ?? this.isFavorite,
        exerciseType: exerciseType ?? this.exerciseType,
      );

  factory DataWorkout.fromRawJson(String str) =>
      DataWorkout.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory DataWorkout.fromJson(Map<String, dynamic> json) => DataWorkout(
        seriesId: json["series_id"],
        seriesTitle: json["series_title"],
        seriesDescription: json["series_description"],
        seriesDuration: json["series_duration"],
        seriesImageUrl: json["series_image_url"],
        workouts: json["workouts"] == null
            ? []
            : List<WorkoutWorkout>.from(
                json["workouts"]!.map((x) => WorkoutWorkout.fromJson(x))),
        isFavorite: json["is_favorite"],
        exerciseType: json["exercise_type"],
      );

  Map<String, dynamic> toJson() => {
        "series_id": seriesId,
        "series_title": seriesTitle,
        "series_description": seriesDescription,
        "series_duration": seriesDuration,
        "series_image_url": seriesImageUrl,
        "workouts": workouts == null
            ? []
            : List<dynamic>.from(workouts!.map((x) => x.toJson())),
        "is_favorite": isFavorite,
        "exercise_type": exerciseType,
      };
}

class WorkoutWorkout {
  final String? workoutId;
  final String? exerciseId;
  final String? exerciseTitle;
  final String? exerciseMediaDuration;
  final String? exerciseThumbnailUrl;
  final String? exerciseMediaType;
  final String? exerciseMediaUrl;
  final List<String>? categories;

  WorkoutWorkout({
    this.workoutId,
    this.exerciseId,
    this.exerciseTitle,
    this.exerciseMediaDuration,
    this.exerciseThumbnailUrl,
    this.exerciseMediaType,
    this.exerciseMediaUrl,
    this.categories,
  });

  WorkoutWorkout copyWith({
    String? workoutId,
    String? exerciseId,
    String? exerciseTitle,
    String? exerciseMediaDuration,
    String? exerciseThumbnailUrl,
    String? exerciseMediaType,
    String? exerciseMediaUrl,
    List<String>? categories,
  }) =>
      WorkoutWorkout(
        workoutId: workoutId ?? this.workoutId,
        exerciseId: exerciseId ?? this.exerciseId,
        exerciseTitle: exerciseTitle ?? this.exerciseTitle,
        exerciseMediaDuration:
            exerciseMediaDuration ?? this.exerciseMediaDuration,
        exerciseThumbnailUrl: exerciseThumbnailUrl ?? this.exerciseThumbnailUrl,
        exerciseMediaType: exerciseMediaType ?? this.exerciseMediaType,
        exerciseMediaUrl: exerciseMediaUrl ?? this.exerciseMediaUrl,
        categories: categories ?? this.categories,
      );

  factory WorkoutWorkout.fromRawJson(String str) =>
      WorkoutWorkout.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory WorkoutWorkout.fromJson(Map<String, dynamic> json) => WorkoutWorkout(
        workoutId: json["workout_id"],
        exerciseId: json["exercise_id"],
        exerciseTitle: json["exercise_title"],
        exerciseMediaDuration: json["exercise_media_duration"],
        exerciseThumbnailUrl: json["exercise_thumbnail_url"],
        exerciseMediaType: json["exercise_media_type"],
        exerciseMediaUrl: json["exercise_media_url"],
        categories: json["categories"] == null
            ? []
            : List<String>.from(json["categories"]!.map((x) => x)),
      );

  Map<String, dynamic> toJson() => {
        "workout_id": workoutId,
        "exercise_id": exerciseId,
        "exercise_title": exerciseTitle,
        "exercise_media_duration": exerciseMediaDuration,
        "exercise_thumbnail_url": exerciseThumbnailUrl,
        "exercise_media_type": exerciseMediaType,
        "exercise_media_url": exerciseMediaUrl,
        "categories": categories == null
            ? []
            : List<dynamic>.from(categories!.map((x) => x)),
      };
}
