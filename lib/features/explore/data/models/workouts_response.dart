import 'dart:convert';

class WorkoutsResponse {
  String? message;
  String? status;
  List<List<Datum>>? data;

  WorkoutsResponse({
    this.message,
    this.status,
    this.data,
  });

  factory WorkoutsResponse.fromRawJson(String str) =>
      WorkoutsResponse.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory WorkoutsResponse.fromJson(Map<String, dynamic> json) =>
      WorkoutsResponse(
        message: json["message"],
        status: json["status"],
        data: json["data"] == null
            ? []
            : List<List<Datum>>.from(json["data"]!
                .map((x) => List<Datum>.from(x.map((x) => Datum.fromJson(x))))),
      );

  Map<String, dynamic> toJson() => {
        "message": message,
        "status": status,
        "data": data == null
            ? []
            : List<dynamic>.from(
                data!.map((x) => List<dynamic>.from(x.map((x) => x.toJson())))),
      };
}

class Datum {
  String? seriesId;
  String? seriesTitle;
  String? seriesDescription;
  String? seriesDuration;
  String? seriesImageUrl;
  List<Workout>? workouts;
  List<String>? categories;

  Datum({
    this.seriesId,
    this.seriesTitle,
    this.seriesDescription,
    this.seriesDuration,
    this.seriesImageUrl,
    this.workouts,
    this.categories,
  });

  factory Datum.fromRawJson(String str) => Datum.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory Datum.fromJson(Map<String, dynamic> json) => Datum(
        seriesId: json["series_id"],
        seriesTitle: json["series_title"],
        seriesDescription: json["series_description"],
        seriesDuration: json["series_duration"],
        seriesImageUrl: json["series_image_url"],
        workouts: json["workouts"] == null
            ? []
            : List<Workout>.from(
                json["workouts"]!.map((x) => Workout.fromJson(x))),
        categories: json["categories"] == null
            ? []
            : List<String>.from(json["categories"]!.map((x) => x)),
      );

  Map<String, dynamic> toJson() => {
        "series_id": seriesId,
        "series_title": seriesTitle,
        "series_description": seriesDescription,
        "series_duration": seriesDuration,
        "series_image_url": seriesImageUrl,
        "workouts": workouts == null
            ? []
            : List<dynamic>.from(workouts!.map((x) => x.toJson())),
        "categories": categories == null
            ? []
            : List<dynamic>.from(categories!.map((x) => x)),
      };
}

class Workout {
  String? workoutId;
  String? exerciseId;
  String? exerciseTitle;
  String? exerciseMediaDuration;
  String? exerciseThumbnailUrl;
  String? exerciseMediaType;
  String? exerciseMediaUrl;
  int? orderOfExercise;
  int? day;
  List<String>? categories;

  Workout({
    this.workoutId,
    this.exerciseId,
    this.exerciseTitle,
    this.exerciseMediaDuration,
    this.exerciseThumbnailUrl,
    this.exerciseMediaType,
    this.exerciseMediaUrl,
    this.orderOfExercise,
    this.day,
    this.categories,
  });

  factory Workout.fromRawJson(String str) => Workout.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory Workout.fromJson(Map<String, dynamic> json) => Workout(
        workoutId: json["workout_id"],
        exerciseId: json["exercise_id"],
        exerciseTitle: json["exercise_title"],
        exerciseMediaDuration: json["exercise_media_duration"],
        exerciseThumbnailUrl: json["exercise_thumbnail_url"],
        exerciseMediaType: json["exercise_media_type"],
        exerciseMediaUrl: json["exercise_media_url"],
        orderOfExercise: json["order_of_exercise"],
        day: json["day"],
        categories: json["categories"] == null
            ? []
            : List<String>.from(json["categories"]!.map((x) => x)),
      );

  Map<String, dynamic> toJson() => {
        "workout_id": workoutId,
        "exercise_id": exerciseId,
        "exercise_title": exerciseTitle,
        "exercise_media_duration": exerciseMediaDuration,
        "exercise_thumbnail_url": exerciseThumbnailUrl,
        "exercise_media_type": exerciseMediaType,
        "exercise_media_url": exerciseMediaUrl,
        "order_of_exercise": orderOfExercise,
        "day": day,
        "categories": categories == null
            ? []
            : List<dynamic>.from(categories!.map((x) => x)),
      };
}
