import 'package:gotcha_mfg_app/features/explore/data/models/filtered_response.dart';

class SearchResult {
  final String title;
  final String id;
  final String subtitle;
  final String duration;
  final String imageUrl;
  final String? mediaType;
  final bool isFavorite;
  final bool isExercise;
  final String url;

  SearchResult({
    required this.isExercise,
    required this.id,
    required this.title,
    required this.subtitle,
    required this.duration,
    required this.imageUrl,
    required this.mediaType,
    required this.isFavorite,
    required this.url,
  });

  @override
  String toString() {
    return 'SearchResult(title: $title, subtitle: $subtitle, duration: $duration, id: $id, imageUrl: $imageUrl, mediaType: $mediaType, isFavorite: $isFavorite)';
  }
}

class CombineExercisesAndWorkouts {
  List<SearchResult> call(FilteredResponse response) {
    final List<SearchResult> searchResults = [];

    // Add exercises to search results
    if (response.data?.exercises != null) {
      for (final exercise in response.data!.exercises!) {
        searchResults.add(
          SearchResult(
            isExercise: true,
            id: exercise.id ?? '',
            title: exercise.title ?? 'Untitled Exercise',
            subtitle: exercise.exerciseType ?? 'Exercise',
            duration: exercise.mediaDuration ?? 'N/A',
            imageUrl: exercise.thumbnailUrl ?? '',
            mediaType: exercise.mediaType,
            isFavorite: exercise.isFavorite ?? false,
            url: (exercise.mediaUrl != null && exercise.mediaUrl!.isNotEmpty)
                ? exercise.mediaUrl!
                : 'Find this exercise in The Mental Fitness Gym App.',
          ),
        );
      }
    }

    // Add workouts to search results
    if (response.data?.workouts != null) {
      for (final workoutSeries in response.data!.workouts!) {
        if (workoutSeries.workouts != null) {
          searchResults.add(
            SearchResult(
              isExercise: false,
              id: workoutSeries.seriesId ?? '',
              title: workoutSeries.seriesTitle ?? 'Untitled Workout',
              subtitle: workoutSeries.exerciseType ?? 'N/A',
              duration: workoutSeries.seriesDuration ?? '0',
              imageUrl: workoutSeries.seriesImageUrl ?? '',
              mediaType: null,
              isFavorite: workoutSeries.isFavorite ?? false,
              url: "Find this workout in The Mental Fitness Gym App.",
            ),
          );
        }
      }
    }

    return searchResults;
  }
}
