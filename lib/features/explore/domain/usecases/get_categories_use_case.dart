import 'package:gotcha_mfg_app/core/usecase/usecase.dart';
import 'package:gotcha_mfg_app/features/explore/data/models/categories_response.dart';
import 'package:gotcha_mfg_app/features/explore/domain/repositories/explore_repository.dart';
import 'package:gotcha_mfg_app/shared/models/result.dart';

class GetCategoriesUseCase
    implements UseCase<Result<CategoriesResponse>, NoParams> {
  GetCategoriesUseCase(this._repository);

  final ExploreRepository _repository;

  @override
  Future<Result<CategoriesResponse>> call(NoParams params) async {
    return _repository.getCategories();
  }
}
