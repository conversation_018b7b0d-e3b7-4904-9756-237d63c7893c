import 'package:gotcha_mfg_app/core/usecase/usecase.dart';
import 'package:gotcha_mfg_app/features/explore/data/models/workouts_response.dart';
import 'package:gotcha_mfg_app/features/explore/domain/repositories/explore_repository.dart';
import 'package:gotcha_mfg_app/shared/models/result.dart';

class GetWorkoutsUseCase
    implements UseCase<Result<WorkoutsResponse>, NoParams> {
  GetWorkoutsUseCase(this._repository);

  final ExploreRepository _repository;

  @override
  Future<Result<WorkoutsResponse>> call(NoParams params) async {
    return _repository.getWorkouts();
  }
}
