import 'package:gotcha_mfg_app/core/usecase/usecase.dart';
import 'package:gotcha_mfg_app/features/explore/data/models/filtered_response.dart';
import 'package:gotcha_mfg_app/features/explore/domain/repositories/explore_repository.dart';
import 'package:gotcha_mfg_app/shared/models/result.dart';

class GetFilteredExercisesUseCase
    implements UseCase<Result<FilteredResponse>, FilteredResponseParams> {
  GetFilteredExercisesUseCase(this._repository);

  final ExploreRepository _repository;

  @override
  Future<Result<FilteredResponse>> call(FilteredResponseParams params) async {
    return _repository.getFilteredExercises(params);
  }
}

class FilteredResponseParams {
  bool? isAll;
  final String? search;
  bool? isWorkout;
  bool? isCompleted;
  bool? isExercise;
  final String? categoryId;
  int page;
  final int? limit;

  FilteredResponseParams({
    this.isAll = true,
    this.search = "",
    this.isWorkout = false,
    this.isCompleted = false,
    this.isExercise = false,
    this.categoryId,
    required this.page,
    this.limit = 1000,
  });

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = {};
    if (search != null) data['search'] = search;
    if (isWorkout != null) data['isWorkout'] = isWorkout;
    if (isExercise != null) data['isExercise'] = isExercise;
    if (isCompleted != null) data['isCompleted'] = isCompleted;
    data['categoryId'] = categoryId;
    // if (isAll != null) data['isAll'] = isAll;

    data['page'] = page;
    if (limit != null) data['limit'] = limit;
    return data;
  }
}
