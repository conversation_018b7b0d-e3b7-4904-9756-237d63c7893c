import 'package:gotcha_mfg_app/features/explore/data/models/categories_response.dart';
import 'package:gotcha_mfg_app/features/explore/data/models/filtered_response.dart';
import 'package:gotcha_mfg_app/features/explore/data/models/workouts_response.dart';
import 'package:gotcha_mfg_app/features/explore/domain/usecases/get_filtered_response_use_case.dart';
import 'package:gotcha_mfg_app/shared/models/result.dart';

abstract class ExploreRepository {
  Future<Result<CategoriesResponse>> getCategories();
  Future<Result<FilteredResponse>> getFilteredExercises(
      FilteredResponseParams params);
  Future<Result<WorkoutsResponse>> getWorkouts();
}
