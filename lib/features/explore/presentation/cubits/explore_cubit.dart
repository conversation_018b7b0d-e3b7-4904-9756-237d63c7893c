import 'package:bloc/bloc.dart';
import 'package:equatable/equatable.dart';
import 'package:gotcha_mfg_app/core/usecase/usecase.dart';
import 'package:gotcha_mfg_app/features/explore/data/models/categories_response.dart';
import 'package:gotcha_mfg_app/features/explore/data/models/filtered_response.dart';
import 'package:gotcha_mfg_app/features/explore/data/models/workouts_response.dart';
import 'package:gotcha_mfg_app/features/explore/domain/usecases/get_categories_use_case.dart';
import 'package:gotcha_mfg_app/features/explore/domain/usecases/get_filtered_response_use_case.dart';
import 'package:gotcha_mfg_app/features/explore/domain/usecases/get_workouts_use_case.dart';

part 'explore_state.dart';

class ExploreCubit extends Cubit<ExploreState> {
  ExploreCubit(
    this._getCategoriesUseCase,
    this._getFilteredExercisesUseCase,
    this._getWorkoutsUseCase,
  ) : super(ExploreInitial());

  final GetCategoriesUseCase _getCategoriesUseCase;
  final GetFilteredExercisesUseCase _getFilteredExercisesUseCase;
  final GetWorkoutsUseCase _getWorkoutsUseCase;

  Future<void> getCategories() async {
    emit(ExploreLoading());
    final categoriesResult = await _getCategoriesUseCase.call(NoParams());
    if (categoriesResult.isSuccess) {
      emit(ExploreLoaded(
        categoriesResponse: categoriesResult.data!,
        filteredResponse: null,
        workoutsResponse: null,
      ));
    } else {
      final error = categoriesResult.error!;
      emit(ExploreError(error));
    }
  }

  Future<void> getCategoriesAndWorkouts() async {
    emit(ExploreLoading());
    final categories = await _getCategoriesUseCase.call(NoParams());
    final workouts = await _getWorkoutsUseCase.call(NoParams());
    if (categories.isSuccess && workouts.isSuccess) {
      emit(ExploreLoaded(
        categoriesResponse: categories.data!,
        filteredResponse: null,
        workoutsResponse: workouts.data!,
      ));
    } else {
      final error = categories.isSuccess ? workouts.error! : categories.error!;
      emit(ExploreError(error));
    }
  }

  Future<void> getFilteredExercises(FilteredResponseParams params) async {
    var previousState = state as ExploreLoaded;
    emit(ExploreLoading());
    final result = await _getFilteredExercisesUseCase.call(params);
    if (result.isSuccess) {
      emit(previousState.copyWith(filteredResponse: result.data!));
    } else {
      emit(ExploreError(result.error!));
    }
  }

  Future<void> getSearchSuggestions(FilteredResponseParams params) async {
    var previousState = state as ExploreLoaded;
    final result = await _getFilteredExercisesUseCase.call(params);
    if (result.isSuccess) {
      emit(previousState.copyWith(searchSuggestions: result.data!));
    } else {
      // do nothing
    }
  }
}
