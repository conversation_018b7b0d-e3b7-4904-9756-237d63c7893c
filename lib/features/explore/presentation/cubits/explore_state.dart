part of 'explore_cubit.dart';

sealed class ExploreState extends Equatable {
  const ExploreState();

  @override
  List<Object> get props => [];
}

final class ExploreInitial extends ExploreState {}

final class ExploreLoading extends ExploreState {}

final class ExploreLoaded extends ExploreState {
  final CategoriesResponse? categoriesResponse;
  final FilteredResponse? filteredResponse;
  final FilteredResponse? searchSuggestions;
  final WorkoutsResponse? workoutsResponse;

  const ExploreLoaded({
    required this.categoriesResponse,
    required this.filteredResponse,
    this.searchSuggestions,
    required this.workoutsResponse,
  });

  ExploreLoaded copyWith({
    CategoriesResponse? categoriesResponse,
    FilteredResponse? filteredResponse,
    FilteredResponse? searchSuggestions,
    WorkoutsResponse? workoutsResponse,
  }) {
    return ExploreLoaded(
      categoriesResponse: categoriesResponse ?? this.categoriesResponse,
      filteredResponse: filteredResponse ?? this.filteredResponse,
      searchSuggestions: searchSuggestions ?? this.searchSuggestions,
      workoutsResponse: workoutsResponse ?? this.workoutsResponse,
    );
  }

  @override
  List<Object> get props => [
        categoriesResponse ?? '',
        filteredResponse ?? '',
        searchSuggestions ?? '',
        workoutsResponse ?? '',
      ];
}

final class ExploreError extends ExploreState {
  final String message;

  const ExploreError(this.message);

  @override
  List<Object> get props => [message];
}
