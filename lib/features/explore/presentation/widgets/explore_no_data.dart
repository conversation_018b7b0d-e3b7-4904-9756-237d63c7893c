import 'package:auto_route/auto_route.dart';
import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:gotcha_mfg_app/config/router/app_router.gr.dart';
import 'package:gotcha_mfg_app/core/extensions/app_text_theme_extension.dart';

class ExploreNoData extends StatelessWidget {
  final TextTheme textTheme;
  final String val;

  const ExploreNoData({
    super.key,
    required this.textTheme,
    required this.val,
  });

  @override
  Widget build(BuildContext context) {
    return Center(
      child: SizedBox(
        width: MediaQuery.sizeOf(context).width * .6,
        child: RichText(
          text: TextSpan(
            style: textTheme.bodyEmphasis.copyWith(),
            children: [
              TextSpan(
                  text:
                      "No results for '$val'. \nIf you think we're missing something important please "),
              TextSpan(
                recognizer: TapGestureRecognizer()
                  ..onTap = () {
                    context.pushRoute(const FeedBackRoute());
                  },
                text: 'give us feedback',
                style: textTheme.bodyEmphasis.copyWith(
                  color: Colors
                      .red, // or your specific red color like AppColors.red
                ),
              ),
              const TextSpan(text: '.'),
            ],
          ),
          textAlign: TextAlign.center,
        ),
      ),
    );
  }
}
