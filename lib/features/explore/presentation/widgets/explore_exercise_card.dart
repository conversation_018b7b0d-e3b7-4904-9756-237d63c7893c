// import 'package:flutter/material.dart';
// import 'package:gap/gap.dart';
// import 'package:gotcha_mfg_app/config/theme/app_assets.dart';
// import 'package:gotcha_mfg_app/config/theme/app_colors.dart';
// import 'package:gotcha_mfg_app/core/extensions/app_text_theme_extension.dart';

// class ExploreExerciseCard extends StatelessWidget {
//   const ExploreExerciseCard({
//     required this.title,
//     required this.subtitle,
//     required this.duration,
//     required this.tag,
//     this.onTap,
//     super.key,
//   });

//   final String title;
//   final String subtitle;
//   final String duration;
//   final String tag;
//   final VoidCallback? onTap;

//   @override
//   Widget build(BuildContext context) {
//     final textTheme = Theme.of(context).textTheme;
//     return GestureDetector(
//       onTap: onTap,
//       child: Card(
//         elevation: 0,
//         shape: RoundedRectangleBorder(
//           borderRadius: BorderRadius.circular(24),
//         ),
//         child: Container(
//           height: 196,
//           decoration: BoxDecoration(
//             borderRadius: BorderRadius.circular(24),
//             image: const DecorationImage(
//               image: AssetImage(
//                 AppAssets.g4lIcon,
//               ), // Add your background image
//               fit: BoxFit.cover,
//             ),
//           ),
//           child: Stack(
//             children: [
//               Container(
//                 decoration: BoxDecoration(
//                   borderRadius: BorderRadius.circular(24),
//                   gradient: LinearGradient(
//                     begin: Alignment.topCenter,
//                     end: Alignment.bottomCenter,
//                     colors: [
//                       Colors.transparent,
//                       Colors.transparent,
//                       Colors.black.withOpacity(0.4),
//                       Colors.black.withOpacity(0.8),
//                     ],
//                   ),
//                 ),
//               ),
//               Padding(
//                 padding:
//                     const EdgeInsets.symmetric(vertical: 16, horizontal: 24),
//                 child: Column(
//                   crossAxisAlignment: CrossAxisAlignment.start,
//                   children: [
//                     Container(
//                       padding: const EdgeInsets.symmetric(
//                         horizontal: 12,
//                         vertical: 6,
//                       ),
//                       decoration: BoxDecoration(
//                         color: AppColors.lightRed,
//                         borderRadius: BorderRadius.circular(16),
//                       ),
//                       child: Text(
//                         tag,
//                         style: textTheme.ralewaySemiBold.copyWith(
//                           color: AppColors.navy,
//                           fontSize: 10,
//                         ),
//                       ),
//                     ),
//                     const Spacer(),
//                     Text(
//                       title,
//                       style: textTheme.gothamBold.copyWith(
//                         color: Colors.white,
//                         fontSize: 16,
//                       ),
//                     ),
//                     Row(
//                       children: [
//                         Text(
//                           subtitle,
//                           style: textTheme.ralewayRegular.copyWith(
//                             color: Colors.white,
//                             fontSize: 14,
//                           ),
//                         ),
//                       ],
//                     ),
//                     Row(
//                       children: [
//                         const Icon(
//                           Icons.access_time,
//                           size: 16,
//                           color: Colors.white,
//                         ),
//                         const Gap(4),
//                         Text(
//                           duration,
//                           style: textTheme.ralewayRegular.copyWith(
//                             color: Colors.white,
//                             fontSize: 14,
//                           ),
//                         ),
//                         const Spacer(),
//                         Container(
//                           padding: const EdgeInsets.all(4),
//                           decoration: const BoxDecoration(
//                             color: AppColors.coral,
//                             shape: BoxShape.circle,
//                           ),
//                           child: Image.asset(
//                             AppAssets.cornerArrow,
//                             width: 24,
//                           ),
//                         ),
//                       ],
//                     ),
//                   ],
//                 ),
//               ),
//             ],
//           ),
//         ),
//       ),
//     );
//   }
// }
