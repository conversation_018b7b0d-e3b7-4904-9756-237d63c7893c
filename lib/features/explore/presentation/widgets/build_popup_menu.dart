import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:gotcha_mfg_app/config/theme/app_colors.dart';
import 'package:gotcha_mfg_app/core/extensions/app_text_theme_extension.dart';
import 'package:gotcha_mfg_app/features/favourites/data/models/not_interest_model.dart';

import '../../../favourites/data/models/favourite_request_model.dart';
import '../../../favourites/presentation/bloc/favourite/favourites_cubit.dart';

Widget buildPopupMenuButton({
  required BuildContext context,
  required String id,
  required bool isFavourite,
  required bool isExercise,
  required String url,
}) {
  return PopupMenuButton<String>(
    elevation: 0,
    color: Colors.white,
    shape: RoundedRectangleBorder(
      borderRadius: BorderRadius.circular(16),
      side: const BorderSide(
        color: AppColors.midBlue,
        width: 1.5,
      ),
    ),
    child: Container(
      height: 36,
      width: 48,
      alignment: Alignment.centerRight,
      child: const Icon(
        Icons.more_vert,
        size: 20,
      ),
    ),
    onSelected: (value) {
      if (value == 'favourite') {
        FavouriteRequest request = FavouriteRequest(
          id: id,
          isExercise: isExercise,
        );
        context.read<FavouritesCubit>().toggleFavorites(request);
      }
      if (value == 'share') {
        // Share.share(url);
      }
      if (value == 'not_interested') {
        NotInterestRequestModel request = NotInterestRequestModel(
          activityId: id,
          type: isExercise ? 'exercise' : 'workout',
        );
        context.read<FavouritesCubit>().notInterested(request);
        // SnackBarService.info(
        //   context: context,
        //   message: 'Coming soon',
        // );
      }
    },
    itemBuilder: (BuildContext context) => [
      PopupMenuItem<String>(
        value: 'favourite',
        child: Text(
          isFavourite ? 'Unfavourite' : 'Add to favourites',
          style: Theme.of(context).textTheme.ralewayMedium.copyWith(
                fontSize: 14,
                color: AppColors.navy,
              ),
        ),
      ),
      // PopupMenuItem<String>(
      //   value: 'share',
      //   child: Text(
      //     'Share',
      //     style: Theme.of(context).textTheme.ralewayMedium.copyWith(
      //           fontSize: 14,
      //           color: AppColors.navy,
      //         ),
      //   ),
      // ),
      PopupMenuItem<String>(
        value: 'not_interested',
        child: Text(
          'Not interested',
          style: Theme.of(context).textTheme.ralewayMedium.copyWith(
                fontSize: 14,
                color: AppColors.coral,
              ),
        ),
      ),
    ],
  );
}
