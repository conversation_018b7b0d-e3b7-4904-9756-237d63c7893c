// import 'package:flutter/material.dart';

// void _showPopupMenu(BuildContext context) {
//   showMenu(
//     context: context,
//     position: const RelativeRect.fromLTRB(
//         100, 100, 100, 100), // Adjust position as needed
//     items: [
//       const PopupMenuItem<String>(
//         value: 'favourite1',
//         child: Text('Favourite 1'),
//       ),
//       const PopupMenuItem<String>(
//         value: 'favourite2',
//         child: Text('Favourite 2'),
//       ),
//     ],
//   ).then((value) {
//     if (value != null) {
//       // Handle the selected value
//       print('Selected: $value');
//       // You can add your logic here based on the selected option
//     }
//   });
// }
