// ApiCallNameUseCase.dart

import '../../../../core/usecase/usecase.dart';
import '../../../../shared/models/result.dart';
import '../../data/models/add_gym_poll.dart';
import '../../data/models/gym_poll_common_response.dart';
import '../repositories/gym_poll_repository.dart';

class AddGymPollUseCase
    implements UseCase<Result<GymPollCommonResponse>, AddGymPoll> {
  AddGymPollUseCase(this._repository);

  final PollRepository _repository;

  @override
  Future<Result<GymPollCommonResponse>> call(AddGymPoll params) async {
    return _repository.addGymPoll(params);
  }
}
