// ApiCallNameUseCase.dart

import '../../../../core/usecase/usecase.dart';
import '../../../../shared/models/result.dart';
import '../../data/models/gym_poll_response.dart';
import '../repositories/gym_poll_repository.dart';

class GetGymPollMessageUseCase
    implements UseCase<Result<GymPollResponse>, NoParams> {
  GetGymPollMessageUseCase(this._repository);

  final PollRepository _repository;

  @override
  Future<Result<GymPollResponse>> call(NoParams params) async {
    return _repository.getGymPollMessage();
  }
}
