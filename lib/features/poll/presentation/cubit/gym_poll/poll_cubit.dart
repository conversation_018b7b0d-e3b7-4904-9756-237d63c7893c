// FeatureCubit.dart
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:gotcha_mfg_app/core/usecase/usecase.dart';

import '../../../data/models/add_gym_poll.dart';
import '../../../domain/usecases/add_gym_poll_usecase.dart';
import '../../../domain/usecases/gym_poll_usecase.dart';
import 'poll_state.dart';

class PollCubit extends Cubit<PollState> {
  PollCubit(this._getGymPollMessageUseCase, this._addGymPollUseCase)
      : super(PollInitial());

  final GetGymPollMessageUseCase _getGymPollMessageUseCase;
  final AddGymPollUseCase _addGymPollUseCase;

  Future<void> getGymPollMessage() async {
    emit(PollLoading());
    final result = await _getGymPollMessageUseCase.call(NoParams());

    if (result.isSuccess) {
      emit(PollLoaded(result.data!));
    } else {
      emit(PollError(result.error!));
    }
  }

  Future<void> addGymPoll(AddGymPoll request) async {
    // emit(PollLoading());
    final result = await _addGymPollUseCase.call(request);

    if (result.isSuccess) {
      emit(PollAddSuccess(result.data!));
    } else {
      emit(PollError(result.error!));
    }
  }
}
