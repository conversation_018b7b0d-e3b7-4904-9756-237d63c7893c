// PollState.dart

import '../../../data/models/gym_poll_common_response.dart';
import '../../../data/models/gym_poll_response.dart';

abstract class PollState {}

class PollInitial extends PollState {}

class PollLoading extends PollState {}

class PollLoaded extends PollState {
  PollLoaded(this.gymPollResponse);
  final GymPollResponse gymPollResponse;
}

class PollAddSuccess extends PollState {
  PollAddSuccess(this.gymPollResponse);
  final GymPollCommonResponse gymPollResponse;
}

class PollError extends PollState {
  PollError(this.message);
  final String message;
}
