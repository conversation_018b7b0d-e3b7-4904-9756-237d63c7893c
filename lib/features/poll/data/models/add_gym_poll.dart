import 'dart:convert';

class AddGymPoll {
  final String? message;

  AddGymPoll({
    this.message,
  });

  AddGymPoll copyWith({
    String? message,
  }) =>
      AddGymPoll(
        message: message ?? this.message,
      );

  factory AddGymPoll.fromRawJson(String str) =>
      AddGymPoll.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory AddGymPoll.fromJson(Map<String, dynamic> json) => AddGymPoll(
        message: json["message"],
      );

  Map<String, dynamic> toJson() => {
        "message": message,
      };
}
