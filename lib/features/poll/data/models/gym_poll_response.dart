import 'dart:convert';

class GymPollResponse {
  final String? message;
  final String? status;
  final Data? data;

  GymPollResponse({
    this.message,
    this.status,
    this.data,
  });

  GymPollResponse copyWith({
    String? message,
    String? status,
    Data? data,
  }) =>
      GymPollResponse(
        message: message ?? this.message,
        status: status ?? this.status,
        data: data ?? this.data,
      );

  factory GymPollResponse.fromRawJson(String str) =>
      GymPollResponse.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory GymPollResponse.fromJson(Map<String, dynamic> json) =>
      GymPollResponse(
        message: json["message"],
        status: json["status"],
        data: json["data"] == null ? null : Data.fromJson(json["data"]),
      );

  Map<String, dynamic> toJson() => {
        "message": message,
        "status": status,
        "data": data?.toJson(),
      };
}

class Data {
  final String? message;
  final Sender? sender;

  Data({
    this.message,
    this.sender,
  });

  Data copyWith({
    String? message,
    Sender? sender,
  }) =>
      Data(
        message: message ?? this.message,
        sender: sender ?? this.sender,
      );

  factory Data.fromRawJson(String str) => Data.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory Data.fromJson(Map<String, dynamic> json) => Data(
        message: json["message"],
        sender: json["sender"] == null ? null : Sender.fromJson(json["sender"]),
      );

  Map<String, dynamic> toJson() => {
        "message": message,
        "sender": sender?.toJson(),
      };
}

class Sender {
  final dynamic firstName;
  final dynamic lastName;

  Sender({
    this.firstName,
    this.lastName,
  });

  Sender copyWith({
    dynamic firstName,
    dynamic lastName,
  }) =>
      Sender(
        firstName: firstName ?? this.firstName,
        lastName: lastName ?? this.lastName,
      );

  factory Sender.fromRawJson(String str) => Sender.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory Sender.fromJson(Map<String, dynamic> json) => Sender(
        firstName: json["first_name"],
        lastName: json["last_name"],
      );

  Map<String, dynamic> toJson() => {
        "first_name": firstName,
        "last_name": lastName,
      };
}
