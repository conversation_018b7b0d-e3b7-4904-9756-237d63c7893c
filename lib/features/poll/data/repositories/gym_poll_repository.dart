// FeatureRepositoryImpl.dart

import '../../../../shared/models/result.dart';
import '../../domain/repositories/gym_poll_repository.dart';
import '../data_sources/gym_poll_datasource.dart';
import '../models/add_gym_poll.dart';
import '../models/gym_poll_common_response.dart';
import '../models/gym_poll_response.dart';

class PollRepositoryImpl implements PollRepository {
  PollRepositoryImpl(this._remoteDataSource);

  final PollRemoteDataSource _remoteDataSource;

  @override
  Future<Result<GymPollResponse>> getGymPollMessage() async {
    return await _remoteDataSource.getGymPollMessage();
  }

  @override
  Future<Result<GymPollCommonResponse>> addGymPoll(AddGymPoll request) async {
    return await _remoteDataSource.addGymPoll(request);
  }
}
