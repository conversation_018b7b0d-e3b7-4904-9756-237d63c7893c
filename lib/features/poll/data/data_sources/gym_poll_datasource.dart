// FeatureRemoteDataSource.dart
import 'package:dio/dio.dart';
import 'package:firebase_crashlytics/firebase_crashlytics.dart';

import '../../../../shared/models/result.dart';
import '../models/add_gym_poll.dart';
import '../models/gym_poll_common_response.dart';
import '../models/gym_poll_response.dart';

abstract class PollRemoteDataSource {
  Future<Result<GymPollResponse>> getGymPollMessage();
  Future<Result<GymPollCommonResponse>> addGymPoll(AddGymPoll request);
}

// FeatureRemoteDataSourceImpl.dart

class PollRemoteDataSourceImpl implements PollRemoteDataSource {
  PollRemoteDataSourceImpl(this._dio);

  final Dio _dio;

  @override
  Future<Result<GymPollResponse>> getGymPollMessage() async {
    try {
      final response = await _dio.get('/app/gym/poll/message');

      if (response.statusCode == 200) {
        final data = response.data;
        final gymPollResponse = GymPollResponse.fromJson(data);
        return Result.success(gymPollResponse);
      } else {
        return Result.failure(
            '${response.statusCode} - ${response.data['message']}');
      }
    } catch (e) {
      FirebaseCrashlytics.instance.recordError(e, StackTrace.current);
      return Result.failure(
          'An error occurred during getting gym poll message: ${e.toString()}');
    }
  }

  @override
  Future<Result<GymPollCommonResponse>> addGymPoll(AddGymPoll request) async {
    try {
      final response = await _dio.post('/app/gym/poll', data: request.toJson());

      if (response.statusCode == 200) {
        final data = response.data;
        final gymPollResponse = GymPollCommonResponse.fromJson(data);
        return Result.success(gymPollResponse);
      } else {
        return Result.failure(
            '${response.statusCode} - ${response.data['message']}');
      }
    } catch (e) {
      FirebaseCrashlytics.instance.recordError(e, StackTrace.current);
      return Result.failure(
          'An error occurred during adding gym poll: ${e.toString()}');
    }
  }
}
