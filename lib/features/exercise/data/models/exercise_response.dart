import 'dart:convert';

class ExerciseResponse {
  final String? message;
  final String? status;
  final Data? data;

  ExerciseResponse({
    this.message,
    this.status,
    this.data,
  });

  ExerciseResponse copyWith({
    String? message,
    String? status,
    Data? data,
  }) =>
      ExerciseResponse(
        message: message ?? this.message,
        status: status ?? this.status,
        data: data ?? this.data,
      );

  factory ExerciseResponse.fromRawJson(String str) =>
      ExerciseResponse.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory ExerciseResponse.fromJson(Map<String, dynamic> json) =>
      ExerciseResponse(
        message: json["message"],
        status: json["status"],
        data: json["data"] == null ? null : Data.fromJson(json["data"]),
      );

  Map<String, dynamic> toJson() => {
        "message": message,
        "status": status,
        "data": data?.toJson(),
      };
}

class Data {
  final String? id;
  final String? mediaType;
  final String? title;
  final String? mediaUrl;
  final String? thumbnailUrl;
  final String? mediaDuration;
  final List<String>? text;
  final String? protectiveFactors;
  final String? protectiveFactorSkills;
  final int? potentialImpacts;
  final int? difficultyLevel;
  final String? evidenceSources;
  final String? originalContributors;
  final String? exerciseType;
  final bool? isForWorkouts;
  final bool? isDeleted;
  final DateTime? createdAt;
  final DateTime? updatedAt;
  final List<Category>? categories;
  final List<Category>? emotions;
  final List<Category>? feelings;
  final List<Category>? factors;
  final bool? isFavorite;
  final List<dynamic>? demographics;
  final List<ReflectionQuestion>? reflectionQuestions;
  final String? dailyExerciseId;
  final bool? isCompleted;
  final dynamic completedAt;
  final String? timeSpent;
  final int? feedbackPercentage;
  final String? subTitle;
  final String? cardTitle;
  final String? cardBody;

  Data({
    this.subTitle,
    this.cardTitle,
    this.cardBody,
    this.id,
    this.mediaType,
    this.title,
    this.mediaUrl,
    this.thumbnailUrl,
    this.mediaDuration,
    this.text,
    this.protectiveFactors,
    this.protectiveFactorSkills,
    this.potentialImpacts,
    this.difficultyLevel,
    this.evidenceSources,
    this.originalContributors,
    this.exerciseType,
    this.isForWorkouts,
    this.isDeleted,
    this.createdAt,
    this.updatedAt,
    this.categories,
    this.emotions,
    this.feelings,
    this.factors,
    this.isFavorite,
    this.demographics,
    this.reflectionQuestions,
    this.dailyExerciseId,
    this.isCompleted,
    this.completedAt,
    this.timeSpent,
    this.feedbackPercentage,
  });

  Data copyWith({
    String? id,
    String? mediaType,
    String? title,
    String? mediaUrl,
    String? thumbnailUrl,
    String? mediaDuration,
    List<String>? text,
    String? protectiveFactors,
    String? protectiveFactorSkills,
    int? potentialImpacts,
    int? difficultyLevel,
    String? evidenceSources,
    String? originalContributors,
    String? exerciseType,
    bool? isForWorkouts,
    bool? isDeleted,
    DateTime? createdAt,
    DateTime? updatedAt,
    List<Category>? categories,
    List<Category>? emotions,
    List<Category>? feelings,
    List<Category>? factors,
    bool? isFavorite,
    List<dynamic>? demographics,
    List<ReflectionQuestion>? reflectionQuestions,
    String? dailyExerciseId,
    bool? isCompleted,
    dynamic completedAt,
    String? timeSpent,
    int? feedbackPercentage,
    String? subTitle,
    String? cardTitle,
    String? cardBody,
  }) =>
      Data(
        id: id ?? this.id,
        mediaType: mediaType ?? this.mediaType,
        title: title ?? this.title,
        mediaUrl: mediaUrl ?? this.mediaUrl,
        thumbnailUrl: thumbnailUrl ?? this.thumbnailUrl,
        mediaDuration: mediaDuration ?? this.mediaDuration,
        text: text ?? this.text,
        protectiveFactors: protectiveFactors ?? this.protectiveFactors,
        protectiveFactorSkills:
            protectiveFactorSkills ?? this.protectiveFactorSkills,
        potentialImpacts: potentialImpacts ?? this.potentialImpacts,
        difficultyLevel: difficultyLevel ?? this.difficultyLevel,
        evidenceSources: evidenceSources ?? this.evidenceSources,
        originalContributors: originalContributors ?? this.originalContributors,
        exerciseType: exerciseType ?? this.exerciseType,
        isForWorkouts: isForWorkouts ?? this.isForWorkouts,
        isDeleted: isDeleted ?? this.isDeleted,
        createdAt: createdAt ?? this.createdAt,
        updatedAt: updatedAt ?? this.updatedAt,
        categories: categories ?? this.categories,
        emotions: emotions ?? this.emotions,
        feelings: feelings ?? this.feelings,
        factors: factors ?? this.factors,
        isFavorite: isFavorite ?? this.isFavorite,
        demographics: demographics ?? this.demographics,
        reflectionQuestions: reflectionQuestions ?? this.reflectionQuestions,
        dailyExerciseId: dailyExerciseId ?? this.dailyExerciseId,
        isCompleted: isCompleted ?? this.isCompleted,
        completedAt: completedAt ?? this.completedAt,
        timeSpent: timeSpent ?? this.timeSpent,
        feedbackPercentage: feedbackPercentage ?? this.feedbackPercentage,
        subTitle: subTitle ?? this.subTitle,
        cardTitle: cardTitle ?? this.cardTitle,
        cardBody: cardBody ?? this.cardBody,
      );

  factory Data.fromRawJson(String str) => Data.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory Data.fromJson(Map<String, dynamic> json) => Data(
        id: json["id"],
        mediaType: json["media_type"],
        title: json["title"],
        mediaUrl: json["media_url"],
        thumbnailUrl: json["thumbnail_url"],
        mediaDuration: json["media_duration"],
        text: json["text"] == null
            ? []
            : List<String>.from(json["text"]!.map((x) => x)),
        protectiveFactors: json["protective_factors"],
        protectiveFactorSkills: json["protective_factor_skills"],
        potentialImpacts: json["potential_impacts"],
        difficultyLevel: json["difficulty_level"],
        evidenceSources: json["evidence_sources"],
        originalContributors: json["original_contributors"],
        exerciseType: json["exercise_type"],
        isForWorkouts: json["is_for_workouts"],
        isDeleted: json["is_deleted"],
        createdAt: json["created_at"] == null
            ? null
            : DateTime.parse(json["created_at"]),
        updatedAt: json["updated_at"] == null
            ? null
            : DateTime.parse(json["updated_at"]),
        categories: json["categories"] == null
            ? []
            : List<Category>.from(
                json["categories"]!.map((x) => Category.fromJson(x))),
        emotions: json["emotions"] == null
            ? []
            : List<Category>.from(
                json["emotions"]!.map((x) => Category.fromJson(x))),
        feelings: json["feelings"] == null
            ? []
            : List<Category>.from(
                json["feelings"]!.map((x) => Category.fromJson(x))),
        factors: json["factors"] == null
            ? []
            : List<Category>.from(
                json["factors"]!.map((x) => Category.fromJson(x))),
        isFavorite: json["is_favorite"],
        demographics: json["demographics"] == null
            ? []
            : List<dynamic>.from(json["demographics"]!.map((x) => x)),
        reflectionQuestions: json["reflection_questions"] == null
            ? []
            : List<ReflectionQuestion>.from(json["reflection_questions"]!
                .map((x) => ReflectionQuestion.fromJson(x))),
        dailyExerciseId: json["daily_exercise_id"],
        isCompleted: json["is_completed"],
        completedAt: json["completed_at"],
        timeSpent: json["time_spent"],
        feedbackPercentage: json["feedback_percentage"],
        subTitle: json["sub_title"],
        cardTitle: json["card_title"],
        cardBody: json["card_body"],
      );

  Map<String, dynamic> toJson() => {
        "id": id,
        "media_type": mediaType,
        "title": title,
        "media_url": mediaUrl,
        "thumbnail_url": thumbnailUrl,
        "media_duration": mediaDuration,
        "text": text == null ? [] : List<dynamic>.from(text!.map((x) => x)),
        "protective_factors": protectiveFactors,
        "protective_factor_skills": protectiveFactorSkills,
        "potential_impacts": potentialImpacts,
        "difficulty_level": difficultyLevel,
        "evidence_sources": evidenceSources,
        "original_contributors": originalContributors,
        "exercise_type": exerciseType,
        "is_for_workouts": isForWorkouts,
        "is_deleted": isDeleted,
        "created_at": createdAt?.toIso8601String(),
        "updated_at": updatedAt?.toIso8601String(),
        "categories": categories == null
            ? []
            : List<dynamic>.from(categories!.map((x) => x.toJson())),
        "emotions": emotions == null
            ? []
            : List<dynamic>.from(emotions!.map((x) => x.toJson())),
        "feelings": feelings == null
            ? []
            : List<dynamic>.from(feelings!.map((x) => x.toJson())),
        "factors": factors == null
            ? []
            : List<dynamic>.from(factors!.map((x) => x.toJson())),
        "is_favorite": isFavorite,
        "demographics": demographics == null
            ? []
            : List<dynamic>.from(demographics!.map((x) => x)),
        "reflection_questions": reflectionQuestions == null
            ? []
            : List<dynamic>.from(reflectionQuestions!.map((x) => x.toJson())),
        "daily_exercise_id": dailyExerciseId,
        "is_completed": isCompleted,
        "completed_at": completedAt,
        "time_spent": timeSpent,
        "feedback_percentage": feedbackPercentage,
        "sub_title": subTitle,
        "card_title": cardTitle,
        "card_body": cardBody,
      };
}

class Category {
  final String? id;
  final String? name;

  Category({
    this.id,
    this.name,
  });

  Category copyWith({
    String? id,
    String? name,
  }) =>
      Category(
        id: id ?? this.id,
        name: name ?? this.name,
      );

  factory Category.fromRawJson(String str) =>
      Category.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory Category.fromJson(Map<String, dynamic> json) => Category(
        id: json["id"],
        name: json["name"],
      );

  Map<String, dynamic> toJson() => {
        "id": id,
        "name": name,
      };
}

class ReflectionQuestion {
  final String? id;
  final String? questionText;
  final bool? isMultiChoice;
  final List<Choice>? choices;

  ReflectionQuestion({
    this.id,
    this.questionText,
    this.isMultiChoice,
    this.choices,
  });

  ReflectionQuestion copyWith({
    String? id,
    String? questionText,
    bool? isMultiChoice,
    List<Choice>? choices,
  }) =>
      ReflectionQuestion(
        id: id ?? this.id,
        questionText: questionText ?? this.questionText,
        isMultiChoice: isMultiChoice ?? this.isMultiChoice,
        choices: choices ?? this.choices,
      );

  factory ReflectionQuestion.fromRawJson(String str) =>
      ReflectionQuestion.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory ReflectionQuestion.fromJson(Map<String, dynamic> json) =>
      ReflectionQuestion(
        id: json["id"],
        questionText: json["question_text"],
        isMultiChoice: json["is_multi_choice"],
        choices: json["choices"] == null
            ? []
            : List<Choice>.from(
                json["choices"]!.map((x) => Choice.fromJson(x))),
      );

  Map<String, dynamic> toJson() => {
        "id": id,
        "question_text": questionText,
        "is_multi_choice": isMultiChoice,
        "choices": choices == null
            ? []
            : List<dynamic>.from(choices!.map((x) => x.toJson())),
      };
}

class Choice {
  final String? id;
  final String? choiceText;

  Choice({
    this.id,
    this.choiceText,
  });

  Choice copyWith({
    String? id,
    String? choiceText,
  }) =>
      Choice(
        id: id ?? this.id,
        choiceText: choiceText ?? this.choiceText,
      );

  factory Choice.fromRawJson(String str) => Choice.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory Choice.fromJson(Map<String, dynamic> json) => Choice(
        id: json["id"],
        choiceText: json["choice_text"],
      );

  Map<String, dynamic> toJson() => {
        "id": id,
        "choice_text": choiceText,
      };
}
