import 'dart:convert';

class NewExerciseResponse {
  String? message;
  String? status;
  Data? data;

  NewExerciseResponse({
    this.message,
    this.status,
    this.data,
  });

  factory NewExerciseResponse.fromRawJson(String str) =>
      NewExerciseResponse.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory NewExerciseResponse.fromJson(Map<String, dynamic> json) =>
      NewExerciseResponse(
        message: json["message"],
        status: json["status"],
        data: json["data"] == null ? null : Data.fromJson(json["data"]),
      );

  Map<String, dynamic> toJson() => {
        "message": message,
        "status": status,
        "data": data?.toJson(),
      };
}

class Data {
  String? id;
  String? title;
  String? thumbnailUrl;
  String? mediaDuration;
  List<String>? text;
  String? protectiveFactors;
  String? protectiveFactorSkills;
  int? potentialImpacts;
  int? difficultyLevel;
  String? evidenceSources;
  String? originalContributors;
  String? exerciseType;
  bool? isForWorkouts;
  DateTime? createdAt;
  DateTime? updatedAt;
  bool? isDeleted;
  bool? isPopular;
  dynamic message;
  dynamic ideaName;
  List<Media>? media;
  // bool? isRedirectToVillage;
  List<Category>? categories;
  List<Category>? emotions;
  List<Category>? feelings;
  List<Category>? factors;
  bool? isFavorite;
  List<dynamic>? demographics;
  List<ReflectionQuestion>? reflectionQuestions;
  String? dailyExerciseId;
  bool? isCompleted;
  dynamic completedAt;
  String? timeSpent;
  int? feedbackPercentage;

  Data({
    this.id,
    this.title,
    this.thumbnailUrl,
    this.mediaDuration,
    this.text,
    this.protectiveFactors,
    this.protectiveFactorSkills,
    this.potentialImpacts,
    this.difficultyLevel,
    this.evidenceSources,
    this.originalContributors,
    this.exerciseType,
    this.isForWorkouts,
    this.createdAt,
    this.updatedAt,
    this.isDeleted,
    this.isPopular,
    this.message,
    this.ideaName,
    this.media,
    // this.isRedirectToVillage,
    this.categories,
    this.emotions,
    this.feelings,
    this.factors,
    this.isFavorite,
    this.demographics,
    this.reflectionQuestions,
    this.dailyExerciseId,
    this.isCompleted,
    this.completedAt,
    this.timeSpent,
    this.feedbackPercentage,
  });

  factory Data.fromRawJson(String str) => Data.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory Data.fromJson(Map<String, dynamic> json) => Data(
        id: json["id"],
        title: json["title"],
        thumbnailUrl: json["thumbnail_url"],
        mediaDuration: json["media_duration"],
        text: json["text"] == null
            ? []
            : List<String>.from(json["text"]!.map((x) => x)),
        protectiveFactors: json["protective_factors"],
        protectiveFactorSkills: json["protective_factor_skills"],
        potentialImpacts: json["potential_impacts"],
        difficultyLevel: json["difficulty_level"],
        evidenceSources: json["evidence_sources"],
        originalContributors: json["original_contributors"],
        exerciseType: json["exercise_type"],
        isForWorkouts: json["is_for_workouts"],
        createdAt: json["created_at"] == null
            ? null
            : DateTime.parse(json["created_at"]),
        updatedAt: json["updated_at"] == null
            ? null
            : DateTime.parse(json["updated_at"]),
        isDeleted: json["is_deleted"],
        isPopular: json["is_popular"],
        message: json["message"],
        ideaName: json["idea_name"],
        media: json["media"] == null
            ? []
            : List<Media>.from(json["media"]!.map((x) => Media.fromJson(x))),
        // isRedirectToVillage: json["is_redirect_to_village"],
        categories: json["categories"] == null
            ? []
            : List<Category>.from(
                json["categories"]!.map((x) => Category.fromJson(x))),
        emotions: json["emotions"] == null
            ? []
            : List<Category>.from(
                json["emotions"]!.map((x) => Category.fromJson(x))),
        feelings: json["feelings"] == null
            ? []
            : List<Category>.from(
                json["feelings"]!.map((x) => Category.fromJson(x))),
        factors: json["factors"] == null
            ? []
            : List<Category>.from(
                json["factors"]!.map((x) => Category.fromJson(x))),
        isFavorite: json["is_favorite"],
        demographics: json["demographics"] == null
            ? []
            : List<dynamic>.from(json["demographics"]!.map((x) => x)),
        reflectionQuestions: json["reflection_questions"] == null
            ? []
            : List<ReflectionQuestion>.from(json["reflection_questions"]!
                .map((x) => ReflectionQuestion.fromJson(x))),
        dailyExerciseId: json["daily_exercise_id"],
        isCompleted: json["is_completed"],
        completedAt: json["completed_at"],
        timeSpent: json["time_spent"],
        feedbackPercentage: json["feedback_percentage"],
      );

  Map<String, dynamic> toJson() => {
        "id": id,
        "title": title,
        "thumbnail_url": thumbnailUrl,
        "media_duration": mediaDuration,
        "text": text == null ? [] : List<dynamic>.from(text!.map((x) => x)),
        "protective_factors": protectiveFactors,
        "protective_factor_skills": protectiveFactorSkills,
        "potential_impacts": potentialImpacts,
        "difficulty_level": difficultyLevel,
        "evidence_sources": evidenceSources,
        "original_contributors": originalContributors,
        "exercise_type": exerciseType,
        "is_for_workouts": isForWorkouts,
        "created_at": createdAt?.toIso8601String(),
        "updated_at": updatedAt?.toIso8601String(),
        "is_deleted": isDeleted,
        "is_popular": isPopular,
        "message": message,
        "idea_name": ideaName,
        "media": media == null
            ? []
            : List<dynamic>.from(media!.map((x) => x.toJson())),
        // "is_redirect_to_village": isRedirectToVillage,
        "categories": categories == null
            ? []
            : List<dynamic>.from(categories!.map((x) => x.toJson())),
        "emotions": emotions == null
            ? []
            : List<dynamic>.from(emotions!.map((x) => x.toJson())),
        "feelings": feelings == null
            ? []
            : List<dynamic>.from(feelings!.map((x) => x.toJson())),
        "factors": factors == null
            ? []
            : List<dynamic>.from(factors!.map((x) => x.toJson())),
        "is_favorite": isFavorite,
        "demographics": demographics == null
            ? []
            : List<dynamic>.from(demographics!.map((x) => x)),
        "reflection_questions": reflectionQuestions == null
            ? []
            : List<dynamic>.from(reflectionQuestions!.map((x) => x.toJson())),
        "daily_exercise_id": dailyExerciseId,
        "is_completed": isCompleted,
        "completed_at": completedAt,
        "time_spent": timeSpent,
        "feedback_percentage": feedbackPercentage,
      };
}

class Category {
  String? id;
  String? name;

  Category({
    this.id,
    this.name,
  });

  factory Category.fromRawJson(String str) =>
      Category.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory Category.fromJson(Map<String, dynamic> json) => Category(
        id: json["id"],
        name: json["name"],
      );

  Map<String, dynamic> toJson() => {
        "id": id,
        "name": name,
      };
}

class Media {
  String? id;
  String? exerciseId;
  String? mediaType;
  String? mediaDuration;
  int? mediaOrder;
  List<String>? text;
  String? title;
  String? subtitle;
  String? body;
  String? mediaUrl;
  DateTime? createdAt;
  DateTime? updatedAt;

  Media({
    this.id,
    this.exerciseId,
    this.mediaType,
    this.mediaDuration,
    this.mediaOrder,
    this.text,
    this.title,
    this.subtitle,
    this.body,
    this.mediaUrl,
    this.createdAt,
    this.updatedAt,
  });

  factory Media.fromRawJson(String str) => Media.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory Media.fromJson(Map<String, dynamic> json) => Media(
        id: json["id"],
        exerciseId: json["exercise_id"],
        mediaType: json["media_type"],
        mediaDuration: json["media_duration"],
        mediaOrder: json["media_order"],
        text: json["text"] == null
            ? []
            : List<String>.from(json["text"]!.map((x) => x)),
        title: json["title"],
        subtitle: json["subtitle"],
        body: json["body"],
        mediaUrl: json["media_url"],
        createdAt: json["created_at"] == null
            ? null
            : DateTime.parse(json["created_at"]),
        updatedAt: json["updated_at"] == null
            ? null
            : DateTime.parse(json["updated_at"]),
      );

  Map<String, dynamic> toJson() => {
        "id": id,
        "exercise_id": exerciseId,
        "media_type": mediaType,
        "media_duration": mediaDuration,
        "media_order": mediaOrder,
        "text": text == null ? [] : List<dynamic>.from(text!.map((x) => x)),
        "title": title,
        "subtitle": subtitle,
        "body": body,
        "media_url": mediaUrl,
        "created_at": createdAt?.toIso8601String(),
        "updated_at": updatedAt?.toIso8601String(),
      };
}

class ReflectionQuestion {
  String? id;
  String? questionText;
  bool? isMultiChoice;
  List<Choice>? choices;

  ReflectionQuestion({
    this.id,
    this.questionText,
    this.isMultiChoice,
    this.choices,
  });

  factory ReflectionQuestion.fromRawJson(String str) =>
      ReflectionQuestion.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory ReflectionQuestion.fromJson(Map<String, dynamic> json) =>
      ReflectionQuestion(
        id: json["id"],
        questionText: json["question_text"],
        isMultiChoice: json["is_multi_choice"],
        choices: json["choices"] == null
            ? []
            : List<Choice>.from(
                json["choices"]!.map((x) => Choice.fromJson(x))),
      );

  Map<String, dynamic> toJson() => {
        "id": id,
        "question_text": questionText,
        "is_multi_choice": isMultiChoice,
        "choices": choices == null
            ? []
            : List<dynamic>.from(choices!.map((x) => x.toJson())),
      };
}

class Choice {
  String? id;
  String? choiceText;

  Choice({
    this.id,
    this.choiceText,
  });

  factory Choice.fromRawJson(String str) => Choice.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory Choice.fromJson(Map<String, dynamic> json) => Choice(
        id: json["id"],
        choiceText: json["choice_text"],
      );

  Map<String, dynamic> toJson() => {
        "id": id,
        "choice_text": choiceText,
      };
}
