import 'dart:convert';

class UpdateExerciseStatusRequest {
  bool? isCompleted;
  String? timeSpent;

  UpdateExerciseStatusRequest({
    this.isCompleted,
    this.timeSpent,
  });

  factory UpdateExerciseStatusRequest.fromRawJson(String str) =>
      UpdateExerciseStatusRequest.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory UpdateExerciseStatusRequest.fromJson(Map<String, dynamic> json) =>
      UpdateExerciseStatusRequest(
        isCompleted: json["isCompleted"],
        timeSpent: json["timeSpent"],
      );

  Map<String, dynamic> toJson() => {
        "isCompleted": isCompleted,
        "timeSpent": timeSpent,
      };
}
