import 'dart:convert';

class ReflectionRequest {
  List<Answer>? answers;

  ReflectionRequest({
    this.answers,
  });

  factory ReflectionRequest.fromRawJson(String str) =>
      ReflectionRequest.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory ReflectionRequest.fromJson(Map<String, dynamic> json) =>
      ReflectionRequest(
        answers: json["answers"] == null
            ? []
            : List<Answer>.from(
                json["answers"]!.map((x) => Answer.fromJson(x))),
      );

  Map<String, dynamic> toJson() => {
        "answers": answers == null
            ? []
            : List<dynamic>.from(answers!.map((x) => x.toJson())),
      };
}

class Answer {
  String? questionId;
  String? answerText;
  String? selectedChoiceId;

  Answer({
    this.questionId,
    this.answerText,
    this.selectedChoiceId,
  });

  factory Answer.fromRawJson(String str) => Answer.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory Answer.fromJson(Map<String, dynamic> json) => Answer(
        questionId: json["question_id"],
        answerText: json["answer_text"],
        selectedChoiceId: json["selected_choice_id"],
      );

  Map<String, dynamic> toJson() => {
        "question_id": questionId,
        "answer_text": answerText,
        "selected_choice_id": selectedChoiceId,
      };
}
