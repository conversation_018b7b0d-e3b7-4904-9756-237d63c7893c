import 'package:gotcha_mfg_app/core/usecase/usecase.dart';
import 'package:gotcha_mfg_app/features/exercise/domain/repositories/exercise_repository.dart';
import 'package:gotcha_mfg_app/shared/models/result.dart';

import 'new_exercise_response.dart';

class GetDetailExerciseUsecase
    implements UseCase<Result<NewExerciseResponse>, ExerciseDetailParams> {
  GetDetailExerciseUsecase(this._repository);

  final ExerciseRepository _repository;

  @override
  Future<Result<NewExerciseResponse>> call(ExerciseDetailParams params) async {
    return _repository.getExerciseDetail(params);
  }
}

class ExerciseDetailParams {
  final String id;
  final String seriesId;

  ExerciseDetailParams({
    required this.id,
    required this.seriesId,
  });
}
