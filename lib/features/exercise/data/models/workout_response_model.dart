import 'dart:convert';

class WorkoutResponseModel {
  String? message;
  String? status;
  Data? data;

  WorkoutResponseModel({
    this.message,
    this.status,
    this.data,
  });

  factory WorkoutResponseModel.fromRawJson(String str) =>
      WorkoutResponseModel.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory WorkoutResponseModel.fromJson(Map<String, dynamic> json) =>
      WorkoutResponseModel(
        message: json["message"],
        status: json["status"],
        data: json["data"] == null ? null : Data.fromJson(json["data"]),
      );

  Map<String, dynamic> toJson() => {
        "message": message,
        "status": status,
        "data": data?.toJson(),
      };
}

class Data {
  DataWorkout? workout;
  bool? hasAnyPreviousWorkoutActive;

  Data({
    this.workout,
    this.hasAnyPreviousWorkoutActive,
  });

  factory Data.fromRawJson(String str) => Data.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory Data.fromJson(Map<String, dynamic> json) => Data(
        workout: json["workout"] == null
            ? null
            : DataWorkout.fromJson(json["workout"]),
        hasAnyPreviousWorkoutActive: json["has_any_previous_workout_active"],
      );

  Map<String, dynamic> toJson() => {
        "workout": workout?.toJson(),
        "has_any_previous_workout_active": hasAnyPreviousWorkoutActive,
      };
}

class DataWorkout {
  String? seriesId;
  String? seriesTitle;
  String? seriesDescription;
  String? seriesDuration;
  String? seriesImageUrl;
  List<WorkoutElement>? workouts;

  DataWorkout({
    this.seriesId,
    this.seriesTitle,
    this.seriesDescription,
    this.seriesDuration,
    this.seriesImageUrl,
    this.workouts,
  });

  factory DataWorkout.fromRawJson(String str) =>
      DataWorkout.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory DataWorkout.fromJson(Map<String, dynamic> json) => DataWorkout(
        seriesId: json["series_id"],
        seriesTitle: json["series_title"],
        seriesDescription: json["series_description"],
        seriesDuration: json["series_duration"],
        seriesImageUrl: json["series_image_url"],
        workouts: json["workouts"] == null
            ? []
            : List<WorkoutElement>.from(
                json["workouts"]!.map((x) => WorkoutElement.fromJson(x))),
      );

  Map<String, dynamic> toJson() => {
        "series_id": seriesId,
        "series_title": seriesTitle,
        "series_description": seriesDescription,
        "series_duration": seriesDuration,
        "series_image_url": seriesImageUrl,
        "workouts": workouts == null
            ? []
            : List<dynamic>.from(workouts!.map((x) => x.toJson())),
      };
}

class WorkoutElement {
  String? workoutId;
  int? orderOfExercise;
  int? day;
  String? exerciseId;
  String? exerciseTitle;
  String? exerciseThumbnailUrl;
  List<String>? categories;
  bool? isCompleted;
  DateTime? completedAt;
  List<ExerciseMedia>? exerciseMedia;
  final String? exerciseMediaDuration;

  WorkoutElement({
    this.workoutId,
    this.orderOfExercise,
    this.day,
    this.exerciseId,
    this.exerciseTitle,
    this.exerciseThumbnailUrl,
    this.categories,
    this.isCompleted,
    this.completedAt,
    this.exerciseMedia,
    this.exerciseMediaDuration,
  });

  factory WorkoutElement.fromRawJson(String str) =>
      WorkoutElement.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory WorkoutElement.fromJson(Map<String, dynamic> json) => WorkoutElement(
        workoutId: json["workout_id"],
        orderOfExercise: json["order_of_exercise"],
        day: json["day"],
        exerciseId: json["exercise_id"],
        exerciseTitle: json["exercise_title"],
        exerciseThumbnailUrl: json["exercise_thumbnail_url"],
        categories: json["categories"] == null
            ? []
            : List<String>.from(json["categories"]!.map((x) => x)),
        isCompleted: json["is_completed"],
        completedAt: json["completed_at"] == null
            ? null
            : DateTime.parse(json["completed_at"]),
        exerciseMedia: json["exercise_media"] == null
            ? []
            : List<ExerciseMedia>.from(
                json["exercise_media"]!.map((x) => ExerciseMedia.fromJson(x))),
        exerciseMediaDuration: json["exercise_media_duration"],
      );

  Map<String, dynamic> toJson() => {
        "workout_id": workoutId,
        "order_of_exercise": orderOfExercise,
        "day": day,
        "exercise_id": exerciseId,
        "exercise_title": exerciseTitle,
        "exercise_thumbnail_url": exerciseThumbnailUrl,
        "categories": categories == null
            ? []
            : List<dynamic>.from(categories!.map((x) => x)),
        "is_completed": isCompleted,
        "completed_at": completedAt?.toIso8601String(),
        "exercise_media": exerciseMedia == null
            ? []
            : List<dynamic>.from(exerciseMedia!.map((x) => x.toJson())),
        "exercise_media_duration": exerciseMediaDuration,
      };
}

class ExerciseMedia {
  String? id;
  String? exerciseId;
  String? mediaType;
  dynamic mediaDuration;
  int? mediaOrder;
  List<String>? text;
  dynamic title;
  dynamic subtitle;
  dynamic body;
  String? mediaUrl;
  DateTime? createdAt;
  DateTime? updatedAt;

  ExerciseMedia({
    this.id,
    this.exerciseId,
    this.mediaType,
    this.mediaDuration,
    this.mediaOrder,
    this.text,
    this.title,
    this.subtitle,
    this.body,
    this.mediaUrl,
    this.createdAt,
    this.updatedAt,
  });

  factory ExerciseMedia.fromRawJson(String str) =>
      ExerciseMedia.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory ExerciseMedia.fromJson(Map<String, dynamic> json) => ExerciseMedia(
        id: json["id"],
        exerciseId: json["exercise_id"],
        mediaType: json["media_type"],
        mediaDuration: json["media_duration"],
        mediaOrder: json["media_order"],
        text: json["text"] == null
            ? []
            : List<String>.from(json["text"]!.map((x) => x)),
        title: json["title"],
        subtitle: json["subtitle"],
        body: json["body"],
        mediaUrl: json["media_url"],
        createdAt: json["created_at"] == null
            ? null
            : DateTime.parse(json["created_at"]),
        updatedAt: json["updated_at"] == null
            ? null
            : DateTime.parse(json["updated_at"]),
      );

  Map<String, dynamic> toJson() => {
        "id": id,
        "exercise_id": exerciseId,
        "media_type": mediaType,
        "media_duration": mediaDuration,
        "media_order": mediaOrder,
        "text": text == null ? [] : List<dynamic>.from(text!.map((x) => x)),
        "title": title,
        "subtitle": subtitle,
        "body": body,
        "media_url": mediaUrl,
        "created_at": createdAt?.toIso8601String(),
        "updated_at": updatedAt?.toIso8601String(),
      };
}
