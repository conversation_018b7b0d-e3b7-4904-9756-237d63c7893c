import 'package:dio/dio.dart';
import 'package:firebase_crashlytics/firebase_crashlytics.dart';
import 'package:gotcha_mfg_app/shared/models/generic_response.dart';
import 'package:gotcha_mfg_app/features/exercise/domain/usecases/get_workout_usecase.dart';
import 'package:gotcha_mfg_app/features/exercise/domain/usecases/update_status_use_case.dart';
import 'package:gotcha_mfg_app/shared/models/result.dart';

import '../../domain/usecases/update_series_use_case.dart';
import '../models/get_exercise_response.dart';
import '../../domain/usecases/post_exercise_use_case.dart';
import '../models/new_exercise_response.dart';
import '../models/post_exercise_response.dart';
import '../models/workout_response_model.dart';

abstract class ExerciseDataSource {
  Future<Result<NewExerciseResponse>> getExerciseDetail(
      ExerciseDetailParams params);
  Future<Result<PostExerciseResponse>> postExerciseDetail(
      PostExerciseDetailParams params);
  Future<Result<WorkoutResponseModel>> getWorkout(GetWorkoutParams params);
  Future<Result<GenericResponse>> updateExerciseStatus(
      UpdateStatusParams params);
  Future<Result<GenericResponse>> updateSeriesStatus(UpdateSeriesParams params);
}

class ExerciseDataSourceImpl implements ExerciseDataSource {
  final Dio _dio;

  ExerciseDataSourceImpl(this._dio);

  @override
  Future<Result<NewExerciseResponse>> getExerciseDetail(
      ExerciseDetailParams params) async {
    try {
      final response = await _dio
          .get('/app/exercise/${params.id}?seriesId=${params.seriesId}');
      if (response.statusCode == 200) {
        final data = response.data;
        final exerciseResponse = NewExerciseResponse.fromJson(data);
        return Result.success(exerciseResponse);
      } else {
        return Result.failure(
            '${response.statusCode} - ${response.data['message']}');
      }
    } catch (e) {
      FirebaseCrashlytics.instance.recordError(e, StackTrace.current);
      return Result.failure(
          'An error occurred during getting exercise details: ${e.toString()}');
    }
  }

  @override
  Future<Result<PostExerciseResponse>> postExerciseDetail(
      PostExerciseDetailParams params) async {
    toPayload() {
      return {
        "answers": params.ans.map((answer) => answer.toJson()).toList(),
      };
    }

    var param = toPayload();
    try {
      final response = await _dio.post(
          '/app/exercise/reflection/add/${params.exerciseId}',
          data: param);
      if (response.statusCode == 200) {
        final data = response.data;
        final exerciseResponse = PostExerciseResponse.fromJson(data);
        return Result.success(exerciseResponse);
      } else {
        return Result.failure(
            '${response.statusCode} - ${response.data['message']}');
      }
    } catch (e) {
      FirebaseCrashlytics.instance.recordError(e, StackTrace.current);
      return Result.failure(
          'An error occurred during posting reflection: ${e.toString()}');
    }
  }

  @override
  Future<Result<WorkoutResponseModel>> getWorkout(
      GetWorkoutParams params) async {
    try {
      final response = await _dio.get('/app/workout/${params.id}');
      if (response.statusCode == 200) {
        final data = response.data;
        final exerciseResponse = WorkoutResponseModel.fromJson(data);
        return Result.success(exerciseResponse);
      } else {
        return Result.failure(
            '${response.statusCode} - ${response.data['message']}');
      }
    } catch (e) {
      FirebaseCrashlytics.instance.recordError(e, StackTrace.current);
      return Result.failure(
          'An error occurred during getting workouts: ${e.toString()}');
    }
  }

  @override
  Future<Result<GenericResponse>> updateExerciseStatus(
      UpdateStatusParams params) async {
    try {
      final response = await _dio.put(
        '/app/exercise/viewed/status/${params.dailyExerciseId}',
        data: params.request.toJson(),
      );
      if (response.statusCode == 200) {
        final data = response.data;
        final updateStatusResponse = GenericResponse.fromJson(data);
        return Result.success(updateStatusResponse);
      } else {
        return Result.failure(
          '${response.statusCode} - ${response.data['message']}',
        );
      }
    } catch (e) {
      FirebaseCrashlytics.instance.recordError(e, StackTrace.current);
      return Result.failure(
        'An error occurred during updating exercise status: ${e.toString()}',
      );
    }
  }

  @override
  Future<Result<GenericResponse>> updateSeriesStatus(
      UpdateSeriesParams params) async {
    try {
      final response = await _dio.post(
          '/app/series/${params.seriesId}?is_completed=${params.isCompleted}&is_override=${params.isOverride}');
      if (response.statusCode == 200) {
        final data = response.data;
        final updateStatusResponse = GenericResponse.fromJson(data);
        return Result.success(updateStatusResponse);
      } else {
        return Result.failure(
          '${response.statusCode} - ${response.data['message']}',
        );
      }
    } catch (e) {
      FirebaseCrashlytics.instance.recordError(e, StackTrace.current);
      return Result.failure(
        'An error occurred during updating series status: ${e.toString()}',
      );
    }
  }
}
