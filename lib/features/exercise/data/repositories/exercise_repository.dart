import 'package:gotcha_mfg_app/features/exercise/data/data_sources/exercise_data_source.dart';
import 'package:gotcha_mfg_app/shared/models/generic_response.dart';
import 'package:gotcha_mfg_app/features/exercise/data/models/workout_response_model.dart';
import 'package:gotcha_mfg_app/features/exercise/domain/repositories/exercise_repository.dart';
import 'package:gotcha_mfg_app/features/exercise/data/models/get_exercise_response.dart';
import 'package:gotcha_mfg_app/features/exercise/domain/usecases/get_workout_usecase.dart';
import 'package:gotcha_mfg_app/features/exercise/domain/usecases/update_status_use_case.dart';
import 'package:gotcha_mfg_app/shared/models/result.dart';

import '../../domain/usecases/post_exercise_use_case.dart';
import '../../domain/usecases/update_series_use_case.dart';
import '../models/new_exercise_response.dart';
import '../models/post_exercise_response.dart';

class ExerciseRepositoryImpl extends ExerciseRepository {
  final ExerciseDataSource _dataSource;

  ExerciseRepositoryImpl(this._dataSource);

  @override
  Future<Result<NewExerciseResponse>> getExerciseDetail(
      ExerciseDetailParams params) async {
    return await _dataSource.getExerciseDetail(params);
  }

  @override
  Future<Result<PostExerciseResponse>> postExerciseDetail(
      PostExerciseDetailParams params) async {
    return await _dataSource.postExerciseDetail(params);
  }

  @override
  Future<Result<WorkoutResponseModel>> getWorkout(
      GetWorkoutParams params) async {
    return await _dataSource.getWorkout(params);
  }

  @override
  Future<Result<GenericResponse>> updateExerciseStatus(
      UpdateStatusParams params) async {
    return await _dataSource.updateExerciseStatus(params);
  }

  @override
  Future<Result<GenericResponse>> updateSeriesStatus(
      UpdateSeriesParams params) async {
    return await _dataSource.updateSeriesStatus(params);
  }
}
