import 'package:gotcha_mfg_app/core/usecase/usecase.dart';
import 'package:gotcha_mfg_app/features/exercise/data/models/reflection_request.dart';
import 'package:gotcha_mfg_app/features/exercise/domain/repositories/exercise_repository.dart';
import 'package:gotcha_mfg_app/shared/models/result.dart';

import '../../data/models/post_exercise_response.dart';

class PostDetailExerciseUsecase
    implements UseCase<Result<PostExerciseResponse>, PostExerciseDetailParams> {
  PostDetailExerciseUsecase(this._repository);

  final ExerciseRepository _repository;

  @override
  Future<Result<PostExerciseResponse>> call(
      PostExerciseDetailParams params) async {
    return _repository.postExerciseDetail(params);
  }
}

class PostExerciseDetailParams {
  final List<Answer> ans;
  final String exerciseId;
  PostExerciseDetailParams(this.exerciseId, {required this.ans});
}
