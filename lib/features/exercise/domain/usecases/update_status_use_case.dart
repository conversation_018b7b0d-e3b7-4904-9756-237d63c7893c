import 'package:gotcha_mfg_app/core/usecase/usecase.dart';
import 'package:gotcha_mfg_app/features/exercise/data/models/update_exercise_status_request.dart';
import 'package:gotcha_mfg_app/shared/models/generic_response.dart';
import 'package:gotcha_mfg_app/features/exercise/domain/repositories/exercise_repository.dart';
import 'package:gotcha_mfg_app/shared/models/result.dart';

class UpdateExerciseStatusUseCase
    implements UseCase<Result<GenericResponse>, UpdateStatusParams> {
  UpdateExerciseStatusUseCase(this._repository);

  final ExerciseRepository _repository;

  @override
  Future<Result<GenericResponse>> call(UpdateStatusParams params) async {
    return _repository.updateExerciseStatus(params);
  }
}

class UpdateStatusParams {
  final String dailyExerciseId;
  final UpdateExerciseStatusRequest request;

  UpdateStatusParams({
    required this.dailyExerciseId,
    required this.request,
  });
}
