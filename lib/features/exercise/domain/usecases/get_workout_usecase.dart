import 'package:gotcha_mfg_app/core/usecase/usecase.dart';
import 'package:gotcha_mfg_app/shared/models/result.dart';

import '../../data/models/workout_response_model.dart';
import '../repositories/exercise_repository.dart';

class GetSingleWorkoutUsecase
    implements UseCase<Result<WorkoutResponseModel>, GetWorkoutParams> {
  GetSingleWorkoutUsecase(this._repository);

  final ExerciseRepository _repository;

  @override
  Future<Result<WorkoutResponseModel>> call(GetWorkoutParams params) async {
    return _repository.getWorkout(params);
  }
}

class GetWorkoutParams {
  final String id;
  GetWorkoutParams({required this.id});
}
