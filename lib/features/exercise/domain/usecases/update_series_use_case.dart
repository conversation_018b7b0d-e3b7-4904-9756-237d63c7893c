import 'package:gotcha_mfg_app/core/usecase/usecase.dart';
import 'package:gotcha_mfg_app/shared/models/generic_response.dart';
import 'package:gotcha_mfg_app/features/exercise/domain/repositories/exercise_repository.dart';
import 'package:gotcha_mfg_app/shared/models/result.dart';

class UpdateSeriesStatusUseCase
    implements UseCase<Result<GenericResponse>, UpdateSeriesParams> {
  UpdateSeriesStatusUseCase(this._repository);

  final ExerciseRepository _repository;

  @override
  Future<Result<GenericResponse>> call(UpdateSeriesParams params) async {
    return _repository.updateSeriesStatus(params);
  }
}

class UpdateSeriesParams {
  final String seriesId;
  bool? isCompleted;
  bool? isOverride;

  UpdateSeriesParams({
    required this.seriesId,
    required this.isCompleted,
    required this.isOverride,
  });
}
