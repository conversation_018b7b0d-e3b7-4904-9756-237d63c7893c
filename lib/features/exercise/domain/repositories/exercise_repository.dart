import 'package:gotcha_mfg_app/shared/models/generic_response.dart';
import 'package:gotcha_mfg_app/features/exercise/data/models/workout_response_model.dart';
import 'package:gotcha_mfg_app/features/exercise/data/models/get_exercise_response.dart';
import 'package:gotcha_mfg_app/features/exercise/domain/usecases/get_workout_usecase.dart';
import 'package:gotcha_mfg_app/features/exercise/domain/usecases/update_status_use_case.dart';
import 'package:gotcha_mfg_app/shared/models/result.dart';

import '../../data/models/new_exercise_response.dart';
import '../../data/models/post_exercise_response.dart';
import '../usecases/post_exercise_use_case.dart';
import '../usecases/update_series_use_case.dart';

abstract class ExerciseRepository {
  Future<Result<NewExerciseResponse>> getExerciseDetail(
      ExerciseDetailParams params);
  Future<Result<PostExerciseResponse>> postExerciseDetail(
      PostExerciseDetailParams params);
  Future<Result<WorkoutResponseModel>> getWorkout(GetWorkoutParams params);
  Future<Result<GenericResponse>> updateExerciseStatus(
      UpdateStatusParams params);
  Future<Result<GenericResponse>> updateSeriesStatus(UpdateSeriesParams params);
}
