import 'dart:math';

import 'package:auto_route/auto_route.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:gotcha_mfg_app/core/extensions/app_text_theme_extension.dart';
import 'package:gotcha_mfg_app/features/exercise/data/models/new_exercise_response.dart';
import 'package:gotcha_mfg_app/shared/widgets/round_button.dart';
import 'package:share_plus/share_plus.dart';

import '../../../../config/theme/app_assets.dart';
import '../../../../config/theme/app_colors.dart';
import '../../../../core/mixpanel_service.dart';
import '../../../../locator.dart';
import '../../data/models/exercise_response.dart';
import '../blocs/reflection_data/reflection_data_cubit.dart';

@RoutePage()
class TextAnimationNewPage extends StatefulWidget {
  final Media media;
  final VoidCallback onComplete;
  final VoidCallback tapFavourite;
  final bool isFavorite;
  final VoidCallback onBackPress;

  const TextAnimationNewPage({
    super.key,
    required this.media,
    required this.onComplete,
    required this.tapFavourite,
    required this.isFavorite,
    required this.onBackPress,
  });

  @override
  State<TextAnimationNewPage> createState() => _TextAnimationNewPageState();
}

class _TextAnimationNewPageState extends State<TextAnimationNewPage>
    with SingleTickerProviderStateMixin {
  final PageController _pageController = PageController();
  ExerciseResponse? exerciseResponse;
  List<String>? questionList = [];
  bool isLastPage = false;
  bool isProcessingNavigation = false;
  String? dailyId;
  bool isFavourite = false;
  bool isSocial = false;
  bool? isFav;
  double _currentPageValue = 0.0;

  bool _isAnimating = false; // Flag to track animation

  bool isDayTime() {
    final now = DateTime.now();
    final hour = now.hour;

    const dayStartTime = 6; // 6:00 AM
    const dayEndTime = 18; // 6:00 PM

    return hour >= dayStartTime && hour < dayEndTime;
  }

  void _handlePageChange(int index) {
    HapticFeedback.selectionClick();
    setState(() {
      isLastPage = questionList?.length == (index + 1);
    });

    // Add Mixpanel tracking for page change
    sl<MixpanelService>().trackEvent(
      'Text Animation Tap',
      properties: {
        'Exercise ID': exerciseResponse?.data?.id ?? '',
        'Page Number': index + 1,
        'Total Pages': questionList?.length ?? 0,
        'Text Content': questionList?[index] ?? '',
        'Timestamp': DateTime.now().toIso8601String(),
      },
    );
  }

  @override
  void initState() {
    super.initState();
    sl<MixpanelService>().trackScreenView(
      'Text Animation Page',
      properties: {'Code': 'screen_view.text_animation_page'},
    );
    sl<MixpanelService>()
        .trackTextAnimation(widget.media.id ?? '', 'Text Animation Page');

    _pageController.addListener(_onScroll);
    context.read<ReflectionDataCubit>().clearAnswers();
    isFavourite = widget.isFavorite;
    questionList = widget.media.text;
    dailyId = widget.media.id;
  }

  void _onScroll() {
    if (_pageController.position.haveDimensions) {
      setState(() {
        _currentPageValue = _pageController.page ?? 0;
      });
    }
  }

  bool isEvenDay() {
    DateTime today = DateTime.now();
    return today.day % 2 == 0;
  }

  bool shouldReturnTrue(int percentage) {
    if (percentage < 0 || percentage > 100) {
      return false;
    }
    int randomValue = Random().nextInt(100) + 1;
    return randomValue <= percentage;
  }

  @override
  void dispose() {
    _pageController.removeListener(_onScroll);
    _pageController.dispose();
    super.dispose();
  }

  void _animateToNextPage() {
    if (_isAnimating) return; // Prevent new animations if one is in progress
    _isAnimating = true; // Set the flag
    _pageController
        .animateToPage(
          (_pageController.page?.toInt() ?? 0) + 1,
          duration: const Duration(milliseconds: 1250),
          curve: Curves.easeInOutCubic,
        )
        .then((_) => _isAnimating = false); //Reset flag after animation
  }

  void _animateToPreviousPage() {
    if (_isAnimating) return; // Prevent new animations

    _isAnimating = true;
    final currentPage = _pageController.page?.toInt() ?? 0;
    if (currentPage > 0) {
      _pageController
          .animateToPage(
        currentPage - 1,
        duration: const Duration(milliseconds: 1250),
        curve: Curves.easeInOutCubic,
      )
          .then((_) {
        _isAnimating = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    final size = MediaQuery.sizeOf(context);

    final textTheme = Theme.of(context).textTheme;

    final bool isNight = !isDayTime();
    return PopScope(
        canPop: false, // Disable default back navigation
        onPopInvoked: (didPop) {
          if (didPop) return;
          // Custom back button logic here
          widget.onBackPress();
        },
        child: Scaffold(
          extendBodyBehindAppBar: true,
          appBar: AppBar(
            toolbarHeight: 0,
            elevation: 0,
            forceMaterialTransparency: true,
            systemOverlayStyle: const SystemUiOverlayStyle(
              statusBarColor: Colors.transparent,
              statusBarIconBrightness: Brightness.light,
              statusBarBrightness: Brightness.dark,
              systemNavigationBarColor: Colors.transparent,
              systemNavigationBarIconBrightness: Brightness.dark,
            ),
          ),
          body: Container(
            decoration: BoxDecoration(
              gradient: isNight
                  ? const LinearGradient(
                      begin: Alignment.topCenter,
                      end: Alignment.bottomCenter,
                      colors: [
                        Color(0xFF5E324A),
                        Color(0xFF4C2D49),
                        Color(0xFF382949),
                        Color(0xFF292548),
                        Color(0xFF1D2147),
                        Color(0xFF152047),
                        Color(0xFF111F48),
                        Color(0xFF0D1E47),
                      ],
                    )
                  : LinearGradient(
                      begin: Alignment.topCenter,
                      end: Alignment.bottomCenter,
                      colors: [
                        isEvenDay()
                            ? const Color(0xFFFADBDC)
                            : const Color.fromARGB(255, 185, 210, 237),
                        isEvenDay()
                            ? const Color(0xFFE0C9CD)
                            : const Color.fromARGB(255, 169, 203, 240),
                        isEvenDay()
                            ? const Color(0xFFDFB9BD)
                            : const Color.fromARGB(255, 185, 210, 237),
                        isEvenDay()
                            ? const Color(0xFFDFB9BD)
                            : const Color(0xFFCBD6E2),
                        isEvenDay()
                            ? const Color(0xFFE0C9CD)
                            : const Color.fromARGB(255, 169, 203, 240),
                        isEvenDay()
                            ? const Color(0xFFFADBDC)
                            : const Color(0xFFCBD6E2),
                      ],
                    ),
            ),
            child: Stack(
              children: [
                Stack(
                  children: [
                    PageView.builder(
                      controller: _pageController,
                      scrollDirection: Axis.vertical,
                      itemCount: questionList?.length,
                      physics: const PageScrollPhysics(),
                      onPageChanged: _handlePageChange,
                      itemBuilder: (context, index) {
                        return AnimatedBuilder(
                          animation: _pageController,
                          builder: (context, child) {
                            double pageValue = 0;
                            if (_pageController.position.haveDimensions) {
                              pageValue = _pageController.page ?? 0;
                            }

                            double difference = (pageValue - index).abs();
                            double currentOpacity =
                                (1 - (difference * 6)).clamp(0.0, 1.0);
                            double yOffset =
                                MediaQuery.of(context).size.height *
                                    0.5 *
                                    difference;
                            if (pageValue > index) {
                              yOffset = yOffset;
                            } else {
                              yOffset = -yOffset;
                            }

                            return Opacity(
                              opacity: currentOpacity,
                              child: Transform(
                                transform: Matrix4.identity()
                                  ..translate(0.0, yOffset),
                                alignment: Alignment.center,
                                child: Column(
                                  mainAxisAlignment: MainAxisAlignment.center,
                                  children: [
                                    const Spacer(),
                                    Center(
                                      child: Padding(
                                        padding: const EdgeInsets.symmetric(
                                            horizontal: 40),
                                        child: Text(
                                          questionList![index],
                                          textAlign: TextAlign.center,
                                          maxLines: 15,
                                          overflow: TextOverflow.ellipsis,
                                          style:
                                              textTheme.gothamMedium.copyWith(
                                            fontSize: 20,
                                            color: isNight
                                                ? Colors.white
                                                : AppColors.navy,
                                          ),
                                        ),
                                      ),
                                    ),
                                    const Spacer(),
                                  ],
                                ),
                              ),
                            );
                          },
                        );
                      },
                    ),
                    Padding(
                      padding:
                          const EdgeInsets.only(top: 50, left: 20, right: 20),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          RoundButton(
                            onToggle: () => widget.onBackPress(),
                            child: Icon(
                              Icons.arrow_back_ios_new_outlined,
                              color: isNight ? Colors.white : AppColors.navy,
                              size: 16,
                            ),
                          ),
                          Row(
                            children: [
                              RoundButton(
                                  onToggle: () {
                                    Share.share(
                                      '${questionList![_pageController.page?.toInt() ?? 0]}\n\nExcerpt from an exercise in the Mental Fitness Gym',
                                      sharePositionOrigin: Rect.fromLTWH(
                                        0,
                                        0,
                                        MediaQuery.of(context).size.width,
                                        MediaQuery.of(context).size.height / 2,
                                      ),
                                    );
                                    // Mixpanel share event
                                    sl<MixpanelService>().trackEvent(
                                        'Share Exercise',
                                        properties: {
                                          'Exercise ID': widget.media.id ?? '',
                                          'Title': widget.media.title ?? '',
                                        });
                                  },
                                  child: Image.asset(
                                    AppAssets.shareiconnew,
                                    color:
                                        isNight ? Colors.white : AppColors.navy,
                                    width: 18,
                                  )),
                              const SizedBox(width: 20),
                              RoundButton(
                                onToggle: () {
                                  setState(() {
                                    isFavourite = !isFavourite;
                                  });
                                  widget.tapFavourite();
                                  // Mixpanel favorite event
                                  sl<MixpanelService>().trackEvent(
                                      'Toggle Favorite',
                                      properties: {
                                        'Exercise ID': widget.media.id ?? '',
                                        'Is Favorite': isFavourite,
                                        'Title': widget.media.title ?? '',
                                      });
                                },
                                child: isFavourite
                                    ? const Icon(
                                        Icons.favorite,
                                        color: Colors.red,
                                        size: 18,
                                      )
                                    : Icon(
                                        Icons.favorite_outline,
                                        color: isNight
                                            ? Colors.white
                                            : AppColors.navy,
                                        size: 18,
                                      ),
                              ),
                            ],
                          ),
                        ],
                      ),
                    ),
                    Positioned(
                      bottom: 56,
                      left: 0,
                      right: 0,
                      child: GestureDetector(
                        onTap: () {
                          if (_isAnimating) return;
                          if (isLastPage || (questionList?.length == 1)) {
                            widget.onComplete();
                          } else {
                            // Track the tap event before animating to next page

                            _animateToNextPage();
                          }
                        },
                        child: Container(
                          width: double.infinity,
                          height: 48,
                          color: Colors.transparent,
                          child: RoundButton(
                            onToggle: () {
                              if (_isAnimating) return;
                              if (isLastPage) {
                                widget.onComplete();
                              } else {
                                _animateToNextPage();
                              }
                            },
                            child: Icon(
                              Icons.keyboard_arrow_down,
                              color: isNight ? Colors.white : AppColors.navy,
                            ),
                          ),
                        ),
                      ),
                    ),
                    Positioned(
                      bottom: 0,
                      left: 0,
                      right: 0,
                      top: size.height * 0.5,
                      child: GestureDetector(
                        onTap: () {
                          if (_isAnimating) return;
                          if (isLastPage || (questionList?.length == 1)) {
                            widget.onComplete();
                          } else {
                            _animateToNextPage();
                          }
                        },
                        onVerticalDragEnd: (details) {
                          if (_isAnimating) return;
                          // Detect upward or downward swipe
                          if (details.primaryVelocity != null) {
                            if (details.primaryVelocity! < 0) {
                              // Swipe Up (Bottom to Top)
                              if (isLastPage || (questionList?.length == 1)) {
                                widget.onComplete();
                              } else {
                                _animateToNextPage();
                              }
                            } else if (details.primaryVelocity! > 0) {
                              //Swipe Down (Top to Bottom)
                              _animateToPreviousPage();
                            }
                          }
                        },
                        child: Container(
                          color: Colors.transparent,
                          height: size.height * .5,
                          width: double.infinity,
                        ),
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ));
  }
}
