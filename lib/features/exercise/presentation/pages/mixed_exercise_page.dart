import 'dart:math';

import 'package:auto_route/auto_route.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:easy_debounce/easy_debounce.dart';
import 'package:gotcha_mfg_app/core/utils/snackbar_service.dart';
import 'package:gotcha_mfg_app/features/exercise/data/models/new_exercise_response.dart';
import 'package:gotcha_mfg_app/features/exercise/presentation/pages/video_player_new_page.dart';
import 'package:gotcha_mfg_app/shared/widgets/loading_widget.dart';
import 'package:gotcha_mfg_app/shared/widgets/retry_widget.dart';

import '../../../../config/router/app_router.gr.dart';
import '../../../../core/mixpanel_service.dart';
import '../../../../locator.dart';
import '../../../../shared/widgets/popup.dart';
import '../../../favourites/data/models/favourite_request_model.dart';
import '../../../favourites/presentation/bloc/favourite/favourites_cubit.dart';
import '../../data/models/update_exercise_status_request.dart';
import '../../domain/usecases/update_series_use_case.dart';
import '../../domain/usecases/update_status_use_case.dart';
import '../blocs/exercise/exercise_cubit.dart';
import '../blocs/reflection_data/reflection_data_cubit.dart';
import 'flip_text_new_page.dart';
import 'image_text_new_page.dart';
import 'text_animation_new_page.dart';

@RoutePage()
class MixedExercisePage extends StatefulWidget {
  final String id;
  final bool notification;
  final String seriesId;
  final String exerciseId;
  final bool isLast;
  final bool isFirst;
  final bool isOverride;

  const MixedExercisePage({
    super.key,
    required this.id,
    required this.notification,
    required this.seriesId,
    required this.isLast,
    required this.isFirst,
    required this.isOverride,
    this.exerciseId = '',
  });

  @override
  State<MixedExercisePage> createState() => _MixedExercisePageState();
}

class _MixedExercisePageState extends State<MixedExercisePage> {
  NewExerciseResponse? _exerciseResponse;
  int _currentIndex = 0;
  bool _showVillage = false;
  bool _isFavourite = false;
  bool _isNavigating = false;

  @override
  void initState() {
    super.initState();
    _initializeExercise();
    final cubit = context.read<ReflectionDataCubit>();

    cubit.clearAnswers();

    _exerciseResponse = null;

    // mixpanel
    sl<MixpanelService>().trackScreenView(
      'Mixed Exercise Container',
      properties: {'Code': 'screen_view.mixed_exercise_container'},
    );
  }

  @override
  void dispose() {
    EasyDebounce.cancel('navigate_next');
    super.dispose();
  }

  void _initializeExercise() {
    if (widget.seriesId.isEmpty || !widget.isFirst) {
      _fetchExerciseData();
    } else {
      // Sequence important: first set initial status, then fetch data
      _setInitialStatus(widget.seriesId);
      Future.delayed(const Duration(seconds: 1), _fetchExerciseData);
    }
  }

  void _fetchExerciseData() {
    context.read<ExerciseCubit>().getExerciseResponse(
          widget.id,
          widget.seriesId,
        );
  }

  void _markExerciseComplete() {
    _updateFinalStatus(
      dailyExerciseId: _exerciseResponse?.data?.dailyExerciseId ?? '',
      isCompleted: true,
      timeSpent: "N/A",
      seriesId: widget.seriesId,
      isFav: _isFavourite,
      exerciseId: widget.id,
    );
  }

  void _toggleFavourite() {
    setState(() {
      _isFavourite = !_isFavourite;
    });

    final request = FavouriteRequest(
      id: widget.exerciseId.isNotEmpty ? widget.exerciseId : widget.id,
      isExercise: true,
    );
    context.read<FavouritesCubit>().toggleFavorites(request);
  }

  Future<void> _navigateToNext() async {
    if (_isNavigating) return;

    _isNavigating = true;

    EasyDebounce.debounce(
      'navigate_next',
      const Duration(milliseconds: 500),
      () async {
        if (_currentIndex < (_exerciseResponse?.data?.media?.length ?? 0) - 1) {
          setState(() {
            _currentIndex++;
          });
          _isNavigating = false;
          return;
        }

        _markExerciseComplete();

        // mixpanel
        sl<MixpanelService>().trackEvent('Exercise Completed', properties: {
          'Page': 'Mixed Exercise Container',
          'Code': 'click.mixed_exercise_container.exercise_completed',
          'Exercise ID': widget.id,
          'Exercise Title': _exerciseResponse?.data?.title ?? '',
          'Exercise Type': _exerciseResponse?.data?.exerciseType ?? '',
        });

        final reflectionQuestions =
            _exerciseResponse?.data?.reflectionQuestions;
        final shouldShowFeedback = _shouldShowFeedbackFlow();

        // Check for social connectedness protective factor
        if (_exerciseResponse?.data?.protectiveFactors
                ?.toLowerCase()
                .contains('social connectedness') ==
            true) {
          setState(() {
            _showVillage = true;
          });
        }

        _handleReflectionOrNextStep(reflectionQuestions, shouldShowFeedback);
        _isNavigating = false;
      },
    );
  }

  void _handleReflectionOrNextStep(
      List<ReflectionQuestion>? reflectionQuestions, bool shouldShowFeedback) {
    if (reflectionQuestions?.isNotEmpty == true) {
      PageRouteInfo route = reflectionQuestions?[0].isMultiChoice == true
          ? ReflectionMultiNewRoute(
              notification: widget.notification,
              refId: _exerciseResponse?.data?.dailyExerciseId ?? '',
              index: 0,
              reflection: reflectionQuestions,
              feedback: shouldShowFeedback,
              isVillage: _showVillage,
            )
          : ReflectionTextNewRoute(
              notification: widget.notification,
              refId: _exerciseResponse?.data?.dailyExerciseId ?? '',
              index: 0,
              reflection: reflectionQuestions,
              feedback: shouldShowFeedback,
              isVillage: _showVillage,
            );
      context.replaceRoute(route);
    } else {
      _handleNoReflectionCase(shouldShowFeedback);
    }
  }

  void _handleNoReflectionCase(bool showFeedback) {
    if (_showVillage) {
      CustomCupertinoAlertDialog.yesOrNoPopup(
        context,
        title: "Check-in",
        content: "Do you want to check-in with your village?",
        onYes: () {
          context.router.replaceAll(
            [
              HomeRoute(index: 2),
              const VillageHomeRoute(),
            ],
            updateExistingRoutes: false,
          );
          //mixpanel
          sl<MixpanelService>().trackButtonClick(
            'Yes - Go to Village',
            properties: {
              'Page': 'Exercise Feedback Page',
              'Code': 'click.exercise_feedback_page.alert_yes'
            },
          );
        },
        onNo: () {
          Navigator.pop(context);
          context.router.replaceAll(
            [HomeRoute(index: 0)],
            updateExistingRoutes: false,
          );
          //mixpanel
          sl<MixpanelService>().trackButtonClick(
            'No - Go to Home',
            properties: {
              'Page': 'Exercise Feedback Page',
              'Code': 'click.exercise_feedback_page.alert_no'
            },
          );
        },
      );
    } else {
      context.pushRoute(ExerciseFeedbackRoute(feedback: showFeedback));
    }
  }

  void _onPreviousTap() {
    if (_currentIndex > 0) {
      setState(() {
        _currentIndex--;
      });
    } else {
      Navigator.of(context).pop();
    }
  }

  Widget _getExercisePage(Media media, BuildContext context) {
    final pageMap = {
      'video': VideoPlayerNewPage(
        media: media,
        onComplete: _navigateToNext,
        tapFavourite: _toggleFavourite,
        isFavorite: _isFavourite,
        onBackPress: _onPreviousTap,
        title: _exerciseResponse?.data?.title ?? '',
        subTitle: _exerciseResponse?.data?.exerciseType ?? '',
        categories: _exerciseResponse?.data?.categories ?? [],
      ),
      'text': TextAnimationNewPage(
        media: media,
        onComplete: _navigateToNext,
        tapFavourite: _toggleFavourite,
        isFavorite: _isFavourite,
        onBackPress: _onPreviousTap,
      ),
      'flip_text': FlipTextNewPage(
        media: media,
        onComplete: _navigateToNext,
        tapFavourite: _toggleFavourite,
        isFavorite: _isFavourite,
        onBackPress: _onPreviousTap,
      ),
      'image': ImageTextNewPage(
        media: media,
        onComplete: _navigateToNext,
        tapFavourite: _toggleFavourite,
        isFavorite: _isFavourite,
        onBackPress: _onPreviousTap,
      ),
    };

    return pageMap[media.mediaType] ??
        RetryWidget(
          onRetry: _fetchExerciseData,
          color: Colors.white,
        );
  }

  bool _shouldShowFeedbackFlow() {
    final percentage = _exerciseResponse?.data?.feedbackPercentage ?? 25;
    if (percentage < 0 || percentage > 100) {
      return false;
    }
    return Random().nextInt(100) + 1 <= percentage;
  }

  void _setInitialStatus(String seriesId) {
    if (seriesId.isEmpty || !widget.isFirst) return;
    context.read<ExerciseCubit>().updateSeriesStatus(
          UpdateSeriesParams(
            seriesId: seriesId,
            isCompleted: false,
            isOverride: widget.isOverride,
          ),
        );
  }

  void _updateFinalStatus({
    required String dailyExerciseId,
    required bool isCompleted,
    required String timeSpent,
    required String? seriesId,
    required bool? isFav,
    required String? exerciseId,
  }) async {
    final updateExerciseStatusRequest = UpdateExerciseStatusRequest(
      isCompleted: isCompleted,
      timeSpent: timeSpent,
    );

    final params = UpdateStatusParams(
      dailyExerciseId: dailyExerciseId,
      request: updateExerciseStatusRequest,
    );

    context.read<ExerciseCubit>().updateExerciseStatus(params);

    if (seriesId != null && seriesId.isNotEmpty && widget.isLast) {
      await Future.delayed(const Duration(milliseconds: 100));
      if (mounted) {
        context.read<ExerciseCubit>().updateSeriesStatus(
              UpdateSeriesParams(
                seriesId: seriesId,
                isCompleted: true,
                isOverride: false,
              ),
            );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return BlocConsumer<ExerciseCubit, ExerciseState>(
      listener: (context, state) {
        if (state is ExerciseError) {
          SnackBarService.error(
            context: context,
            message: state.error,
          );
        }
        if (state is ExerciseLoaded) {
          if (_exerciseResponse?.data?.protectiveFactors
                  ?.toLowerCase()
                  .contains('social connectedness') ==
              true) {
            setState(() {
              _showVillage = true;
            });
          }
          // if (state.data?.data?.isRedirectToVillage == true) {
          //   setState(() {
          //     _showVillage = true;
          //   });
          // }
          _exerciseResponse = state.data;
          _isFavourite = state.data?.data?.isFavorite ?? false;
        }
        if (state is FavUpLoaded) {
          setState(() {
            _isFavourite = !_isFavourite;
          });
        }
      },
      builder: (context, state) {
        if (state is ExerciseLoading) {
          return const LoadingWidget(color: Colors.white);
        }
        if (state is ExerciseError) {
          return RetryWidget(
            onRetry: _fetchExerciseData,
            color: Colors.white,
          );
        }
        return Scaffold(
          body: _getExercisePage(
              (_exerciseResponse!.data!.media!
                ..sort((a, b) => (a.mediaOrder ?? 0)
                    .compareTo(b.mediaOrder ?? 0)))[_currentIndex],
              context),
        );
      },
    );
  }
}
