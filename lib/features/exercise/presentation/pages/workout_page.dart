import 'package:auto_route/auto_route.dart';
import 'package:dotted_border/dotted_border.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:gap/gap.dart';
import 'package:gotcha_mfg_app/config/theme/app_assets.dart';
import 'package:gotcha_mfg_app/config/theme/app_colors.dart';
import 'package:gotcha_mfg_app/core/extensions/app_text_theme_extension.dart';
import 'package:gotcha_mfg_app/core/extensions/url_extensions.dart';
import 'package:gotcha_mfg_app/core/utils/snackbar_service.dart';
import 'package:gotcha_mfg_app/features/exercise/data/models/workout_response_model.dart';
import 'package:gotcha_mfg_app/features/exercise/domain/usecases/get_workout_usecase.dart';
import 'package:gotcha_mfg_app/features/exercise/presentation/blocs/workout/workout_cubit.dart';
import 'package:gotcha_mfg_app/shared/widgets/app_header.dart';
import 'package:gotcha_mfg_app/shared/widgets/curved_separator.dart';
import 'package:gotcha_mfg_app/shared/widgets/loading_widget.dart';
import 'package:gotcha_mfg_app/shared/widgets/network_image_indicator.dart';
import 'package:gotcha_mfg_app/shared/widgets/popup.dart';
import 'package:gotcha_mfg_app/shared/widgets/primary_button.dart';
import 'package:gotcha_mfg_app/shared/widgets/retry_widget.dart';
import 'package:timelines_plus/timelines_plus.dart';

import '../../../../core/mixpanel_service.dart';
import '../../../../core/utils/platform_utils.dart';
import '../../../../locator.dart';
import 'mixed_exercise_page.dart';

@RoutePage()
class WorkoutPage extends StatefulWidget {
  const WorkoutPage({
    super.key,
    required this.seriesId,
  });

  final String seriesId;

  @override
  State<WorkoutPage> createState() => _WorkoutPageState();
}

class _WorkoutPageState extends State<WorkoutPage> {
  var params = GetWorkoutParams(id: '');

  @override
  void initState() {
    super.initState();
    sl<MixpanelService>().trackScreenView(
      'Workout Page',
      properties: {'Code': 'screen_view.workout_page'},
    );
    params = GetWorkoutParams(id: widget.seriesId);
    context.read<WorkoutCubit>().getWorkouts(params);
  }

  @override
  Widget build(BuildContext context) {
    var textTheme = Theme.of(context).textTheme;
    return BlocConsumer<WorkoutCubit, WorkoutState>(
      listener: (context, state) {
        if (state is WorkoutError) {
          SnackBarService.error(
            context: context,
            message: state.message,
          );
        }
      },
      builder: (context, state) {
        if (state is WorkoutLoading) {
          return const LoadingWidget(color: Colors.white);
        }
        if (state is WorkoutError) {
          return RetryWidget(
            color: Colors.white,
            onRetry: () {
              context.read<WorkoutCubit>().getWorkouts(params);
            },
          );
        }
        if (state is WorkoutLoaded) {
          final workouts = state.workoutResponse?.data?.workout;
          final workoutsData = state.workoutResponse?.data?.workout?.workouts;
          final isAnyWorkoutActive =
              state.workoutResponse?.data?.hasAnyPreviousWorkoutActive ?? false;
          bool isStarted =
              workoutsData?.any((e) => e.isCompleted == true) ?? false;
          bool isFinished =
              workoutsData?.every((e) => e.isCompleted == true) ?? false;

          return Scaffold(
            appBar: AppBar(
              toolbarHeight: 0,
              elevation: 0,
              systemOverlayStyle: const SystemUiOverlayStyle(
                statusBarColor: Colors.white,
                systemNavigationBarIconBrightness: Brightness.dark,
                statusBarIconBrightness: Brightness.dark,
                statusBarBrightness: Brightness.light,
                systemNavigationBarColor: Colors.white,
              ),
            ),
            body: Container(
              margin: EdgeInsets.only(
                top: isIos ? 4 : 8,
                right: 8,
                left: 8,
                bottom: isIos ? 64 : 48,
              ),
              child: ListView(
                children: [
                  AppHeader(
                    title: workouts?.seriesTitle ?? 'N/A',
                    subTitle:
                        isStarted ? null : workouts?.seriesDescription ?? 'N/A',
                    onBackTap: () {
                      Navigator.pop(context);
                    },
                    subTitleStyle:
                        textTheme.bodyRegular.copyWith(color: Colors.white),
                  ),
                  const CurvedSeparator(
                    outerColor: AppColors.navy,
                    innerColor: Colors.white,
                    height: 32,
                  ),
                  Padding(
                    padding: const EdgeInsets.only(right: 8),
                    child: Timeline.tileBuilder(
                      shrinkWrap: true,
                      physics: const NeverScrollableScrollPhysics(),
                      theme: TimelineThemeData(
                        nodePosition: 0.06,
                        color: AppColors.coral,
                        indicatorTheme: const IndicatorThemeData(
                          size: 15.0,
                        ),
                        connectorTheme: const ConnectorThemeData(
                          thickness: 2.0,
                        ),
                      ),
                      builder: TimelineTileBuilder.connected(
                        connectionDirection: ConnectionDirection.before,
                        itemCount: workoutsData?.length ?? 0,
                        contentsBuilder: (_, index) {
                          int completedWorkoutCount = workoutsData
                                  ?.where(
                                      (workout) => workout.isCompleted == true)
                                  .length ??
                              0;
                          var workout = workoutsData?[index];
                          return GestureDetector(
                            onTap: () {
                              if (index > completedWorkoutCount) {
                                CustomCupertinoAlertDialog.showAlertPopup(
                                  context,
                                  title: 'Locked',
                                  content:
                                      "Let's take things one step at a time. Our workouts are structured to be completed in a specific order. Continue with today's progress and you'll be there in no time.",
                                );
                              } else if (workout?.isCompleted == true) {
                                CustomCupertinoAlertDialog.yesOrNoPopup(
                                  context,
                                  title: 'Redo',
                                  content:
                                      'You\'ve already completed this day of your workout - want to redo it?',
                                  onNo: () => Navigator.of(context).pop(),
                                  onYes: () {
                                    WorkoutElement? item = workout;
                                    var isLast = item == workoutsData?.last;
                                    var isFirst = item == workoutsData?.first;
                                    Navigator.of(context).push(
                                      MaterialPageRoute(
                                        builder: (context) => MixedExercisePage(
                                          exerciseId: item?.exerciseId ?? '',
                                          notification: false,
                                          id: item?.workoutId ?? '',
                                          seriesId: workouts?.seriesId ?? '',
                                          isLast: isLast,
                                          isFirst: isFirst,
                                          isOverride: false,
                                        ),
                                      ),
                                    );
                                    // mixpanel
                                    sl<MixpanelService>()
                                        .trackButtonClick('Redo', properties: {
                                      'Page': 'Workout Page',
                                      'Code': 'click.workout_page.redo',
                                      'Workout Name':
                                          workout?.exerciseTitle ?? '',
                                      'Source': 'Card',
                                    });
                                    return;
                                  },
                                );
                              } else {
                                var isWorkoutCompleted =
                                    hasAnyWorkoutCompletedToday(workoutsData);

                                if (isWorkoutCompleted) {
                                  CustomCupertinoAlertDialog.showAlertPopup(
                                    context,
                                    title: 'Completed',
                                    content:
                                        "You've already completed your workout for the day.",
                                  );
                                  return;
                                }

                                WorkoutElement? item;

                                if (isFinished) {
                                  item = workoutsData?.first;
                                } else {
                                  item = workoutsData?.firstWhere(
                                    (element) =>
                                        element.isCompleted == false ||
                                        element.isCompleted == null,
                                  );
                                }

                                var isLast = item == workoutsData?.last;
                                var isFirst = item == workoutsData?.first;

                                if (isAnyWorkoutActive) {
                                  CustomCupertinoAlertDialog.yesOrNoPopup(
                                    context,
                                    title: 'New workout',
                                    content:
                                        "Starting a new workout will lose your progress on your current workout. Are you sure you want to do this?",
                                    onYes: () {
                                      Navigator.of(context).push(
                                        MaterialPageRoute(
                                          builder: (context) =>
                                              MixedExercisePage(
                                            exerciseId: item?.exerciseId ?? '',
                                            notification: false,
                                            id: item?.workoutId ?? '',
                                            seriesId: workouts?.seriesId ?? '',
                                            isLast: isLast,
                                            isFirst: isFirst,
                                            isOverride: true,
                                          ),
                                        ),
                                      );
                                      // mixpanel
                                      sl<MixpanelService>().trackButtonClick(
                                          'New Workout',
                                          properties: {
                                            'Page': 'Workout Page',
                                            'Code':
                                                'click.workout_page.new_workout',
                                            'Workout Name':
                                                item?.exerciseTitle ?? '',
                                            'Source': 'Card',
                                          });
                                      return;
                                    },
                                    onNo: () {
                                      Navigator.pop(context);
                                    },
                                  );
                                  return;
                                }
                                Navigator.of(context).push(
                                  MaterialPageRoute(
                                    builder: (context) => MixedExercisePage(
                                      exerciseId: item?.exerciseId ?? '',
                                      notification: false,
                                      id: item?.workoutId ?? '',
                                      seriesId: workouts?.seriesId ?? '',
                                      isLast: isLast,
                                      isFirst: isFirst,
                                      isOverride: false,
                                    ),
                                  ),
                                );
                                // mixpanel
                                sl<MixpanelService>().trackButtonClick(
                                    index == 0
                                        ? 'Start Workout'
                                        : 'Continue Workout',
                                    properties: {
                                      'Page': 'Workout Page',
                                      'Code':
                                          'click.workout_page.continue_workout',
                                      'Workout Name': item?.exerciseTitle ?? '',
                                      'Source': 'Card',
                                    });
                                return;
                              }
                            },
                            child: Container(
                              decoration: BoxDecoration(
                                borderRadius: BorderRadius.circular(12),
                                color:
                                    (isShowingTodayTag(workoutsData, index) &&
                                            workout?.completedAt == null)
                                        ? AppColors.lightRed
                                        : Colors.white,
                              ),
                              margin: const EdgeInsets.all(8),
                              padding: const EdgeInsets.all(8),
                              child: Column(
                                children: [
                                  ClipRRect(
                                    borderRadius: BorderRadius.circular(12),
                                    child: SizedBox(
                                      height: 144,
                                      width: double.infinity,
                                      child: Stack(
                                        children: [
                                          Positioned.fill(
                                            child: NetworkImageWithIndicator(
                                                imageUrl: workoutsData?[index]
                                                        .exerciseThumbnailUrl
                                                        ?.exerciseUrl ??
                                                    ''),
                                          ),
                                          // Add semi-transparent overlay
                                          Positioned.fill(
                                            child: Container(
                                              color:
                                                  Colors.black.withOpacity(0.3),
                                            ),
                                          ),
                                          _buildLockIcon(workoutsData, index),
                                          _buildTodayTag(workoutsData,
                                              index), // Use the updated function
                                          Positioned(
                                            bottom: 16,
                                            right: 16,
                                            child: Container(
                                              padding: const EdgeInsets.all(8),
                                              width: 32,
                                              decoration: const BoxDecoration(
                                                color: AppColors.coral,
                                                shape: BoxShape.circle,
                                              ),
                                              child: Image.asset(
                                                AppAssets.cornerArrow,
                                                width: 20,
                                              ),
                                            ),
                                          ),
                                        ],
                                      ),
                                    ),
                                  ),
                                  Padding(
                                    padding: const EdgeInsets.all(8.0),
                                    child: Row(
                                      mainAxisAlignment:
                                          MainAxisAlignment.spaceBetween,
                                      crossAxisAlignment:
                                          CrossAxisAlignment.start,
                                      children: [
                                        Expanded(
                                          child: Text(
                                            "${workoutsData?[index].exerciseTitle}",
                                            style: isWorkoutToBeHighlighted(
                                                    workoutsData, index)
                                                ? textTheme.bodyEmphasis
                                                : textTheme.ralewayBold
                                                    .copyWith(fontSize: 13),
                                          ),
                                        ),
                                        const Gap(8),
                                        Padding(
                                          padding:
                                              const EdgeInsets.only(top: 2),
                                          child: Text(
                                            "${workoutsData?[index].exerciseMediaDuration} • Day $index",
                                            style: textTheme.labels,
                                          ),
                                        ),
                                      ],
                                    ),
                                  )
                                ],
                              ),
                            ),
                          );
                        },
                        indicatorBuilder: (context, index) {
                          int completedWorkoutCount = workoutsData
                                  ?.where(
                                      (workout) => workout.isCompleted == true)
                                  .length ??
                              0;

                          // Check if the current index corresponds to a completed workout
                          if (index < completedWorkoutCount) {
                            return const DotIndicator(
                              color: AppColors.coral,
                              size: 24,
                              child: Icon(
                                Icons.check,
                                color: Colors.white,
                                size: 16,
                              ),
                            );
                          } else if (index == completedWorkoutCount) {
                            // This is the first incomplete workout
                            return const DotIndicator(
                              color: AppColors.coral,
                              size: 24,
                            );
                          } else {
                            // For all other incomplete workouts
                            return ContainerIndicator(
                              size: 22,
                              child: DottedBorder(
                                borderType: BorderType.Circle,
                                color: AppColors.coral,
                                radius: const Radius.circular(30),
                                strokeWidth: 2,
                                dashPattern: const [4, 4],
                                child: Container(
                                  decoration: BoxDecoration(
                                    borderRadius: BorderRadius.circular(30),
                                  ),
                                  child: const SizedBox.expand(),
                                ),
                              ),
                            );
                          }
                        },
                        connectorBuilder: (context, index, type) {
                          int completedWorkoutCount = ((workoutsData
                                      ?.where((workout) =>
                                          workout.isCompleted == true)
                                      .length ??
                                  0) +
                              1);

                          // If the current index is less than the completed count, use a solid line
                          if (index < completedWorkoutCount) {
                            return const SolidLineConnector(
                              color: AppColors.coral,
                            );
                          } else {
                            // For incomplete workouts, use a dashed line
                            return const DashedLineConnector(
                              color: AppColors.coral,
                              gap: 4,
                            );
                          }
                        },
                      ),
                    ),
                  ),
                  const Gap(120),
                ],
              ),
            ),
            floatingActionButtonLocation:
                FloatingActionButtonLocation.centerFloat,
            floatingActionButton: Container(
              width: double.infinity,
              margin: const EdgeInsets.symmetric(horizontal: 24),
              child: PrimaryButton(
                text: isStarted && !isFinished
                    ? "Continue workout"
                    : isFinished
                        ? "Restart workout"
                        : "Start workout",
                onPressed: () {
                  WorkoutElement? item;
                  String mixpanelEventName = 'Start Workout';
                  String mixpanelEventCode = 'click.workout_page.start_workout';
                  if (isFinished) {
                    // if restart workout, select first item of array
                    item = workoutsData?.first;
                    mixpanelEventName = 'Restart Workout';
                    mixpanelEventCode = 'click.workout_page.restart_workout';
                  } else {
                    var isWorkoutCompleted =
                        hasAnyWorkoutCompletedToday(workoutsData);

                    if (isWorkoutCompleted) {
                      CustomCupertinoAlertDialog.showAlertPopup(
                        context,
                        title: 'Completed',
                        content:
                            "You've already completed your workout for the day.",
                      );
                      return;
                    }
                    item = workoutsData?.firstWhere(
                      (element) =>
                          element.isCompleted == false ||
                          element.isCompleted == null,
                    );
                    if (isStarted && !isFinished) {
                      mixpanelEventName = 'Continue Workout';
                      mixpanelEventCode = 'click.workout_page.continue_workout';
                    }
                  }

                  var isLast = item == workoutsData?.last;
                  var isFirst = item == workoutsData?.first;

                  if (isAnyWorkoutActive) {
                    CustomCupertinoAlertDialog.yesOrNoPopup(
                      context,
                      title: 'New workout',
                      content:
                          "Starting a new workout will lose your progress on your current workout. Are you sure you want to do this?",
                      onYes: () {
                        Navigator.of(context).push(
                          MaterialPageRoute(
                            builder: (context) => MixedExercisePage(
                              notification: false,
                              id: item?.workoutId ?? '',
                              seriesId: workouts?.seriesId ?? '',
                              isLast: isLast,
                              isFirst: isFirst,
                              isOverride: true,
                            ),
                          ),
                        );
                        // mixpanel
                        sl<MixpanelService>()
                            .trackButtonClick('New Workout', properties: {
                          'Page': 'Workout Page',
                          'Code': 'click.workout_page.new_workout',
                          'Workout Name': item?.exerciseTitle ?? '',
                          'Source': 'Card',
                        });
                        return;
                      },
                      onNo: () {
                        Navigator.pop(context);
                      },
                    );
                    return;
                  }
                  Navigator.of(context).push(
                    MaterialPageRoute(
                      builder: (context) => MixedExercisePage(
                        notification: false,
                        id: item?.workoutId ?? '',
                        seriesId: workouts?.seriesId ?? '',
                        isLast: isLast,
                        isFirst: isFirst,
                        isOverride: false,
                      ),
                    ),
                  );
                  // mixpanel
                  sl<MixpanelService>()
                      .trackButtonClick(mixpanelEventName, properties: {
                    'Page': 'Workout Page',
                    'Code': mixpanelEventCode,
                    'Workout Name': item?.exerciseTitle ?? '',
                    'Source': 'Card',
                  });
                  return;
                },
              ),
            ),
          );
        }
        return const SizedBox();
      },
    );
  }

  Widget _buildLockIcon(List<WorkoutElement>? workouts, int index) {
    if (workouts == null || workouts.isEmpty) {
      return const SizedBox();
    }

    // All workouts except the one to be done next:
    if (!isWorkoutToBeHighlighted(workouts, index) &&
        index > (getLastCompletedWorkoutIndex(workouts))) {
      return const Positioned.fill(
        child: Center(
          child: Icon(
            Icons.lock_rounded,
            color: Colors.white,
            size: 24,
          ),
        ),
      );
    }
    return const SizedBox();
  }

  // Helper function to get the index of the next workout to be completed
  int _getNextWorkoutIndex(List<WorkoutElement>? workouts) {
    if (workouts == null || workouts.isEmpty) {
      return -1;
    }

    // Find the first workout that is not completed
    for (int i = 0; i < workouts.length; i++) {
      if (workouts[i].isCompleted != true) {
        return i;
      }
    }

    return -1; // All workouts are completed
  }

  bool isShowingTodayTag(List<WorkoutElement>? workouts, int index) {
    if (workouts == null || workouts.isEmpty) {
      return false;
    }

    // Check if any workout was completed today
    bool workoutCompletedToday = hasAnyWorkoutCompletedToday(workouts);

    // If a workout was completed today, check if this is today's exercise
    if (workoutCompletedToday) {
      return isTodaysExercise(workouts, index);
    } else {
      // If no workout was completed today, check if this is the next workout to be completed
      int nextWorkoutIndex = _getNextWorkoutIndex(workouts);
      return nextWorkoutIndex == index;
    }
  }

  /// Updated _buildTodayTag function
  Widget _buildTodayTag(List<WorkoutElement>? workouts, int index) {
    if (workouts == null || workouts.isEmpty) {
      return const SizedBox();
    }

    // Check if any workout was completed today
    bool workoutCompletedToday = hasAnyWorkoutCompletedToday(workouts);

    // If a workout was completed today, show the tag only on that workout
    if (workoutCompletedToday) {
      if (isTodaysExercise(workouts, index)) {
        return _todayTagWidget();
      }
    } else {
      // If no workout was completed today, show the tag on the next workout to be completed
      int nextWorkoutIndex = _getNextWorkoutIndex(workouts);
      if (nextWorkoutIndex == index) {
        return _todayTagWidget();
      }
    }

    return const SizedBox(); // Don't show the tag
  }

  /// Widget for the "Today" tag
  Widget _todayTagWidget() {
    return Positioned(
      left: 16,
      top: 16,
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
        decoration: BoxDecoration(
          color: AppColors.lightRed,
          borderRadius: BorderRadius.circular(16),
        ),
        child: Text(
          "Today",
          style: Theme.of(context).textTheme.ralewaySemiBold.copyWith(
                color: AppColors.navy,
                fontSize: 10,
              ),
        ),
      ),
    );
  }

  bool isTodaysExercise(List<WorkoutElement>? workoutList, int index) {
    if (workoutList == null || workoutList.isEmpty) {
      return false;
    }

    final today = DateTime.now();
    final todayDate = DateTime(today.year, today.month, today.day);

    final workout = workoutList[index];

    if (workout.isCompleted == true && workout.completedAt != null) {
      final completedAtDate = DateTime(workout.completedAt!.year,
              workout.completedAt!.month, workout.completedAt!.day)
          .toLocal(); // toLocal is important

      return completedAtDate.isAtSameMomentAs(todayDate);
    }

    return false;
  }

  /// Checks if ANY workout in the list has been completed today.
  bool hasAnyWorkoutCompletedToday(List<WorkoutElement>? workoutList) {
    if (workoutList == null || workoutList.isEmpty) {
      return false;
    }

    final todayUtc = DateTime.now();

    for (final workout in workoutList) {
      if (workout.isCompleted == true && workout.completedAt != null) {
        final completedAtDateTimeUtc =
            (workout.completedAt as DateTime).toLocal();

        if (completedAtDateTimeUtc.year == todayUtc.year &&
            completedAtDateTimeUtc.month == todayUtc.month &&
            completedAtDateTimeUtc.day == todayUtc.day) {
          return true;
        }
      }
    }
    return false;
  }

  /// Returns the index of the last workout that was completed, or -1 if none were completed.
  int getLastCompletedWorkoutIndex(List<WorkoutElement>? workouts) {
    if (workouts == null || workouts.isEmpty) {
      return -1;
    }

    // Sort workouts by their order (ascending).  Important for correctness.
    final sortedWorkouts = List<WorkoutElement>.from(workouts)
      ..sort(
          (a, b) => (a.orderOfExercise ?? 0).compareTo(b.orderOfExercise ?? 0));

    // Iterate backwards to find the *last* completed one.
    for (int i = sortedWorkouts.length - 1; i >= 0; i--) {
      if (sortedWorkouts[i].isCompleted == true) {
        return i;
      }
    }
    return -1; // No workouts completed
  }

  /// Determines if a workout at a given index should be highlighted (is the next to be done).
  bool isWorkoutToBeHighlighted(
    List<WorkoutElement>? workouts,
    int index,
  ) {
    if (workouts == null ||
        workouts.isEmpty ||
        index < 0 ||
        index >= workouts.length) {
      return false; // Handle invalid input
    }

    int lastCompletedIndex = getLastCompletedWorkoutIndex(workouts);

    if (lastCompletedIndex == -1) {
      return index == 0; // If no workouts are completed, highlight the first
    }

    return index ==
        lastCompletedIndex + 1; // Highlight the one after the last completed
  }
}
