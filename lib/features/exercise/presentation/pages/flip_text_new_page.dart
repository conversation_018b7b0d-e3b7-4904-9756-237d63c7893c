import 'dart:io';
import 'dart:ui' as ui;

import 'package:auto_route/auto_route.dart';
import 'package:flutter/material.dart';
import 'package:flutter/rendering.dart';
import 'package:flutter/services.dart';
import 'dart:math';
import 'dart:math' as math;

import 'package:gap/gap.dart';
import 'package:gotcha_mfg_app/config/theme/app_assets.dart';
import 'package:gotcha_mfg_app/config/theme/app_colors.dart';
import 'package:gotcha_mfg_app/core/extensions/app_text_theme_extension.dart';
import 'package:gotcha_mfg_app/core/utils/app_print.dart';
import 'package:gotcha_mfg_app/features/exercise/data/models/new_exercise_response.dart';
import 'package:gotcha_mfg_app/shared/widgets/round_button.dart';
import 'package:share_plus/share_plus.dart';

import 'package:path_provider/path_provider.dart';

import '../../../../core/utils/snackbar_service.dart';
import '../../../../core/mixpanel_service.dart';
import '../../../../locator.dart';

@RoutePage()
class FlipTextNewPage extends StatefulWidget {
  final Media media;
  final VoidCallback onComplete;
  final VoidCallback tapFavourite;
  final bool isFavorite;
  final VoidCallback onBackPress;

  const FlipTextNewPage({
    super.key,
    required this.media,
    required this.onComplete,
    required this.tapFavourite,
    required this.isFavorite,
    required this.onBackPress,
  });

  @override
  State<FlipTextNewPage> createState() => _FlipTextNewPageState();
}

class _FlipTextNewPageState extends State<FlipTextNewPage>
    with SingleTickerProviderStateMixin {
  // State variables
  bool _isFavorite = false;
  final GlobalKey _screenshotKey = GlobalKey();

  late AnimationController _animationController;
  bool _isFrontSide = true;
  List<Map<String, dynamic>> _pageContents = [];

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 500),
      vsync: this,
    );
    _pageContents = [
      {
        'backgroundColor': AppColors.lightRed,
        'icon': Icons.favorite,
        'title': widget.media.title ?? 'N/A',
        'quote': widget.media.subtitle ?? 'N/A',
        'content': null,
      },
      {
        'backgroundColor': AppColors.grey,
        'icon': null,
        'title': null,
        'quote': null,
        'content': widget.media.body ?? 'N/A',
      },
    ];
    _isFavorite = widget.isFavorite;
    // Mixpanel screen view tracking
    sl<MixpanelService>().trackScreenView(
      'Flip Card Page',
      properties: {
        'Code': 'screen_view.flip_card_page',
        'Exercise ID': widget.media.id ?? '',
        'Title': widget.media.title ?? '',
      },
    );
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  void _toggleFlip() {
    if (_animationController.isDismissed) {
      _animationController.forward();
    } else if (_animationController.isCompleted) {
      _animationController.reverse();
    }
    setState(() {
      _isFrontSide = !_isFrontSide;
    });
    // Mixpanel flip event
    sl<MixpanelService>().trackEvent('Flip Card', properties: {
      'Exercise ID': widget.media.id ?? '',
      'Is Front Side': _isFrontSide,
      'Title': widget.media.title ?? '',
      'Action': 'Flip'
    });
  }

  Future<void> _captureAndShareScreenshot() async {
    try {
      // Add delay to ensure rendering is complete
      await Future.delayed(const Duration(milliseconds: 300));

      RenderRepaintBoundary? boundary = _screenshotKey.currentContext
          ?.findRenderObject() as RenderRepaintBoundary?;

      if (boundary == null) {
        info('Error: Could not find RepaintBoundary');
        return;
      }

      // Capture the image with high quality
      ui.Image image = await boundary.toImage(pixelRatio: 3.0);
      ByteData? byteData =
          await image.toByteData(format: ui.ImageByteFormat.png);

      if (byteData == null) {
        info('Error: Failed to get byte data from image');
        return;
      }

      Uint8List pngBytes = byteData.buffer.asUint8List();

      // Save to temporary file with unique filename
      final tempDir = await getTemporaryDirectory();
      final timestamp = DateTime.now().millisecondsSinceEpoch;
      File file =
          await File('${tempDir.path}/screenshot_$timestamp.png').create();
      await file.writeAsBytes(pngBytes);

      // Share the file
      await Share.shareXFiles(
        [XFile(file.path)],
        text: 'From an exercise in the Mental Fitness Gym',
        sharePositionOrigin: Rect.fromLTWH(
          0,
          0,
          MediaQuery.of(context).size.width,
          MediaQuery.of(context).size.height / 2,
        ),
      );
      // Mixpanel share event
      sl<MixpanelService>().trackEvent('Share Exercise', properties: {
        'Exercise ID': widget.media.id ?? '',
        'Title': widget.media.title ?? '',
      });
    } catch (e) {
      SnackBarService.error(
        context: context,
        message: 'Error taking screenshot.',
      );
    }
  }

  Widget _buildPageContent(BuildContext context, Map<String, dynamic> content) {
    final textTheme = Theme.of(context).textTheme;
    final bool hasContent = content['content'] !=
        null; // Check if 'content' key exists and is not null
    final bool hasIcon = content['icon'] != null;

    return PopScope(
      canPop: false, // Disable default back navigation
      onPopInvoked: (didPop) {
        if (didPop) return;
        widget.onBackPress();
      },
      child: GestureDetector(
        onHorizontalDragEnd: (details) {
          // Only trigger on significant swipe, avoid accidental flips
          if (details.primaryVelocity != null &&
              details.primaryVelocity!.abs() > 200) {
            _toggleFlip();
          }
        },
        onTap: _toggleFlip,
        child: RepaintBoundary(
          key: _screenshotKey,
          child: Stack(
            children: [
              Container(
                width: MediaQuery.of(context).size.width,
                decoration: BoxDecoration(
                  gradient: hasIcon
                      ? const LinearGradient(
                          begin: Alignment.topCenter,
                          end: Alignment.bottomLeft,
                          colors: [
                            Color(0xFFffdfdd),
                            Color(0xFFfde8e6),
                            Color(0xFFfeecec),
                            Color(0xFFFDF6F7),
                          ],
                        )
                      : null,
                  borderRadius: BorderRadius.circular(20),
                  color: hasIcon ? null : content['backgroundColor'] as Color?,
                ),
                child: Padding(
                  padding:
                      const EdgeInsets.symmetric(horizontal: 0, vertical: 8),
                  child: LayoutBuilder(
                    builder: (context, constraints) {
                      return SingleChildScrollView(
                        physics: const BouncingScrollPhysics(),
                        child: ConstrainedBox(
                          constraints: BoxConstraints(
                            minHeight: constraints.maxHeight,
                          ),
                          child: IntrinsicHeight(
                            child: Column(
                              mainAxisAlignment: hasContent
                                  ? MainAxisAlignment.center
                                  : MainAxisAlignment.start,
                              children: [
                                (hasIcon)
                                    ? SizedBox(
                                        height:
                                            MediaQuery.of(context).size.height *
                                                0.1)
                                    : const Gap(
                                        40), // Reduced initial space slightly
                                if (hasIcon)
                                  Center(
                                    child: Image.asset(
                                      AppAssets
                                          .fliptextlogo, // Make sure this path is correct
                                      scale: 5,
                                      errorBuilder: (context, error,
                                              stackTrace) =>
                                          const Icon(Icons.error,
                                              color: Colors
                                                  .red), // Add error handling for assets
                                    ),
                                  ),
                                if (content['title'] != null)
                                  const Spacer(), // Using Gap package
                                if (content['title'] != null)
                                  Padding(
                                    padding: const EdgeInsets.symmetric(
                                        horizontal:
                                            24), // Add horizontal padding if text might wrap
                                    child: Text(
                                      content['title'].toUpperCase(),
                                      textAlign: TextAlign.center,
                                      style: textTheme.primaryHeading,
                                    ),
                                  ),
                                if (content['quote'] != null) const Spacer(),
                                if (content['quote'] != null)
                                  Padding(
                                    padding: const EdgeInsets.symmetric(
                                        horizontal: 24),
                                    child: Text(
                                      content['quote'],
                                      textAlign: TextAlign.center,
                                      style: textTheme.ralewayRegular.copyWith(
                                        fontSize: 17,
                                        fontStyle: FontStyle.italic,
                                      ),
                                    ),
                                  ),
                                if (hasContent)
                                  const Spacer()
                                else
                                  const Spacer(flex: 4),
                                if (hasContent)
                                  Padding(
                                    padding: const EdgeInsets.symmetric(
                                        horizontal: 24),
                                    child: Text(
                                      content['content'],
                                      textAlign: TextAlign.center,
                                      style: textTheme.ralewayRegular
                                          .copyWith(fontSize: 17),
                                    ),
                                  ),
                                // If centering, add Spacer to push content up if needed,
                                // otherwise Gap for consistent bottom spacing
                                if (hasContent) ...[
                                  const Spacer(),
                                  const Gap(32)
                                ] else
                                  const Gap(32),
                                // Ensure some space at the very bottom when scrolling ends if not centered
                              ],
                            ),
                          ),
                        ),
                      );
                    },
                  ),
                ),
              ),
              // Grain effect overlay
              Positioned.fill(
                child: IgnorePointer(
                  child: CustomPaint(
                    painter: GrainPainter(),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildAppBar(BuildContext context, Media? data) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 8.0),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          IconButton(
            icon: const Icon(
              Icons.arrow_back_ios,
              color: AppColors.navy,
              size: 18,
            ),
            onPressed: () => widget.onBackPress(),
          ),
          const Spacer(),
          Row(
            children: [
              _buildShareButton(data?.title),
              const Gap(8),
              _buildFavoriteButton(),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildShareButton(String? cardTitle) {
    return RoundButton(
      onToggle: () {
        _captureAndShareScreenshot();
      },
      child: Image.asset(
        AppAssets.shareiconnew,
        color: AppColors.navy,
        width: 18,
      ),
    );
  }

  Widget _buildFavoriteButton() {
    return RoundButton(
      onToggle: () {
        setState(() {
          _isFavorite = !_isFavorite;
        });
        widget.tapFavourite();
        // Mixpanel favorite event
        sl<MixpanelService>().trackEvent('Toggle Favorite', properties: {
          'Exercise ID': widget.media.id ?? '',
          'Is Favorite': _isFavorite,
          'Title': widget.media.title ?? '',
        });
      },
      child: _isFavorite
          ? const Icon(
              Icons.favorite,
              color: Colors.red,
              size: 18,
            )
          : const Icon(
              Icons.favorite_outline,
              color: AppColors.navy,
              size: 18,
            ),
    );
  }

  Widget _buildContinueButton() {
    return GestureDetector(
      onTap: _handleContinuePress,
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Text(
            'Tap to continue ',
            style: Theme.of(context).textTheme.linkText,
          ),
          const Icon(
            Icons.arrow_forward,
            size: 14,
          )
        ],
      ),
    );
  }

  void _handleContinuePress() {
    if (_isFrontSide) {
      // If on front side, flip to back side
      _toggleFlip();
      // Mixpanel continue (flip) event
      sl<MixpanelService>().trackEvent('Flip Card', properties: {
        'Exercise ID': widget.media.id ?? '',
        'Is Front Side': _isFrontSide,
        'Title': widget.media.title ?? '',
        'Action': 'Flip'
      });
    } else {
      // If on back side, call the onComplete callback
      widget.onComplete();
      // Mixpanel continue (complete) event
      sl<MixpanelService>().trackButtonClick('Tap to Continue', properties: {
        'Exercise ID': widget.media.id ?? '',
        'Title': widget.media.title ?? '',
        'Page': 'Flip Card Page',
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        elevation: 0,
        toolbarHeight: 0,
        systemOverlayStyle: const SystemUiOverlayStyle(
          statusBarColor: Colors.white,
          statusBarIconBrightness: Brightness.dark,
          statusBarBrightness: Brightness.light,
          systemNavigationBarIconBrightness: Brightness.dark,
          systemNavigationBarColor: Colors.white,
        ),
      ),
      body: SafeArea(
        child: Padding(
          padding: const EdgeInsets.fromLTRB(8, 0, 8, 8),
          child: Column(
            children: [
              _buildAppBar(context, widget.media),
              Expanded(
                child: AnimatedBuilder(
                  animation: _animationController,
                  builder: _buildFlipAnimation,
                ),
              ),
              const Gap(24),
              _buildContinueButton(),
              const Gap(8),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildFlipAnimation(BuildContext context, Widget? child) {
    final angle = _animationController.value * pi;
    final isFrontVisible = angle <= pi / 2 || angle >= 3 * pi / 2;

    return Transform(
      transform: Matrix4.identity()
        ..setEntry(3, 2, 0.001)
        ..rotateY(angle),
      alignment: Alignment.center,
      child: isFrontVisible
          ? _buildPageContent(context, _pageContents[0])
          : Transform(
              transform: Matrix4.rotationY(pi),
              alignment: Alignment.center,
              child: _buildPageContent(context, _pageContents[1]),
            ),
    );
  }
}

class GrainPainter extends CustomPainter {
  final double opacity;
  final int density;
  final Color color;
  final math.Random _random = math.Random();

  GrainPainter(
      {this.opacity = 0.08, this.density = 120, this.color = Colors.black});

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = color.withOpacity(opacity)
      ..strokeWidth = 1.5;
    for (int i = 0; i < size.width * size.height / density; i++) {
      final dx = _random.nextDouble() * size.width;
      final dy = _random.nextDouble() * size.height;
      canvas.drawPoints(
        ui.PointMode.points,
        [Offset(dx, dy)],
        paint,
      );
    }
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}
