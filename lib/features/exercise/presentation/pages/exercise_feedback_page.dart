// import 'package:auto_route/auto_route.dart';
// import 'package:flutter/material.dart';
// import 'package:flutter/services.dart';
// import 'package:gap/gap.dart';
// import 'package:gotcha_mfg_app/config/router/app_router.gr.dart';
// import 'package:gotcha_mfg_app/config/theme/app_assets.dart';
// import 'package:gotcha_mfg_app/config/theme/app_colors.dart';
// import 'package:gotcha_mfg_app/core/extensions/app_text_theme_extension.dart';
// import 'package:gotcha_mfg_app/shared/widgets/popup.dart';

// import '../../../../core/utils/platform_utils.dart';

// @RoutePage()
// class ExerciseFeedbackPage extends StatefulWidget {
//   final bool feedback;
//   const ExerciseFeedbackPage({super.key, required this.feedback});

//   @override
//   State<ExerciseFeedbackPage> createState() => _ExerciseFeedbackPageState();
// }

// class _ExerciseFeedbackPageState extends State<ExerciseFeedbackPage> {
//   List feedbacks = ['Loved it', 'It was okay', 'Not for me'];
//   List<String> feedbacksImages = [
//     AppAssets.lovedit,
//     AppAssets.itwasok,
//     AppAssets.notforme,
//   ];

//   int? selectedIndex;

//   @override
//   Widget build(BuildContext context) {
//     // SystemChrome.setSystemUIOverlayStyle(const SystemUiOverlayStyle(
//     //   systemNavigationBarIconBrightness: Brightness.dark,
//     //   statusBarColor: Colors.white,
//     //   statusBarBrightness: Brightness.light,
//     //   statusBarIconBrightness: Brightness.dark,
//     // ));
//     final textTheme = Theme.of(context).textTheme;
//     final size = MediaQuery.of(context).size;
//     return Scaffold(
//       backgroundColor: Colors.white,
//       appBar: AppBar(
//         toolbarHeight: 0,
//         elevation: 0,
//         systemOverlayStyle: const SystemUiOverlayStyle(
//           statusBarColor: Colors.white,
//           systemNavigationBarIconBrightness: Brightness.dark,
//           statusBarBrightness: Brightness.light,
//           statusBarIconBrightness: Brightness.dark,
//           systemNavigationBarColor: AppColors.lightBlue,
//         ),
//       ),
//       body: Padding(
//         padding: EdgeInsets.only(
//           top: isIos ? 4 : 8,
//           left: 8,
//           right: 8,
//         ),
//         child: Container(
//           width: size.width,
//           height: size.height,
//           decoration: const BoxDecoration(
//             color: AppColors.lightBlue,
//             borderRadius: BorderRadius.only(
//               topLeft: Radius.circular(30),
//               topRight: Radius.circular(30),
//             ),
//           ),
//           child: Padding(
//             padding: const EdgeInsets.all(32),
//             child: Column(
//               mainAxisAlignment: MainAxisAlignment.center,
//               crossAxisAlignment: CrossAxisAlignment.start,
//               children: [
//                 Text(
//                   'Did you find this exercise helpful?',
//                   style: textTheme.gothamBold.copyWith(fontSize: 18),
//                 ),
//                 const Gap(4),
//                 Text(
//                   'Your feedback helps us to recommend more relevant exercises.',
//                   style: textTheme.ralewayRegular.copyWith(fontSize: 14),
//                 ),
//                 const Gap(24),
//                 for (var item in feedbacks)
//                   Column(
//                     children: [
//                       ActionChip(
//                         avatar: ClipRRect(
//                           borderRadius: BorderRadius.circular(100),
//                           child: SizedBox(
//                             height: 40,
//                             width: 40,
//                             child: Padding(
//                               padding: const EdgeInsets.all(2),
//                               child: Image.asset(
//                                 feedbacksImages[feedbacks.indexOf(item)],
//                               ),
//                             ),
//                           ),
//                         ),
//                         shape: StadiumBorder(
//                           side: BorderSide(
//                             color: selectedIndex == feedbacks.indexOf(item)
//                                 ? AppColors.coral
//                                 : Colors.white,
//                           ),
//                         ),
//                         onPressed: () {
//                           setState(() {
//                             selectedIndex = feedbacks.indexOf(item);
//                           });
//                           if (selectedIndex == 2) {
//                             context.replaceRoute(HomeRoute(index: 0));
//                           } else {
//                             if (widget.feedback) {
//                               CustomCupertinoAlertDialog.yesOrNoPopup(
//                                 context,
//                                 title: "Have feedback?",
//                                 content:
//                                     "Your feedback is invaluable in making the gym better for you (and our other gym members)",
//                                 onYes: () {
//                                   context.router.replaceAll(
//                                     [
//                                       HomeRoute(index: 0),
//                                       const FeedBackRoute()
//                                     ],
//                                     updateExistingRoutes: false,
//                                   );
//                                   return;
//                                 },
//                                 yesButtonText: "Yes",
//                                 noButtonText: "Not today",
//                                 onNo: () {
//                                   Navigator.pop(context);
//                                   context.router.replaceAll(
//                                     [HomeRoute(index: 0)],
//                                     updateExistingRoutes: false,
//                                   );
//                                   return;
//                                 },
//                               );
//                             } else {
//                               context.router.replaceAll(
//                                 [HomeRoute(index: 0)],
//                                 updateExistingRoutes: false,
//                               );
//                               return;
//                             }
//                           }
//                         },
//                         label: Text(
//                           item,
//                           style: Theme.of(context)
//                               .textTheme
//                               .ralewaySemiBold
//                               .copyWith(
//                                 fontSize: 14,
//                                 color: AppColors.navy,
//                               ),
//                         ),
//                         backgroundColor:
//                             selectedIndex == feedbacks.indexOf(item)
//                                 ? AppColors.lightRed
//                                 : Colors.white,
//                       ),
//                       const Gap(4),
//                     ],
//                   ),
//               ],
//             ),
//           ),
//         ),
//       ),
//     );
//   }
// }

import 'package:auto_route/auto_route.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:gap/gap.dart';
import 'package:gotcha_mfg_app/config/router/app_router.gr.dart';
import 'package:gotcha_mfg_app/config/theme/app_assets.dart';
import 'package:gotcha_mfg_app/config/theme/app_colors.dart';
import 'package:gotcha_mfg_app/core/extensions/app_text_theme_extension.dart';
import 'package:gotcha_mfg_app/shared/widgets/popup.dart';
import '../../../../locator.dart';
import '../../../../core/mixpanel_service.dart';

import '../../../../core/utils/platform_utils.dart';

@RoutePage()
class ExerciseFeedbackPage extends StatefulWidget {
  final bool feedback;
  const ExerciseFeedbackPage({super.key, required this.feedback});

  @override
  State<ExerciseFeedbackPage> createState() => _ExerciseFeedbackPageState();
}

class _ExerciseFeedbackPageState extends State<ExerciseFeedbackPage> {
  List feedbacks = ['Loved it', 'It was okay', 'Not for me'];
  List<String> feedbacksImages = [
    AppAssets.lovedit,
    AppAssets.itwasok,
    AppAssets.notforme,
  ];

  int? selectedIndex;

  @override
  void initState() {
    sl<MixpanelService>().trackScreenView(
      'Exercise Feedback Page',
      properties: {'Code': 'screen_view.exercise_feedback_page'},
    );
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    // SystemChrome.setSystemUIOverlayStyle(const SystemUiOverlayStyle(
    //   systemNavigationBarIconBrightness: Brightness.dark,
    //   statusBarColor: Colors.white,
    //   statusBarBrightness: Brightness.light,
    //   statusBarIconBrightness: Brightness.dark,
    // ));
    final textTheme = Theme.of(context).textTheme;
    final size = MediaQuery.of(context).size;
    return Scaffold(
      backgroundColor: Colors.white,
      appBar: AppBar(
        toolbarHeight: 0,
        elevation: 0,
        systemOverlayStyle: const SystemUiOverlayStyle(
          statusBarColor: Colors.white,
          systemNavigationBarIconBrightness: Brightness.dark,
          statusBarBrightness: Brightness.light,
          statusBarIconBrightness: Brightness.dark,
          systemNavigationBarColor: AppColors.lightBlue,
        ),
      ),
      body: Padding(
        padding: EdgeInsets.only(
          top: isIos ? 4 : 8,
          left: 8,
          right: 8,
        ),
        child: Container(
          width: size.width,
          height: size.height,
          decoration: const BoxDecoration(
            color: AppColors.lightBlue,
            borderRadius: BorderRadius.only(
              topLeft: Radius.circular(30),
              topRight: Radius.circular(30),
            ),
          ),
          child: Padding(
            padding: const EdgeInsets.all(32),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Did you find this exercise helpful?',
                  style: textTheme.gothamBold.copyWith(fontSize: 18),
                ),
                const Gap(4),
                Text(
                  'Your feedback helps us to recommend more relevant exercises.',
                  style: textTheme.ralewayRegular.copyWith(fontSize: 14),
                ),
                const Gap(24),
                for (var item in feedbacks)
                  Column(
                    children: [
                      ActionChip(
                        avatar: ClipRRect(
                          borderRadius: BorderRadius.circular(100),
                          child: SizedBox(
                            height: 40,
                            width: 40,
                            child: Padding(
                              padding: const EdgeInsets.all(2),
                              child: Image.asset(
                                feedbacksImages[feedbacks.indexOf(item)],
                              ),
                            ),
                          ),
                        ),
                        shape: StadiumBorder(
                          side: BorderSide(
                            color: selectedIndex == feedbacks.indexOf(item)
                                ? AppColors.coral
                                : Colors.white,
                          ),
                        ),
                        onPressed: () {
                          setState(() {
                            selectedIndex = feedbacks.indexOf(item);
                          });
                          // Track feedback selection in Mixpanel
                          sl<MixpanelService>().trackEvent(
                            'Exercise Feedback',
                            properties: {
                              'Selected Feedback':
                                  feedbacks[selectedIndex ?? 0],
                              'Code': 'exercise_feedback.selected_feedback'
                            },
                          );
                          if (selectedIndex == 2) {
                            context.replaceRoute(HomeRoute(index: 0));
                          } else {
                            // if (widget.feedback) {
                            CustomCupertinoAlertDialog.yesOrNoPopup(
                              context,
                              title: "Have feedback?",
                              content:
                                  "Your feedback is invaluable in making the gym better for you (and our other gym members)",
                              onYes: () {
                                context.router.replaceAll(
                                  [HomeRoute(index: 0), const FeedBackRoute()],
                                  updateExistingRoutes: false,
                                );
                                // mixpanel
                                sl<MixpanelService>().trackButtonClick('Yes',
                                    properties: {
                                      'Page': 'Exercise Feedback Page',
                                      'Code': 'click.exercise_feedback_page.yes'
                                    });
                                return;
                              },
                              yesButtonText: "Yes",
                              noButtonText: "Not today",
                              onNo: () {
                                Navigator.pop(context);
                                context.router.replaceAll(
                                  [HomeRoute(index: 0)],
                                  updateExistingRoutes: false,
                                );
                                // mixpanel
                                sl<MixpanelService>()
                                    .trackButtonClick('Not Today', properties: {
                                  'Page': 'Exercise Feedback Page',
                                  'Code':
                                      'click.exercise_feedback_page.not_today'
                                });
                                return;
                              },
                            );
                            // } else {
                            //   context.router.replaceAll(
                            //     [HomeRoute(index: 0)],
                            //     updateExistingRoutes: false,
                            //   );
                            //   return;
                            // }
                          }
                        },
                        label: Text(
                          item,
                          style: Theme.of(context)
                              .textTheme
                              .ralewaySemiBold
                              .copyWith(
                                fontSize: 14,
                                color: AppColors.navy,
                              ),
                        ),
                        backgroundColor:
                            selectedIndex == feedbacks.indexOf(item)
                                ? AppColors.lightRed
                                : Colors.white,
                      ),
                      const Gap(4),
                    ],
                  ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
