import 'package:auto_route/auto_route.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:gap/gap.dart';
import 'package:gotcha_mfg_app/config/router/app_router.gr.dart';
import 'package:gotcha_mfg_app/config/theme/app_colors.dart';
import 'package:gotcha_mfg_app/core/extensions/app_text_theme_extension.dart';
import 'package:gotcha_mfg_app/features/exercise/data/models/new_exercise_response.dart';
// import 'package:gotcha_mfg_app/features/exercise/data/models/exercise_response.dart';
import 'package:gotcha_mfg_app/features/exercise/data/models/reflection_request.dart';
import 'package:gotcha_mfg_app/features/exercise/presentation/blocs/reflection_data/reflection_data_cubit.dart';
import 'package:gotcha_mfg_app/shared/widgets/app_header.dart';
import 'package:gotcha_mfg_app/shared/widgets/primary_button.dart';

import '../../../../core/mixpanel_service.dart';
import '../../../../core/utils/platform_utils.dart';
import '../../../../locator.dart';
import '../widgets/reflection_choice.dart';

@RoutePage()
class ReflectionMultiNewPage extends StatefulWidget {
  final String? refId;
  final List<ReflectionQuestion>? reflection;
  final int index;
  final bool feedback;
  final bool isVillage;
  final bool notification;

  const ReflectionMultiNewPage(
      {super.key,
      required this.reflection,
      required this.index,
      required this.refId,
      required this.feedback,
      required this.isVillage,
      required this.notification});

  @override
  State<ReflectionMultiNewPage> createState() => _ReflectionMultiNewPageState();
}

class _ReflectionMultiNewPageState extends State<ReflectionMultiNewPage>
    with WidgetsBindingObserver {
  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addObserver(this);
    _setSystemUIOverlayStyle();
    sl<MixpanelService>().trackScreenView(
      'Reflection Multi Choice Page',
      properties: {'Code': 'screen_view.reflection_multi_choice_page'},
    );
  }

  @override
  void dispose() {
    WidgetsBinding.instance.removeObserver(this);
    super.dispose();
  }

  @override
  void didChangeMetrics() {
    super.didChangeMetrics();
    _setSystemUIOverlayStyle();
  }

  void _setSystemUIOverlayStyle() {
    SystemChrome.setSystemUIOverlayStyle(const SystemUiOverlayStyle(
      statusBarColor: Colors.transparent,
      statusBarBrightness: Brightness.light,
      statusBarIconBrightness: Brightness.dark,
      systemNavigationBarColor: AppColors.grey,
      systemNavigationBarIconBrightness: Brightness.dark,
    ));
  }

  @override
  Widget build(BuildContext context) {
    final textTheme = Theme.of(context).textTheme;
    final size = MediaQuery.sizeOf(context);
    final isKeyboardOpen = MediaQuery.of(context).viewInsets.bottom != 0;

    return AnnotatedRegion<SystemUiOverlayStyle>(
      value: const SystemUiOverlayStyle(
        statusBarColor: Colors.transparent,
        statusBarBrightness: Brightness.light,
        statusBarIconBrightness: Brightness.dark,
        systemNavigationBarColor: AppColors.grey,
        systemNavigationBarIconBrightness: Brightness.dark,
      ),
      child: BlocConsumer<ReflectionDataCubit, ReflectionDataState>(
        listener: (context, state) {},
        builder: (context, state) {
          return Scaffold(
            resizeToAvoidBottomInset: true,
            backgroundColor: Colors.white,
            appBar: PreferredSize(
              preferredSize: const Size.fromHeight(0),
              child: AppBar(
                elevation: 0,
                backgroundColor: Colors.white,
                systemOverlayStyle: const SystemUiOverlayStyle(
                  statusBarColor: Colors.transparent,
                  statusBarBrightness: Brightness.light,
                  statusBarIconBrightness: Brightness.dark,
                  systemNavigationBarColor: AppColors.grey,
                  systemNavigationBarIconBrightness: Brightness.dark,
                ),
              ),
            ),
            body: Padding(
              padding: EdgeInsets.only(
                top: isIos ? 4 : 8,
                left: 8,
                right: 8,
              ),
              child: Column(
                children: [
                  AppHeader(
                      title: 'Exercise reflection',
                      currentStep: widget.index + 1,
                      totalSteps: (widget.reflection?.length ?? 0) + 1),
                  Expanded(
                    child: Container(
                      color: AppColors.navy,
                      child: Container(
                        padding: EdgeInsets.only(bottom: isIos ? 88 : 56),
                        decoration: const BoxDecoration(
                          borderRadius: BorderRadius.only(
                            topLeft: Radius.circular(30),
                            topRight: Radius.circular(30),
                          ),
                          color: AppColors.grey,
                        ),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            const Spacer(flex: 1),
                            Expanded(
                              flex: 5,
                              child: SingleChildScrollView(
                                child: Padding(
                                  padding: const EdgeInsets.symmetric(
                                    horizontal: 24,
                                    vertical: 24,
                                  ),
                                  child: Column(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    children: [
                                      Text(
                                        widget.reflection?[widget.index]
                                                .questionText ??
                                            '',
                                        style: Theme.of(context)
                                            .textTheme
                                            .gothamBold
                                            .copyWith(fontSize: 18),
                                      ),
                                      const Gap(20),
                                      ListView.builder(
                                        shrinkWrap: true,
                                        physics:
                                            const NeverScrollableScrollPhysics(),
                                        itemCount: widget
                                            .reflection![widget.index]
                                            .choices!
                                            .length,
                                        itemBuilder: (context, index) {
                                          var item = widget
                                              .reflection![widget.index]
                                              .choices![index];
                                          var cubit = context
                                              .read<ReflectionDataCubit>();
                                          var isSelected =
                                              cubit.isSelected(item.id ?? '');
                                          return Align(
                                            alignment: Alignment.centerLeft,
                                            child: ReflectionChoice(
                                              isSelected: isSelected,
                                              title: item.choiceText ?? '',
                                              onTap: () {
                                                cubit.addAnswer(
                                                  Answer(
                                                    questionId: widget
                                                            .reflection![
                                                                widget.index]
                                                            .id ??
                                                        '',
                                                    answerText: null,
                                                    selectedChoiceId:
                                                        item.id ?? '',
                                                  ),
                                                );
                                              },
                                            ),
                                          );
                                        },
                                      ),
                                    ],
                                  ),
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ),
            floatingActionButtonLocation:
                FloatingActionButtonLocation.centerDocked,
            floatingActionButton: isKeyboardOpen
                ? const SizedBox()
                : Padding(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 32,
                      vertical: 24,
                    ),
                    child: SizedBox(
                      width: size.width,
                      child: PrimaryButton(
                        text: 'Next',
                        isEnabled: (state as ReflectionDataLoaded).answers!.any(
                            (element) =>
                                element.questionId ==
                                widget.reflection?[widget.index].id),
                        onPressed: () {
                          final dataCubit = context.read<ReflectionDataCubit>();
                          var data = dataCubit.state as ReflectionDataLoaded;
                          if ((widget.index + 1) == widget.reflection?.length) {
                            sl<MixpanelService>().trackSubmissionWithOptions(
                              "Reflection Complete Page",
                              answers: data.answers,
                            );
                            context.pushRoute(
                              ReflectionCompleteNewRoute(
                                  notification: widget.notification,
                                  refId: widget.refId,
                                  answers: data.answers!,
                                  totalBars: widget.reflection?.length ?? 0,
                                  feedback: widget.feedback,
                                  isVillage: widget.isVillage),
                            );
                          } else if ((widget.index) <
                              (widget.reflection?.length ?? 0)) {
                            if (widget.reflection?[widget.index + 1]
                                    .isMultiChoice ==
                                true) {
                              context.pushRoute(
                                ReflectionMultiNewRoute(
                                    notification: widget.notification,
                                    refId: widget.refId,
                                    index: widget.index + 1,
                                    reflection: widget.reflection,
                                    feedback: widget.feedback,
                                    isVillage: widget.isVillage),
                              );
                            } else {
                              context.pushRoute(
                                ReflectionTextNewRoute(
                                    notification: widget.notification,
                                    refId: widget.refId,
                                    index: widget.index + 1,
                                    reflection: widget.reflection,
                                    feedback: widget.feedback,
                                    isVillage: widget.isVillage),
                              );
                            }
                          }
                        },
                      ),
                    ),
                  ),
          );
        },
      ),
    );
  }
}
