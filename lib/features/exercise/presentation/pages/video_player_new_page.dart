import 'dart:async';

import 'package:auto_route/auto_route.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:gap/gap.dart';
import 'package:gotcha_mfg_app/config/theme/app_colors.dart';
import 'package:gotcha_mfg_app/core/extensions/app_text_theme_extension.dart';
import 'package:gotcha_mfg_app/core/extensions/string_extensions.dart';
import 'package:gotcha_mfg_app/core/extensions/time_extensions.dart';
import 'package:gotcha_mfg_app/core/utils/platform_utils.dart';
import 'package:gotcha_mfg_app/features/exercise/data/models/new_exercise_response.dart';
import 'package:gotcha_mfg_app/shared/widgets/loading_widget.dart';
import 'package:gotcha_mfg_app/shared/widgets/round_button.dart';
import 'package:share_plus/share_plus.dart';
import 'package:youtube_player_flutter/youtube_player_flutter.dart';

import '../../../../config/theme/app_assets.dart';
import '../../../../core/mixpanel_service.dart';
import '../../../../locator.dart';
import '../blocs/reflection_data/reflection_data_cubit.dart';

@RoutePage()
class VideoPlayerNewPage extends StatefulWidget {
  const VideoPlayerNewPage({
    super.key,
    required this.media,
    required this.onComplete,
    required this.tapFavourite,
    required this.isFavorite,
    required this.onBackPress,
    this.title = '',
    this.subTitle = '',
    this.categories = const [],
  });

  final Media media;
  final VoidCallback onComplete;
  final VoidCallback tapFavourite;
  final VoidCallback onBackPress;
  final bool isFavorite;
  final String? title;
  final String? subTitle;
  final List<Category>? categories;

  @override
  State<VideoPlayerNewPage> createState() => _FullscreenVideoPlayerState();
}

class _FullscreenVideoPlayerState extends State<VideoPlayerNewPage> {
  late YoutubePlayerController _controller;
  bool beSocial = false;
  DateTime? _videoStartTime;
  int _lastProgressPercentage = 0;
  bool _hasCompleted = false;

  // State variables
  bool _isMuted = false;
  bool _isFavorite = false;
  bool _showControls = true;
  bool _isLoadingVideo = true;

  String _currentPosition = '0.00';

  Timer? _hideControlsTimer;

  // Track previous playing state to avoid duplicate pause events
  bool _wasPlaying = false;

  @override
  void initState() {
    super.initState();
    sl<MixpanelService>().trackScreenView(
      'Video Player Page',
      properties: {'Code': 'screen_view.videoplayer_page'},
    );
    _isFavorite = widget.isFavorite;
    allowAllOrientations();
    // Set initial status
    WidgetsBinding.instance.addPostFrameCallback((_) {
      // _setInitialStatus(widget.seriesId);
    });

    // Fetch exercise data from the API
    // context.read<ExerciseCubit>().getExerciseResponse(
    //       widget.id,
    //       widget.seriesId,
    //     );

    context.read<ReflectionDataCubit>().clearAnswers();

    // Initialize YouTube player with a placeholder or empty video ID
    _controller = YoutubePlayerController(
      initialVideoId: '',
      flags: const YoutubePlayerFlags(
        autoPlay: true,
        loop: false,
        forceHD: true,
        enableCaption: true,
        hideControls: true,
        controlsVisibleAtStart: false,
        // showVideoProgressIndicator: false, // Hide the default progress bar
      ),
    );

    _updateYoutubePlayerController(widget.media.mediaUrl ?? '');

    // Add listener to update the state when the video is playing or paused
    _controllerListener();

    // Set status bar style
    // WidgetsBinding.instance.addPostFrameCallback((_) => _setStatusBar());

    _startHideControlsTimer(); // Start timer initially
  }

  void allowAllOrientations() async {
    await SystemChrome.setPreferredOrientations([
      DeviceOrientation.portraitUp,
      DeviceOrientation.portraitDown,
      DeviceOrientation.landscapeLeft,
      DeviceOrientation.landscapeRight,
    ]);
  }

  // Add listener to the YouTube player controller
  void _controllerListener() {
    _controller.addListener(() {
      if (!mounted) return;

      setState(() {
        _currentPosition = _controller.value.position.toMinutesSecondsString();
      });

      // Only track progress if we have valid duration
      if (_controller.metadata.duration.inSeconds > 0) {
        _trackVideoProgress();

        // Check for video completion - only if we have valid duration and position
        if (!_hasCompleted &&
            _controller.value.position.inSeconds >=
                _controller.metadata.duration.inSeconds) {
          _hasCompleted = true;
          _trackVideoEnd(true);
          resetOrientationAndUI();
          widget.onComplete();
          _handleNavigationAway();
        }
      }

      // Track video state changes
      if (_controller.value.isPlaying && _videoStartTime == null) {
        _trackVideoStart();
      }
      // Only track pause event when transitioning from playing to paused
      if (_wasPlaying &&
          !_controller.value.isPlaying &&
          _videoStartTime != null &&
          _controller.value.playerState == PlayerState.paused) {
        _trackVideoPause();
      }
      // Update previous playing state
      _wasPlaying = _controller.value.isPlaying;
    });
  }

  @override
  void dispose() {
    // Track video end before disposing
    if (_videoStartTime != null) {
      _trackVideoEnd(false);
    }
    // Reset orientation and UI modes
    resetOrientationAndUI();
    // Dispose of the YouTube player controller
    _controller.dispose();
    _cancelHideControlsTimer(); // Cancel timer in dispose
    super.dispose();
  }

  void resetOrientationAndUI() {
    SystemChrome.setPreferredOrientations([
      DeviceOrientation.portraitUp,
      DeviceOrientation.portraitDown,
    ]);
    // SystemChrome.setSystemUIOverlayStyle(
    //   const SystemUiOverlayStyle(
    //     statusBarColor: Colors.white,
    //     statusBarIconBrightness: Brightness.dark,
    //     statusBarBrightness: Brightness.light,
    //     // systemNavigationBarColor: Colors.white,
    //   ),
    // );
    // SystemChrome.setEnabledSystemUIMode(
    //   SystemUiMode.edgeToEdge,
    // );
  }

  // Function to hide controls
  void _hideControls() {
    setState(() {
      _showControls = false;
    });
  }

  // Toggle controls visibility and reset timer
  void _toggleControls() {
    setState(() {
      _showControls = !_showControls;
    });
    _startHideControlsTimer(); // Reset timer on every tap
  }

  Future<void> _handleNavigationAway() async {
    // Make async
    _controller.pause();
    resetOrientationAndUI();
    await Future.delayed(const Duration(milliseconds: 50)); // Small delay
  }

  // Start or reset the timer to hide controls
  void _startHideControlsTimer() {
    _cancelHideControlsTimer(); // Cancel any existing timer
    _hideControlsTimer = Timer(const Duration(seconds: 5), _hideControls);
  }

  // Cancel the hide controls timer
  void _cancelHideControlsTimer() {
    _hideControlsTimer?.cancel();
  }

  // Update YouTube player controller with the correct URL
  void _updateYoutubePlayerController(String videoUrl) {
    final videoId = YoutubePlayer.convertUrlToId(videoUrl) ?? '';
    if (videoId.isNotEmpty) {
      // _controller.dispose();

      _controller = YoutubePlayerController(
        initialVideoId: videoId,
        flags: const YoutubePlayerFlags(
            autoPlay: true,
            loop: false,
            forceHD: true,
            enableCaption: true,
            hideControls: true,
            controlsVisibleAtStart: false,
            hideThumbnail: true
            // showVideoProgressIndicator: false, // Hide the default progress bar
            ),
      );
      _controllerListener();
      // Listen for player initialization to hide loading
      _controller.addListener(_playerStateListener);
    }
  }

  void _playerStateListener() {
    if (_controller.value.isReady) {
      setState(() {
        _isLoadingVideo = false;
      });
      _controller.removeListener(
          _playerStateListener); // Remove listener after loading
    }
  }

  @override
  Widget build(BuildContext context) {
    var textTheme = Theme.of(context).textTheme;
    var size = MediaQuery.sizeOf(context);
    return PopScope(
      canPop: false,
      onPopInvokedWithResult: (didPop, result) {
        if (didPop) return;
        widget.onBackPress();
        _handleNavigationAway();
      },
      child: OrientationBuilder(
        builder: (context, orientation) {
          var isLandscape = orientation == Orientation.landscape;
          return Scaffold(
            backgroundColor: Colors.black,
            extendBodyBehindAppBar: true,
            appBar: AppBar(
              toolbarHeight: 0,
              backgroundColor: Colors.black,
              elevation: 0,
              forceMaterialTransparency: true,
              systemOverlayStyle: const SystemUiOverlayStyle(
                statusBarColor: Colors.black,
                statusBarIconBrightness: Brightness.light,
                statusBarBrightness: Brightness.dark,
                systemNavigationBarColor: Colors.black,
                systemNavigationBarIconBrightness: Brightness.light,
              ),
            ),
            body: GestureDetector(
              behavior: HitTestBehavior.translucent,
              onTap: () => _toggleControls(), // Call _toggleControls on tap
              onPanDown: (_) =>
                  _startHideControlsTimer(), // Reset timer on pan down (scroll/drag on video area)
              child: Stack(
                fit: StackFit.expand,
                children: [
                  // YouTube player
                  Center(
                    child: IgnorePointer(
                      child: YoutubePlayerBuilder(
                        onExitFullScreen: () {
                          SystemChrome.setEnabledSystemUIMode(
                              SystemUiMode.edgeToEdge);
                          SystemChrome.setSystemUIOverlayStyle(
                            const SystemUiOverlayStyle(
                              statusBarColor: Colors.black,
                              statusBarBrightness: Brightness.dark,
                              statusBarIconBrightness: Brightness.light,
                              systemNavigationBarColor: Colors.black,
                              systemNavigationBarIconBrightness:
                                  Brightness.light,
                            ),
                          );
                        },
                        player: YoutubePlayer(
                          aspectRatio: 9 / 16,
                          onEnded: (metaData) {
                            if (!_hasCompleted) {
                              _hasCompleted = true;
                              _trackVideoEnd(true);
                              resetOrientationAndUI();
                              widget.onComplete();
                              _handleNavigationAway();
                            }
                          },
                          controller: _controller,
                          progressColors: const ProgressBarColors(
                            playedColor: AppColors.coral,
                            handleColor: AppColors.coral,
                          ),
                          showVideoProgressIndicator: true,
                        ),
                        builder: (context, player) {
                          return player;
                        },
                      ),
                    ),
                  ),
                  if (_isLoadingVideo) const Center(child: Loader()),

                  // Custom controls overlay
                  if (_showControls) ...[
                    Positioned.fill(
                      child: GestureDetector(
                        onTap: () => _toggleControls(),
                        onPanDown: (_) =>
                            _startHideControlsTimer(), // Reset timer on tap on overlay too
                        child: Container(
                          color: Colors.black.withOpacity(0.4),
                        ),
                      ),
                    ),
                  ],
                  if (_showControls && !_isLoadingVideo)
                    Positioned.fill(
                      child: Stack(
                        children: [
                          // Top bar with back and mute
                          Positioned(
                            top: 48,
                            left: 20,
                            right: 20,
                            child: Row(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                RoundButton(
                                  onToggle: () {
                                    // Navigator.pop(context);
                                    widget.onBackPress();
                                    _handleNavigationAway();
                                  },
                                  child: isLandscape
                                      ? const Padding(
                                          padding: EdgeInsets.only(left: 6),
                                          child: Icon(
                                            Icons.arrow_back_ios,
                                            color: Colors.white,
                                            size: 18,
                                          ),
                                        )
                                      : const Padding(
                                          padding: EdgeInsets.only(left: 6),
                                          child: Center(
                                            child: Icon(
                                              Icons.arrow_back_ios,
                                              color: Colors.white,
                                              size: 18,
                                            ),
                                          ),
                                        ),
                                ),
                                const Gap(12),
                                if (isLandscape)
                                  _metadata(
                                    textTheme: textTheme,
                                    title: widget.title ?? 'N/A',
                                    subTitle: widget.subTitle ?? 'N/A',
                                    categories: (widget.categories ?? [])
                                        .map((e) => e.name?.toString() ?? "N/A")
                                        .toList(),
                                  ),
                                const Spacer(),
                                Row(
                                  children: [
                                    RoundButton(
                                      onToggle: () {
                                        Share.share(
                                          'Check this out: ${widget.media.mediaUrl ?? 'N/A'}',
                                          sharePositionOrigin: Rect.fromLTWH(
                                            0,
                                            0,
                                            MediaQuery.of(context).size.width,
                                            MediaQuery.of(context).size.height /
                                                2,
                                          ),
                                        );
                                        // Mixpanel share event
                                        sl<MixpanelService>().trackEvent(
                                            'Share Exercise',
                                            properties: {
                                              'Exercise ID':
                                                  widget.media.id ?? '',
                                              'Title': widget.media.title ?? '',
                                            });
                                      },
                                      child: Image.asset(
                                        AppAssets.shareiconnew,
                                        color: Colors.white,
                                        width: 18,
                                      ),
                                    ),
                                    const Gap(8),
                                    RoundButton(
                                      onToggle: () {
                                        setState(() {
                                          _isMuted = !_isMuted;
                                          _controller.setVolume(
                                            _isMuted ? 0 : 100,
                                          );
                                        });
                                      },
                                      child: Icon(
                                        _isMuted
                                            ? Icons.volume_off
                                            : Icons.volume_up,
                                        color: Colors.white,
                                        size: 18,
                                      ),
                                    ),
                                    const Gap(8),
                                    RoundButton(
                                      onToggle: () {
                                        setState(() {
                                          _isFavorite = !_isFavorite;
                                        });
                                        widget.tapFavourite();
                                        // Mixpanel favorite event
                                        sl<MixpanelService>().trackEvent(
                                            'Toggle Favorite',
                                            properties: {
                                              'Exercise ID':
                                                  widget.media.id ?? '',
                                              'Is Favorite': _isFavorite,
                                              'Title': widget.media.title ?? '',
                                            });
                                      },
                                      child: _isFavorite
                                          ? const Icon(
                                              Icons.favorite,
                                              color: Colors.red,
                                              size: 18,
                                            )
                                          : const Icon(
                                              Icons.favorite_outline,
                                              color: Colors.white,
                                              size: 18,
                                            ),
                                    ),
                                    // const Gap(8),
                                    // RoundButton(
                                    //   onToggle: () {},
                                    //   child: const Icon(
                                    //     Icons.more_horiz_rounded,
                                    //     color: Colors.white,
                                    //     size: 18,
                                    //   ),
                                    // ),
                                  ],
                                ),
                              ],
                            ),
                          ),

                          if (!isLandscape)
                            Positioned(
                              top: 120,
                              left: 20,
                              right: 0,
                              child: _metadata(
                                textTheme: textTheme,
                                title: widget.title ?? 'N/A',
                                subTitle: widget.subTitle ?? 'N/A',
                                categories: (widget.categories ?? [])
                                    .map((e) => e.name?.toString() ?? "N/A")
                                    .toList(),
                              ),
                            ),

                          // Playback controls
                          Positioned(
                            top: 0,
                            bottom: 0,
                            left: 0,
                            right: 0,
                            child: Center(
                              child: Row(
                                mainAxisAlignment:
                                    MainAxisAlignment.spaceEvenly,
                                children: [
                                  const Spacer(
                                    flex: 3,
                                  ),
                                  GestureDetector(
                                    child: Image.asset(
                                      AppAssets.backwardvideo,
                                      width: 22,
                                    ),
                                    onTap: () {
                                      final newPosition =
                                          _controller.value.position -
                                              const Duration(seconds: 10);
                                      _controller.seekTo(newPosition);
                                      _startHideControlsTimer();
                                    },
                                  ),
                                  const Spacer(),
                                  // IconButton(
                                  //   icon: const Icon(
                                  //     Icons.replay_10,
                                  //     color: Colors.white,
                                  //     size: 32,
                                  //   ),
                                  //   onPressed: () {
                                  //     final newPosition =
                                  //         _controller.value.position -
                                  //             const Duration(seconds: 10);
                                  //     _controller.seekTo(newPosition);
                                  //     _startHideControlsTimer(); // Reset timer on control interaction
                                  //   },
                                  // ),
                                  IconButton(
                                    icon: Icon(
                                      _controller.value.isPlaying
                                          ? Icons.pause
                                          : Icons.play_arrow,
                                      color: Colors.white,
                                      size: 48,
                                    ),
                                    onPressed: () {
                                      _controller.value.isPlaying
                                          ? _controller.pause()
                                          : _controller.play();
                                      _startHideControlsTimer(); // Reset timer on control interaction
                                    },
                                  ),
                                  const Spacer(),
                                  GestureDetector(
                                    child: Image.asset(
                                      AppAssets.forwardvideo,
                                      width: 22,
                                    ),
                                    onTap: () {
                                      final newPosition =
                                          _controller.value.position +
                                              const Duration(seconds: 10);
                                      _controller.seekTo(newPosition);
                                      _startHideControlsTimer();
                                    },
                                  ),
                                  const Spacer(
                                    flex: 3,
                                  ),

                                  // IconButton(
                                  //   icon: const Icon(
                                  //     Icons.forward_10,
                                  //     color: Colors.white,
                                  //     size: 32,
                                  //   ),
                                  //   onPressed: () {
                                  //     final newPosition =
                                  //         _controller.value.position +
                                  //             const Duration(seconds: 10);
                                  //     _controller.seekTo(newPosition);
                                  //     _startHideControlsTimer(); // Reset timer on control interaction
                                  //   },
                                  // ),
                                ],
                              ),
                            ),
                          ),
                          Positioned(
                            bottom: isIos ? 48 : 64,
                            left: 20,
                            right: 20,
                            // child: CustomVideoProgressBar(
                            //   // Use CustomVideoProgressBar here
                            //   controller: _controller,
                            // ),
                            child: ProgressBar(
                              key: const Key('progressBar'),
                              controller: _controller,
                              colors: const ProgressBarColors(
                                playedColor: AppColors.coral,
                                handleColor: AppColors.coral,
                              ),
                            ),
                          ),
                          Positioned(
                            // Time duration text below progress bar
                            bottom: isIos ? 72 : 84,
                            left: 20,
                            right: 20,
                            child: Row(
                              children: [
                                const Gap(4),
                                Text(
                                  _currentPosition,
                                  style: textTheme.ralewayMedium.copyWith(
                                    fontSize: 14,
                                    color: Colors.white,
                                  ),
                                ),
                                const Spacer(),
                                Text(
                                  _controller.metadata.duration
                                      .toMinutesSecondsString(),
                                  style: textTheme.ralewayMedium.copyWith(
                                    fontSize: 14,
                                    color: Colors.white,
                                  ),
                                ),
                                const Gap(4),
                              ],
                            ),
                          )
                        ],
                      ),
                    ),
                ],
              ),
            ),
          );
        },
      ),
    );
  }

  // Metadata widget
  Column _metadata({
    required TextTheme textTheme,
    required String title,
    required String subTitle,
    required List<String> categories,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          title,
          style: textTheme.gothamBold.copyWith(
            fontSize: 18,
            color: Colors.white,
          ),
        ),
        Text(
          subTitle.capitalizeFirstLetter(),
          style: textTheme.ralewayRegular.copyWith(
            fontSize: 14,
            color: Colors.white,
          ),
        ),
        Wrap(
          spacing: 8,
          runSpacing: 4,
          children: categories
              .map((category) => Chip(
                    backgroundColor: AppColors.lightRed,
                    shape: const StadiumBorder(
                      side: BorderSide(
                        color: AppColors.lightRed,
                      ),
                    ),
                    label: Text(
                      category.capitalizeFirstLetterOfEachWord(),
                      style: textTheme.ralewaySemiBold.copyWith(
                        color: AppColors.navy,
                        fontSize: 10,
                      ),
                    ),
                  ))
              .toList(),
        ),
      ],
    );
  }

  // Add tracking methods
  Future<void> _trackVideoStart() async {
    _videoStartTime = DateTime.now();
    await Future.delayed(const Duration(milliseconds: 500));
    sl<MixpanelService>().trackEvent('Video Start', properties: {
      'Video ID': widget.media.id ?? '',
      'Video Title': widget.media.title ?? '',
      'Video Duration': _controller.metadata.duration.inSeconds,
      'Timestamp': DateTime.now().toIso8601String(),
    });
  }

  void _trackVideoProgress() {
    if (_controller.value.isPlaying &&
        _controller.metadata.duration.inSeconds > 0) {
      final currentPosition = _controller.value.position;
      final totalDuration = _controller.metadata.duration;

      final progressPercentage =
          (currentPosition.inSeconds * 100 / totalDuration.inSeconds).floor();

      // Only track progress if percentage is valid
      if (progressPercentage >= 0 && progressPercentage <= 100) {
        // Track progress at 25%, 50%, 75% intervals
        if (progressPercentage >= 25 && _lastProgressPercentage < 25) {
          _trackProgressMilestone(25);
        } else if (progressPercentage >= 50 && _lastProgressPercentage < 50) {
          _trackProgressMilestone(50);
        } else if (progressPercentage >= 75 && _lastProgressPercentage < 75) {
          _trackProgressMilestone(75);
        }

        _lastProgressPercentage = progressPercentage;
      }
    }
  }

  void _trackProgressMilestone(int percentage) {
    sl<MixpanelService>().trackEvent('Video Progress', properties: {
      'Video ID': widget.media.id ?? '',
      'Video Title': widget.media.title ?? '',
      'Progress Percentage': percentage,
      'Watch Time': _videoStartTime != null
          ? DateTime.now().difference(_videoStartTime!).inSeconds
          : 0,
      'Timestamp': DateTime.now().toIso8601String(),
    });
  }

  void _trackVideoEnd(bool completed) {
    if (_videoStartTime == null) return; // Don't track if video never started

    final watchDuration = DateTime.now().difference(_videoStartTime!);
    final totalDuration = _controller.metadata.duration;

    // Only calculate percentage if duration is valid
    final watchPercentage = totalDuration.inSeconds > 0
        ? (_controller.value.position.inSeconds * 100 / totalDuration.inSeconds)
            .floor()
        : 0;

    sl<MixpanelService>().trackEvent('Video End', properties: {
      'Video ID': widget.media.id ?? '',
      'Video Title': widget.media.title ?? '',
      'Completed': completed,
      'Watch Time': watchDuration.inSeconds,
      'Watch Percentage': watchPercentage,
      'Drop Off Time': _controller.value.position.inSeconds,
      'Timestamp': DateTime.now().toIso8601String(),
    });
  }

  void _trackVideoPause() {
    sl<MixpanelService>().trackEvent('Video Pause', properties: {
      'Video ID': widget.media.id ?? '',
      'Video Title': widget.media.title ?? '',
      'Pause At': _controller.value.position.inSeconds,
      'Watch Time': _videoStartTime != null
          ? DateTime.now().difference(_videoStartTime!).inSeconds
          : 0,
      'Timestamp': DateTime.now().toIso8601String(),
    });
  }
}
