import 'dart:io';
import 'dart:ui' as ui;

import 'package:auto_route/auto_route.dart';
import 'package:flutter/material.dart';
import 'package:flutter/rendering.dart';
import 'package:flutter/services.dart';
import 'package:gap/gap.dart';
import 'package:gotcha_mfg_app/core/extensions/app_text_theme_extension.dart';
import 'package:gotcha_mfg_app/core/utils/app_print.dart';
import 'package:gotcha_mfg_app/shared/widgets/network_image_indicator.dart';
import 'package:share_plus/share_plus.dart';

import '../../../../config/theme/app_assets.dart';
import '../../../../config/theme/app_colors.dart';
import '../../../../core/utils/snackbar_service.dart';
import '../../../../shared/widgets/round_button.dart';
import '../../data/models/new_exercise_response.dart';
import 'package:path_provider/path_provider.dart';
import '../../../../core/mixpanel_service.dart';
import '../../../../locator.dart';

@RoutePage()
class ImageTextNewPage extends StatefulWidget {
  final Media media;
  final VoidCallback onComplete;
  final VoidCallback tapFavourite;
  final bool isFavorite;
  final VoidCallback onBackPress;

  const ImageTextNewPage(
      {super.key,
      required this.media,
      required this.onComplete,
      required this.tapFavourite,
      required this.isFavorite,
      required this.onBackPress});

  @override
  State<ImageTextNewPage> createState() => _ImageTextNewPageState();
}

class _ImageTextNewPageState extends State<ImageTextNewPage> {
  bool _isFavorite = false;
  // Create screenshot key inside the class
  final GlobalKey _screenshotKey = GlobalKey();

  @override
  void initState() {
    super.initState();
    _isFavorite = widget.isFavorite;
    // Mixpanel screen view tracking
    sl<MixpanelService>().trackScreenView(
      'Image Card Page',
      properties: {
        'Code': 'screen_view.image_card_page',
        'Exercise ID': widget.media.id ?? '',
        'Title': widget.media.title ?? '',
      },
    );
  }

  Widget _buildAppBar(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 8.0),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          IconButton(
            icon: const Icon(
              Icons.arrow_back_ios,
              color: AppColors.navy,
              size: 18,
            ),
            onPressed: () => widget.onBackPress(),
          ),
          const Spacer(),
          Row(
            children: [
              _buildShareButton(),
              const Gap(8),
              _buildFavoriteButton(),
            ],
          ),
        ],
      ),
    );
  }

  Future<void> _captureAndShareScreenshot() async {
    try {
      // Add delay to ensure rendering is complete
      await Future.delayed(const Duration(milliseconds: 300));

      RenderRepaintBoundary? boundary = _screenshotKey.currentContext
          ?.findRenderObject() as RenderRepaintBoundary?;

      if (boundary == null) {
        info('Error: Could not find RepaintBoundary');
        return;
      }

      // Capture the image with high quality
      ui.Image image = await boundary.toImage(pixelRatio: 3.0);
      ByteData? byteData =
          await image.toByteData(format: ui.ImageByteFormat.png);

      if (byteData == null) {
        info('Error: Failed to get byte data from image');
        return;
      }

      Uint8List pngBytes = byteData.buffer.asUint8List();

      // Save to temporary file with unique filename
      final tempDir = await getTemporaryDirectory();
      final timestamp = DateTime.now().millisecondsSinceEpoch;
      File file =
          await File('${tempDir.path}/screenshot_$timestamp.png').create();
      await file.writeAsBytes(pngBytes);

      // Share the file
      await Share.shareXFiles(
        [XFile(file.path)],
        text: 'From an exercise in the Mental Fitness Gym',
        sharePositionOrigin: Rect.fromLTWH(
          0,
          0,
          MediaQuery.of(context).size.width,
          MediaQuery.of(context).size.height / 2,
        ),
      );
      // Mixpanel share event
      sl<MixpanelService>().trackEvent('Share Exercise', properties: {
        'Exercise ID': widget.media.id ?? '',
        'Title': widget.media.title ?? '',
      });
    } catch (e) {
      SnackBarService.error(
        context: context,
        message: 'Error taking screenshot.',
      );
    }
  }

  Widget _buildShareButton() {
    return RoundButton(
      onToggle: _captureAndShareScreenshot,
      child: Image.asset(
        AppAssets.shareiconnew,
        color: AppColors.navy,
        width: 18,
      ),
    );
  }

  Widget _buildFavoriteButton() {
    return RoundButton(
      onToggle: () {
        setState(() {
          _isFavorite = !_isFavorite;
        });
        widget.tapFavourite();
        // Mixpanel favorite event
        sl<MixpanelService>().trackEvent('Toggle Favorite', properties: {
          'Exercise ID': widget.media.id ?? '',
          'Is Favorite': _isFavorite,
          'Title': widget.media.title ?? '',
        });
        // Add favorite functionality here
      },
      child: _isFavorite
          ? const Icon(
              Icons.favorite,
              color: Colors.red,
              size: 18,
            )
          : const Icon(
              Icons.favorite_outline,
              color: AppColors.navy,
              size: 18,
            ),
    );
  }

  Widget _buildContinueButton() {
    return GestureDetector(
      onTap: () {
        widget.onComplete();
        // Mixpanel continue event
        sl<MixpanelService>().trackButtonClick('Tap to Continue', properties: {
          'Exercise ID': widget.media.id ?? '',
          'Title': widget.media.title ?? '',
          'Page': 'Image Card Page',
        });
      },
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Text(
            'Tap to continue ',
            style: Theme.of(context).textTheme.linkText,
          ),
          const Icon(
            Icons.arrow_forward,
            size: 14,
          )
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return PopScope(
        canPop: false, // Disable default back navigation
        onPopInvoked: (didPop) {
          if (didPop) return;
          // Custom back button logic here
          widget.onBackPress();
        },
        child: Scaffold(
          appBar: AppBar(
            elevation: 0,
            toolbarHeight: 0,
            systemOverlayStyle: const SystemUiOverlayStyle(
              statusBarColor: Colors.white,
              statusBarIconBrightness: Brightness.dark,
              statusBarBrightness: Brightness.light,
              systemNavigationBarIconBrightness: Brightness.dark,
              systemNavigationBarColor: Colors.white,
            ),
          ),
          body: SafeArea(
            child: Padding(
              padding: const EdgeInsets.fromLTRB(8, 0, 8, 8),
              child: Column(
                children: [
                  _buildAppBar(context),
                  Expanded(
                    child: RepaintBoundary(
                        key: _screenshotKey,
                        child:
                            // CachedNetworkImage(
                            //   imageUrl: widget.media.mediaUrl ?? '',
                            //   imageBuilder: (context, imageProvider) => Container(
                            //     decoration: BoxDecoration(
                            //       borderRadius: BorderRadius.circular(20),
                            //       image: DecorationImage(
                            //         image: imageProvider,
                            //         fit: BoxFit.cover,
                            //       ),
                            //     ),
                            //   ),placeholder: (context,url)=> LoadingWidget(color: Colors.white),
                            //   ),

                            Container(
                          width: double.infinity,
                          decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(20),
                          ),
                          child: ClipRRect(
                            borderRadius: BorderRadius.circular(20),
                            child: NetworkImageWithIndicator(
                              imageUrl: widget.media.mediaUrl ?? '',
                              fit: BoxFit.cover,
                            ),
                          ),
                        )),
                  ),
                  const Gap(24),
                  _buildContinueButton(),
                  const Gap(8),
                ],
              ),
            ),
          ),
        ));
  }
}
