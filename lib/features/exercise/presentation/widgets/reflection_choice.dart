import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:gotcha_mfg_app/config/theme/app_colors.dart';
import 'package:gotcha_mfg_app/core/extensions/app_text_theme_extension.dart';

class ReflectionChoice extends StatelessWidget {
  const ReflectionChoice({
    super.key,
    required this.isSelected,
    required this.title,
    required this.onTap,
  });

  final bool isSelected;
  final String title;
  final VoidCallback onTap;

  @override
  Widget build(BuildContext context) {
    // Calculate if text will likely need two lines
    final TextStyle textStyle =
        Theme.of(context).textTheme.ralewaySemiBold.copyWith(
              fontSize: 14,
              fontStyle: FontStyle.normal,
            );
    final TextPainter textPainter = TextPainter(
      text: TextSpan(text: title, style: textStyle),
      maxLines: 1,
      textDirection: TextDirection.ltr,
    )..layout(maxWidth: MediaQuery.of(context).size.width * 0.7);

    final bool isMultiLine = textPainter.didExceedMaxLines;

    return GestureDetector(
      onTap: onTap,
      child: Container(
        margin: const EdgeInsets.symmetric(vertical: 8),
        padding: const EdgeInsets.symmetric(horizontal: 8),
        decoration: BoxDecoration(
          color: isSelected ? AppColors.lightRed : Colors.white,
          borderRadius: BorderRadius.circular(30),
          border: Border.all(
            color: isSelected ? AppColors.coral : Colors.white,
            width: isSelected ? 1.5 : 0,
          ),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: isMultiLine
              ? CrossAxisAlignment.center
              : CrossAxisAlignment.center,
          children: [
            Padding(
              padding: EdgeInsets.only(
                  top: isMultiLine ? 4 : 0, bottom: isMultiLine ? 4 : 0),
              child: Transform.scale(
                scale: 1,
                child: Radio(
                  materialTapTargetSize: MaterialTapTargetSize.shrinkWrap,
                  groupValue: isSelected,
                  value: true,
                  activeColor: Colors.red,
                  onChanged: (_) => onTap(),
                ),
              ),
            ),
            Flexible(
              child: Padding(
                // Add vertical padding only for multi-line text
                padding:
                    EdgeInsets.symmetric(vertical: isMultiLine ? 8.0 : 0.0),
                child: Text(
                  title,
                  style: textStyle,
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                ),
              ),
            ),
            const Gap(8),
          ],
        ),
      ),
    );
  }
}
