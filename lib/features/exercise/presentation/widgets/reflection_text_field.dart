// import 'package:flutter/material.dart';
// import 'package:gotcha_mfg_app/config/theme/app_colors.dart';
// import 'package:gotcha_mfg_app/core/extensions/app_text_theme_extension.dart';

// class ReflectionTextField extends StatefulWidget {
//   TextEditingController textFieldController = TextEditingController();
//   final Function(String) onChanged;

//   ReflectionTextField(
//     this.textFieldController, {
//     required this.onChanged,
//     super.key,
//   });

//   @override
//   State<ReflectionTextField> createState() => _ReflectionTextFieldState();
// }

// class _ReflectionTextFieldState extends State<ReflectionTextField> {
//   @override
//   Widget build(BuildContext context) {
//     var border = OutlineInputBorder(
//       borderRadius: BorderRadius.circular(22),
//       borderSide: const BorderSide(
//         color: Colors.white,
//         width: 0,
//       ),
//     );
//     return Container(
//       margin: const EdgeInsets.symmetric(horizontal: 24),
//       height: 122,
//       decoration: BoxDecoration(
//         borderRadius: BorderRadius.circular(22),
//         color: Colors.white,
//       ),
//       width: MediaQuery.of(context).size.width,
//       child: TextField(
//         controller: widget.textFieldController,
//         onChanged: widget.onChanged,
//         style: Theme.of(context).textTheme.ralewayBold.copyWith(
//               fontSize: 14,
//               color: AppColors.navy,
//             ),
//         maxLines: null, // Allows the TextField to expand vertically
//         minLines: 1,
//         decoration: InputDecoration(
//           hintText: 'Type here...',
//           isDense: false,
//           // contentPadding: const EdgeInsets.symmetric(horizontal: 16),
//           enabledBorder: border,
//           focusedBorder: border,
//           border: border,
//           contentPadding:
//               const EdgeInsets.only( top: isIos ? 4 : 8, bottom: 8, left: 16, right: 16),
//         ),
//       ),
//     );
//   }
// }

import 'package:flutter/material.dart';
import 'package:gotcha_mfg_app/config/theme/app_colors.dart';
import 'package:gotcha_mfg_app/core/extensions/app_text_theme_extension.dart';

import '../../../../core/utils/platform_utils.dart';

class ReflectionTextField extends StatefulWidget {
  final TextTheme texttheme;
  TextEditingController textFieldController = TextEditingController();
  final Function(String) onChanged;

  ReflectionTextField(
    this.textFieldController, {
    required this.onChanged,
    super.key,
    required this.texttheme,
  });

  @override
  State<ReflectionTextField> createState() => _ReflectionTextFieldState();
}

class _ReflectionTextFieldState extends State<ReflectionTextField> {
  @override
  Widget build(BuildContext context) {
    var border = OutlineInputBorder(
      borderRadius: BorderRadius.circular(22),
      borderSide: const BorderSide(
        color: Colors.white,
        width: 0,
      ),
    );
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 24),
      height: 122,
      decoration: BoxDecoration(
        border: Border.all(color: AppColors.midBlue, width: 1.5),
        borderRadius: BorderRadius.circular(14),
        color: Colors.white,
      ),
      width: MediaQuery.of(context).size.width,
      child: TextField(
        controller: widget.textFieldController,
        onChanged: widget.onChanged,
        style: Theme.of(context).textTheme.ralewayMedium.copyWith(
              fontSize: 18,
              color: AppColors.navy,
            ),
        maxLines: null, // Allows the TextField to expand vertically
        minLines: 1,
        textInputAction: TextInputAction.done, // Add this line
        decoration: InputDecoration(
          labelStyle: Theme.of(context).textTheme.ralewayMedium.copyWith(
                fontSize: 18,
                color: AppColors.midBlue,
              ),
          hintText: 'Type here...',
          hintStyle: Theme.of(context).textTheme.ralewayMedium.copyWith(
                fontSize: 18,
                color: AppColors.midBlue,
              ),
          isDense: false,
          // contentPadding: const EdgeInsets.symmetric(horizontal: 16),
          enabledBorder: border,
          focusedBorder: border,
          border: border,
          contentPadding: EdgeInsets.only(
            top: isIos ? 4 : 8,
            bottom: 8,
            left: 16,
            right: 16,
          ),
        ),
      ),
    );
  }
}
