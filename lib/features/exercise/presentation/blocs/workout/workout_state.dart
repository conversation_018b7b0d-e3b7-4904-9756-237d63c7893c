part of 'workout_cubit.dart';

sealed class WorkoutState extends Equatable {
  const WorkoutState();

  @override
  List<Object> get props => [];
}

final class WorkoutCubitInitial extends WorkoutState {}

final class WorkoutLoading extends WorkoutState {}

final class WorkoutLoaded extends WorkoutState {
  final WorkoutResponseModel? workoutResponse;

  const WorkoutLoaded({required this.workoutResponse});

  @override
  List<Object> get props => [
        workoutResponse ?? '',
      ];
}

final class WorkoutError extends WorkoutState {
  final String message;

  const WorkoutError(this.message);

  @override
  List<Object> get props => [message];
}
