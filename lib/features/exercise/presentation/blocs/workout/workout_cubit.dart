import 'package:bloc/bloc.dart';
import 'package:equatable/equatable.dart';
import 'package:gotcha_mfg_app/features/exercise/data/models/workout_response_model.dart';

import '../../../domain/usecases/get_workout_usecase.dart';

part 'workout_state.dart';

class WorkoutCubit extends Cubit<WorkoutState> {
  WorkoutCubit(
    this._getWorkoutUsecase,
  ) : super(WorkoutCubitInitial());
  final GetSingleWorkoutUsecase _getWorkoutUsecase;

  // final GetFilteredFavouriteUseCase _getWorkoutsUseCase;
  Future<void> getWorkouts(GetWorkoutParams params) async {
    emit(WorkoutLoading());
    final workout = await _getWorkoutUsecase.call(params);
    if (workout.isSuccess && workout.data?.data?.workout?.workouts != null) {
      final sortedWorkouts = [...workout.data!.data!.workout!.workouts!]..sort(
          (a, b) => (a.orderOfExercise ?? 0).compareTo(b.orderOfExercise ?? 0));

      workout.data!.data!.workout!.workouts = sortedWorkouts;
      emit(WorkoutLoaded(
        workoutResponse: workout.data!,
      ));
    } else {
      emit(WorkoutError(workout.error!));
    }
  }
}
