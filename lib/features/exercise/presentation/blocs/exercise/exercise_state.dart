part of 'exercise_cubit.dart';

sealed class ExerciseState {
  const ExerciseState();

  @override
  List<Object> get props => [];
}

final class ExerciseInitial extends ExerciseState {}

final class ExerciseLoading extends ExerciseState {}

final class ExerciseLoaded extends ExerciseState {
  final NewExerciseResponse? data;

  const ExerciseLoaded(this.data);
}

final class NewExerciseLoaded extends ExerciseState {
  final NewExerciseResponse? data;

  const NewExerciseLoaded(this.data);
}

final class ExerciseUpLoaded extends ExerciseState {
  final PostExerciseResponse? data;
  const ExerciseUpLoaded(this.data);
}

final class FavUpLoaded extends ExerciseState {
  const FavUpLoaded();
}

final class ExerciseError extends ExerciseState {
  final String error;

  const ExerciseError(this.error);
}

final class CheckInSuccess extends ExerciseState {}

final class ExerciseStatusUpdated extends ExerciseState {}

final class SeriesStatusUpdated extends ExerciseState {}
