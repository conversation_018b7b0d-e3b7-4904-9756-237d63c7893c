import 'package:bloc/bloc.dart';
import 'package:gotcha_mfg_app/features/exercise/domain/usecases/update_series_use_case.dart';
import 'package:gotcha_mfg_app/features/exercise/domain/usecases/update_status_use_case.dart';

import '../../../../favourites/data/models/favourite_request_model.dart';
import '../../../../favourites/domain/usecases/add_favourites_usecases.dart';
import '../../../data/models/get_exercise_response.dart';
import '../../../data/models/new_exercise_response.dart';
import '../../../data/models/post_exercise_response.dart';
import '../../../domain/usecases/post_exercise_use_case.dart';

part 'exercise_state.dart';

class ExerciseCubit extends Cubit<ExerciseState> {
  ExerciseCubit(
    this._getExerciseUsecase,
    this._postExerciseUsecase,
    this._updateExerciseStatusUseCase,
    this._updateSeriesStatusUseCase,
    this._addFavouritesUseCase,
  ) : super(ExerciseInitial());

  final GetDetailExerciseUsecase _getExerciseUsecase;
  final PostDetailExerciseUsecase _postExerciseUsecase;
  final UpdateExerciseStatusUseCase _updateExerciseStatusUseCase;
  final UpdateSeriesStatusUseCase _updateSeriesStatusUseCase;
  final AddFavoritesUseCase _addFavouritesUseCase;

  Future<void> getExerciseResponse(
    String id,
    String seriesId,
  ) async {
    emit(ExerciseLoading());
    final result = await _getExerciseUsecase(ExerciseDetailParams(
      id: id,
      seriesId: seriesId,
    ));

    if (result.isSuccess) {
      emit(ExerciseLoaded(result.data));
    } else {
      emit(ExerciseError(result.error!));
    }
  }

  Future<void> postExerciseResponse(
    PostExerciseDetailParams params,
    FavouriteRequest request,
    bool isFav,
  ) async {
    // emit(ExerciseLoading());
    // if (isFav) {
    //   final favresult = await _addFavouritesUseCase.call(request);
    //   if (favresult.isSuccess) {
    //     emit(const FavUpLoaded());
    //   }
    //   return;
    // }

    final result = await _postExerciseUsecase(params);

    if (result.isSuccess) {
      emit(ExerciseUpLoaded(result.data!));
    } else {
      emit(ExerciseError(result.error!));
    }
  }

  Future<void> updateExerciseStatus(UpdateStatusParams params) async {
    final result = await _updateExerciseStatusUseCase.call(params);
    if (result.isSuccess) {
      // emit(ExerciseStatusUpdated());
    } else {
      // emit(ExerciseError(result.error!));
    }
  }

  Future<void> updateSeriesStatus(UpdateSeriesParams params) async {
    emit(ExerciseLoading());
    final result = await _updateSeriesStatusUseCase.call(params);
    if (result.isSuccess) {
      // emit(SeriesStatusUpdated());
    } else {
      // emit(ExerciseError(result.error!));
    }
  }

  Future<void> getMixedExerciseResponse(
    String id,
    String seriesId,
  ) async {
    emit(ExerciseLoading());
    final result = await _getExerciseUsecase(ExerciseDetailParams(
      id: id,
      seriesId: seriesId,
    ));

    if (result.isSuccess) {
      emit(ExerciseLoaded(result.data));
    } else {
      emit(ExerciseError(result.error!));
    }
  }
}
