part of 'reflection_data_cubit.dart';

sealed class ReflectionDataState extends Equatable {
  const ReflectionDataState();

  @override
  List<Object> get props => [];
}

final class ReflectionDataInitial extends ReflectionDataState {}

final class ReflectionDataLoading extends ReflectionDataState {}

final class ReflectionDataLoaded extends ReflectionDataState {
  final List<Answer>? answers;

  const ReflectionDataLoaded({
    required this.answers,
  });

  ReflectionDataLoaded copyWith({
    List<Answer>? answers,
  }) {
    return ReflectionDataLoaded(
      answers: answers ?? this.answers,
    );
  }

  @override
  List<Object> get props => [answers ?? ''];
}
