import 'package:bloc/bloc.dart';
import 'package:equatable/equatable.dart';
import 'package:gotcha_mfg_app/features/exercise/data/models/reflection_request.dart';

part 'reflection_data_state.dart';

class ReflectionDataCubit extends Cubit<ReflectionDataState> {
  ReflectionDataCubit() : super(const ReflectionDataLoaded(answers: []));

  void addAnswer(Answer answer) {
    var previousState = state as ReflectionDataLoaded;
    if (previousState.answers?.any((a) => a.questionId == answer.questionId) ==
        true) {
      previousState.answers
          ?.removeWhere((a) => a.questionId == answer.questionId);
    }
    emit(previousState.copyWith(answers: [...?previousState.answers, answer]));
  }

  void removeAnswer(Answer answer) {
    var previousState = state as ReflectionDataLoaded;
    emit(previousState.copyWith(
        answers: previousState.answers
            ?.where((a) => a.questionId != answer.questionId)
            .toList()));
  }

  // is Selected
  bool isSelected(String choiceId) {
    var previousState = state as ReflectionDataLoaded;
    return previousState.answers?.any((a) => a.selectedChoiceId == choiceId) ==
        true;
  }

  void clearAnswers() {
    emit(const ReflectionDataLoaded(answers: []));
  }
}
