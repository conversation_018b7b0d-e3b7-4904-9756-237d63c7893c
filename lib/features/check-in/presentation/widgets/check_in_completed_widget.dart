// import 'dart:developer';

// import 'package:flutter/material.dart';
// import 'package:gotcha_mfg_app/config/theme/app_assets.dart';
// import 'package:gotcha_mfg_app/core/extensions/app_text_theme_extension.dart';
// import '../../../../config/theme/app_colors.dart';

// class CheckInCompleteView extends StatefulWidget {
//   final String emotion;
//   final String description;
//   final VoidCallback onEdit;
//   final DateTime? dateTime;

//   const CheckInCompleteView({
//     super.key,
//     required this.emotion,
//     required this.onEdit,
//     required this.dateTime,
//     required this.description,
//   });

//   @override
//   State<CheckInCompleteView> createState() => _CheckInCompleteViewState();
// }

// class _CheckInCompleteViewState extends State<CheckInCompleteView> {
//   bool _isExpanded = false;
//   late DateTime? _localDateTime; // Store the localized DateTime

//   @override
//   void initState() {
//     super.initState();
//     _localDateTime = widget.dateTime; // Convert to local on init
//     _checkDateTimeAndSetExpansion();
//   }

//   @override
//   void didUpdateWidget(covariant CheckInCompleteView oldWidget) {
//     super.didUpdateWidget(oldWidget);
//     // Check if dateTime has changed and update accordingly
//     if (widget.dateTime != oldWidget.dateTime) {
//       _localDateTime = widget.dateTime?.toLocal(); // Update localized DateTime
//       _checkDateTimeAndSetExpansion();
//     }
//   }

//   void _checkDateTimeAndSetExpansion() {
//     if (widget.dateTime != null) {
//       log("qwerty");
//       final now = DateTime.now();
//       final difference = now.difference(_localDateTime!);
//       print(
//           'local--------$_localDateTime-----${difference.inSeconds}-------$now');

//       if (difference.inSeconds < 10) {
//         setState(() {
//           _isExpanded = true;
//         });

//         // Start timer only when the widget is first expanded
//         _startCollapseTimer();
//       } else {
//         //Make sure it is not expanded if the difference is >=30
//         setState(() {
//           _isExpanded = false;
//         });
//       }
//     }
//   }

//   void _startCollapseTimer() {
//     // Calculate the remaining time until collapse
//     final now = DateTime.now();
//     final difference = now.difference(_localDateTime!);
//     final remainingTime = Duration(seconds: 10 - difference.inSeconds);

//     Future.delayed(remainingTime, () {
//       if (mounted && _isExpanded) {
//         setState(() {
//           _isExpanded = false;
//         });
//       }
//     });
//   }

//   @override
//   Widget build(BuildContext context) {
//     print('description-------${widget.description}');
//     final textTheme = Theme.of(context).textTheme;

//     return GestureDetector(
//       onTap: () {
//         setState(() {
//           _isExpanded = !_isExpanded;
//         });
//       },
//       child: Container(
//         padding: const EdgeInsets.symmetric(horizontal: 24),
//         decoration: BoxDecoration(
//           borderRadius: BorderRadius.circular(30),
//         ),
//         child: Column(
//           crossAxisAlignment: CrossAxisAlignment.start,
//           mainAxisSize: MainAxisSize.min,
//           children: [
//             Row(
//               children: [
//                 Image.asset(
//                   AppAssets.completetick,
//                   scale: 4.5,
//                 ),
//                 // const Icon(
//                 //   Icons.check_circle_outline,
//                 //   color: AppColors.navy,
//                 //   size: 20,
//                 // ),
//                 const SizedBox(width: 8),
//                 Expanded(
//                   child: Text(
//                     'Check-in complete',
//                     style: textTheme.bodyEmphasis,
//                   ),
//                 ),
//                 TextButton(
//                   onPressed: widget.onEdit,
//                   child: Text(
//                     'Edit check-in',
//                     style: textTheme.linkText.copyWith(
//                       color: AppColors.coral,
//                     ),
//                   ),
//                 ),
//               ],
//             ),
//             AnimatedCrossFade(
//               firstChild: const SizedBox.shrink(),
//               secondChild: Column(
//                 crossAxisAlignment: CrossAxisAlignment.start,
//                 children: [
//                   const SizedBox(height: 8),
//                   Text(
//                     widget.description,
//                     style: textTheme.bodyRegular.copyWith(
//                       color: AppColors.navy,
//                     ),
//                   ),
//                   // const SizedBox(height: 8),
//                   // Text(
//                   //   "Checking in with your emotions is a great first step. It can be tough to acknowledge how you're feeling, but it takes a lot of courage.",
//                   //   style: textTheme.bodyRegular.copyWith(
//                   //     color: AppColors.navy,
//                   //   ),
//                   // ),
//                   const SizedBox(height: 16),
//                   Text(
//                     "We've recommended you a few new exercises below",
//                     style: textTheme.labels.copyWith(
//                       color: AppColors.navy,
//                     ),
//                   ),
//                 ],
//               ),
//               crossFadeState: _isExpanded
//                   ? CrossFadeState.showSecond
//                   : CrossFadeState.showFirst,
//               duration: const Duration(milliseconds: 300),
//               firstCurve: Curves.easeOut,
//               secondCurve: Curves.easeIn,
//               sizeCurve: Curves.easeInOut,
//             ),
//           ],
//         ),
//       ),
//     );
//   }
// }

import 'dart:async';

import 'package:flutter/material.dart';
import 'package:gotcha_mfg_app/config/theme/app_assets.dart';
import 'package:gotcha_mfg_app/core/extensions/app_text_theme_extension.dart';
import '../../../../config/theme/app_colors.dart';
import '../../../../core/utils/app_print.dart';

class CheckInCompleteView extends StatefulWidget {
  final String emotion;
  final String description;
  final VoidCallback onEdit;
  final DateTime? dateTime;

  const CheckInCompleteView({
    super.key,
    required this.emotion,
    required this.onEdit,
    required this.dateTime,
    required this.description,
  });

  @override
  State<CheckInCompleteView> createState() => _CheckInCompleteViewState();
}

class _CheckInCompleteViewState extends State<CheckInCompleteView> {
  bool _isExpanded = false;
  late DateTime? _checkInTime; // Store the check-in time

  @override
  void initState() {
    super.initState();
    _checkInTime = widget.dateTime; // Initialize with the provided DateTime
    _checkDateTimeAndSetExpansion();
  }

  @override
  void didUpdateWidget(covariant CheckInCompleteView oldWidget) {
    super.didUpdateWidget(oldWidget);
    // Check if dateTime has changed and update accordingly
    if (widget.dateTime != oldWidget.dateTime) {
      _checkInTime = widget.dateTime;
      _checkDateTimeAndSetExpansion();
    }
  }

  void _checkDateTimeAndSetExpansion() {
    if (_checkInTime != null) {
      info("qwerty");
      final now = DateTime.now();

      // Create a new DateTime object that ignores the timezone
      final checkInTimeWithoutTZ = DateTime(
        _checkInTime!.year,
        _checkInTime!.month,
        _checkInTime!.day,
        _checkInTime!.hour,
        _checkInTime!.minute,
        _checkInTime!.second,
        _checkInTime!.millisecond,
        _checkInTime!.microsecond,
      );

      final difference = now.difference(checkInTimeWithoutTZ);

      if (difference.inSeconds < 10) {
        setState(() {
          _isExpanded = true;
        });

        // Start timer only when the widget is first expanded
        _startCollapseTimer();
      } else {
        //Make sure it is not expanded if the difference is >= 10
        setState(() {
          _isExpanded = false;
        });
      }
    }
  }

  void _startCollapseTimer() {
    // Calculate the remaining time until collapse.
    final now = DateTime.now();
    final checkInTimeWithoutTZ = DateTime(
      _checkInTime!.year,
      _checkInTime!.month,
      _checkInTime!.day,
      _checkInTime!.hour,
      _checkInTime!.minute,
      _checkInTime!.second,
      _checkInTime!.millisecond,
      _checkInTime!.microsecond,
    );

    final difference = now.difference(checkInTimeWithoutTZ);
    final remainingTime = Duration(seconds: 10 - difference.inSeconds);

    // Ensure the delay is non-negative.
    final actualDelay =
        remainingTime.isNegative ? Duration.zero : remainingTime;

    Future.delayed(actualDelay, () {
      if (mounted && _isExpanded) {
        setState(() {
          _isExpanded = false;
        });
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    final textTheme = Theme.of(context).textTheme;

    return GestureDetector(
      onTap: () {
        setState(() {
          _isExpanded = !_isExpanded;
        });
      },
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 24),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(30),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisSize: MainAxisSize.min,
          children: [
            Row(
              children: [
                Image.asset(
                  AppAssets.completetick,
                  scale: 4.5,
                ),
                // const Icon(
                //   Icons.check_circle_outline,
                //   color: AppColors.navy,
                //   size: 20,
                // ),
                const SizedBox(width: 8),
                Expanded(
                  child: Text(
                    'Check-in complete',
                    style: textTheme.ralewaySemiBold.copyWith(
                      fontSize: 17,
                    ),
                  ),
                ),
                TextButton(
                  onPressed: widget.onEdit,
                  child: Text(
                    'Edit check-in',
                    style: textTheme.linkText.copyWith(
                      color: AppColors.coral,
                    ),
                  ),
                ),
              ],
            ),
            AnimatedCrossFade(
              firstChild: const SizedBox.shrink(),
              secondChild: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const SizedBox(height: 8),
                  Text(
                    widget.description,
                    style: textTheme.bodyRegular.copyWith(
                      color: AppColors.navy,
                    ),
                  ),
                  // const SizedBox(height: 8),
                  // Text(
                  //   "Checking in with your emotions is a great first step. It can be tough to acknowledge how you're feeling, but it takes a lot of courage.",
                  //   style: textTheme.bodyRegular.copyWith(
                  //     color: AppColors.navy,
                  //   ),
                  // ),
                  const SizedBox(height: 20),
                  // Text(
                  //   "We've recommended you a few new exercises below",
                  //   style: textTheme.labels.copyWith(
                  //     color: AppColors.navy,
                  //   ),
                  // ),
                ],
              ),
              crossFadeState: _isExpanded
                  ? CrossFadeState.showSecond
                  : CrossFadeState.showFirst,
              duration: const Duration(milliseconds: 300),
              firstCurve: Curves.easeOut,
              secondCurve: Curves.easeIn,
              sizeCurve: Curves.easeInOut,
            ),
          ],
        ),
      ),
    );
  }
}
