import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:gotcha_mfg_app/config/theme/app_colors.dart';
import 'package:gotcha_mfg_app/core/extensions/app_text_theme_extension.dart';

class FeedBackHeader extends StatelessWidget {
  const FeedBackHeader({
    required this.textTheme,
    required this.index,
    required this.onTap,
    required this.totalBars,
    super.key,
  });

  final TextTheme textTheme;
  final int index; // Current step index
  final VoidCallback onTap;
  final int totalBars; // Total number of progress bars

  @override
  Widget build(BuildContext context) {
    final textTheme = Theme.of(context).textTheme;
    final size = MediaQuery.sizeOf(context);
    return Container(
      padding: const EdgeInsets.symmetric(
        horizontal: 16,
        vertical: 14,
      ),
      decoration: const BoxDecoration(
        color: AppColors.navy,
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(30),
          topRight: Radius.circular(30),
        ),
      ),
      child: Row(
        children: [
          Padding(
            padding: const EdgeInsets.all(8.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Feedback',
                  style: textTheme.gothamBold.copyWith(
                    fontSize: 20,
                    color: Colors.white,
                  ),
                ),
              ],
            ),
          ),
          const Spacer(),
          // Dynamically generate progress bars
          Row(
            children: List.generate(
              totalBars,
              (i) {
                return Row(
                  children: [
                    Container(
                      height: 4,
                      width: size.width * 0.12,
                      decoration: BoxDecoration(
                        color: i <= index ? AppColors.coral : AppColors.grey,
                        borderRadius: BorderRadius.circular(10),
                      ),
                    ),
                    if (i < totalBars - 1) const Gap(4), // Add gap between bars
                  ],
                );
              },
            ),
          ),
        ],
      ),
    );
  }
}
