// FeatureState.dart
import '../../../data/models/feedback_submit_response.dart';

abstract class FeedbackState {}

class FeedbackInitial extends FeedbackState {}

class FeedbackLoading extends FeedbackState {}

class FeedbackLoaded extends FeedbackState {
  FeedbackLoaded(this.feedbackSubmitResponse);
  final FeedbackSubmitResponse feedbackSubmitResponse;
}

class FeedbackError extends FeedbackState {
  FeedbackError(this.message);
  final String message;
}
