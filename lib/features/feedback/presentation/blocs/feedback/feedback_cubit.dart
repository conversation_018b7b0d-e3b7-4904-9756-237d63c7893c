// FeatureCubit.dart
import 'package:flutter_bloc/flutter_bloc.dart';

import '../../../data/models/add_feedback.dart';
import '../../../domain/usecases/add_feedback_usecase.dart';
import 'feedback_state.dart';

class FeedbackCubit extends Cubit<FeedbackState> {
  FeedbackCubit(this._addFeedbackUseCase) : super(FeedbackInitial());

  final AddFeedbackUseCase _addFeedbackUseCase;

  Future<void> addFeedback(AddFeedbackData request) async {
    emit(FeedbackLoading());
    final result = await _addFeedbackUseCase.call(request);

    if (result.isSuccess) {
      emit(FeedbackLoaded(result.data!));
    } else {
      emit(FeedbackError(result.error!));
    }
  }
}
