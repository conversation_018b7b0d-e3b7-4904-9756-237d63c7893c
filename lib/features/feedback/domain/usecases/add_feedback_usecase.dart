// ApiCallNameUseCase.dart

import '../../../../core/usecase/usecase.dart';
import '../../../../shared/models/result.dart';
import '../../data/models/add_feedback.dart';
import '../../data/models/feedback_submit_response.dart';
import '../repositories/feedback_repository.dart';

class AddFeedbackUseCase
    implements UseCase<Result<FeedbackSubmitResponse>, AddFeedbackData> {
  AddFeedbackUseCase(this._repository);

  final FeedbackRepository _repository;

  @override
  Future<Result<FeedbackSubmitResponse>> call(AddFeedbackData params) async {
    return _repository.addFeedback(params);
  }
}
