// FeatureRemoteDataSource.dart
import 'package:dio/dio.dart';

import '../../../../shared/models/result.dart';
import '../models/add_feedback.dart';
import '../models/feedback_submit_response.dart';

abstract class FeedbackRemoteDataSource {
  Future<Result<FeedbackSubmitResponse>> addFeedback(AddFeedbackData request);
}

// FeatureRemoteDataSourceImpl.dart

class FeedbackRemoteDataSourceImpl implements FeedbackRemoteDataSource {
  FeedbackRemoteDataSourceImpl(this._dio);

  final Dio _dio;

  @override
  Future<Result<FeedbackSubmitResponse>> addFeedback(
      AddFeedbackData request) async {
    try {
      final response = await _dio.post(
        '/app/feedback',
        data: request.toJson(),
      );

      if (response.statusCode == 200) {
        final data = response.data;
        final feedbackSubmitResponse = FeedbackSubmitResponse.fromJson(data);
        return Result.success(feedbackSubmitResponse);
      } else {
        return Result.failure(
            '${response.statusCode} - ${response.data['message']}');
      }
    } catch (e) {
      return Result.failure(
          'An error occurred during adding feedback: ${e.toString()}');
    }
  }
}
