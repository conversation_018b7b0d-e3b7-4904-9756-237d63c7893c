// FeatureRepositoryImpl.dart

import '../../../../shared/models/result.dart';
import '../../domain/repositories/feedback_repository.dart';
import '../data_sources/feedback_datasource.dart';
import '../models/add_feedback.dart';
import '../models/feedback_submit_response.dart';

class FeedbackRepositoryImpl implements FeedbackRepository {
  FeedbackRepositoryImpl(this._remoteDataSource);

  final FeedbackRemoteDataSource _remoteDataSource;

  @override
  Future<Result<FeedbackSubmitResponse>> addFeedback(
      AddFeedbackData request) async {
    return await _remoteDataSource.addFeedback(request);
  }
}
