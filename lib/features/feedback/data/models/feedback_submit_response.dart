import 'dart:convert';

class FeedbackSubmitResponse {
  final String? message;
  final String? status;

  FeedbackSubmitResponse({
    this.message,
    this.status,
  });

  FeedbackSubmitResponse copyWith({
    String? message,
    String? status,
  }) =>
      FeedbackSubmitResponse(
        message: message ?? this.message,
        status: status ?? this.status,
      );

  factory FeedbackSubmitResponse.fromRawJson(String str) =>
      FeedbackSubmitResponse.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory FeedbackSubmitResponse.fromJson(Map<String, dynamic> json) =>
      FeedbackSubmitResponse(
        message: json["message"],
        status: json["status"],
      );

  Map<String, dynamic> toJson() => {
        "message": message,
        "status": status,
      };
}
