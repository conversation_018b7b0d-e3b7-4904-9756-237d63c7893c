class AddFeedbackData {
  List<FeedbackDataItem> feedbackData;

  AddFeedbackData({required this.feedbackData});

  factory AddFeedbackData.fromJson(Map<String, dynamic> json) {
    var feedbackDataList = json['feedback_data'] as List;
    List<FeedbackDataItem> feedbackItems = feedbackDataList
        .map((item) => FeedbackDataItem.fromJson(item))
        .toList();
    return AddFeedbackData(
      feedbackData: feedbackItems,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'feedback_data': feedbackData.map((item) => item.toJson()).toList(),
    };
  }
}

class FeedbackDataItem {
  String question;
  String answer;
  String questionType;

  FeedbackDataItem({
    required this.question,
    required this.answer,
    required this.questionType,
  });

  factory FeedbackDataItem.fromJson(Map<String, dynamic> json) {
    return FeedbackDataItem(
      question: json['question'] as String,
      answer: json['answer'] as String,
      questionType: json['question_type'] as String,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'question': question,
      'answer': answer,
      'question_type': questionType,
    };
  }
}
