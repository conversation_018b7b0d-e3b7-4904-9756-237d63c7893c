name: gotcha_mfg_app
description: "Flutter-based mobile application designed to support the Gotcha4Life initiative."

publish_to: 'none'

version: 1.0.5+36

environment:
  sdk: '>=3.4.1 <4.0.0'

dependencies:
  cupertino_icons: ^1.0.6
  flutter:
    sdk: flutter
  dio: ^5.7.0
  pretty_dio_logger: ^1.4.0
  dio_smart_retry: ^6.0.0
  flutter_secure_storage: ^9.2.2
  shared_preferences: ^2.2.2
  flutter_bloc: ^8.1.6
  intl: ^0.19.0
  cached_network_image: ^3.4.1
  flutter_svg: ^2.0.10+1
  get_it: ^8.0.0
  equatable: ^2.0.5
  internet_connection_checker_plus: ^2.5.2
  gap: ^3.0.1
  bloc: ^8.1.4
  flutter_native_splash: ^2.4.1
  rename: ^3.0.2
  icons_launcher: ^3.0.0
  auto_route: ^9.2.2
  uuid: ^4.5.1
  logger: ^2.4.0
  toastification: ^2.3.0
  lottie: ^3.1.3
  youtube_player_flutter: ^9.1.1
  timelines_plus: ^1.0.4
  dotted_border: ^2.1.0
  wheel_picker: ^0.2.1
  webview_flutter: ^4.10.0
  url_launcher: ^6.3.1
  skeletonizer: ^1.4.3
  mixpanel_flutter: ^2.3.3
  easy_debounce: ^2.0.3
  firebase_crashlytics: ^4.3.5
  firebase_core: ^3.13.0
  firebase_messaging: ^15.2.5
  flutter_local_notifications: ^18.0.1
  share_plus: ^10.1.4
  device_info_plus: ^11.3.0
  visibility_detector: ^0.4.0+2
  flutter_timezone: ^3.0.1
  permission_handler: ^11.4.0
  video_player: ^2.9.3
  readmore: ^2.2.0
  volume_controller: ^2.0.7
  custom_refresh_indicator: ^4.0.1
  package_info_plus: ^8.3.0
  android_id: ^0.4.0
  firebase_analytics: ^11.4.5
  
dev_dependencies:
  auto_route_generator: ^9.0.0
  build_runner:
  flutter_lints: ^3.0.0
  flutter_test:
    sdk: flutter
  very_good_analysis: ^6.0.0

flutter:
  uses-material-design: true
  assets:
    - assets/images/
    - assets/fonts/
    - assets/animations/

  fonts:
    - family: Gotham-Bold
      fonts:
        - asset: assets/fonts/gotham/Gotham-Bold.otf

    - family: Gotham-Medium
      fonts:
        - asset: assets/fonts/gotham/Gotham-Medium.otf

    - family: Raleway
      fonts:
        - asset: assets/fonts/raleway/Raleway-Bold.ttf
          weight: 700
        - asset: assets/fonts/raleway/Raleway-SemiBold.ttf
          weight: 600
        - asset: assets/fonts/raleway/Raleway-Medium.ttf
          weight: 500
        - asset: assets/fonts/raleway/Raleway-Regular.ttf
          weight: 400
        - asset: assets/fonts/raleway/Raleway-Light.ttf
          weight: 300  

flutter_native_splash:
  color: "#FF584F"
  image: assets/images/g4l_icon.png
  android_12:
    color: "#FF584F"
    image: assets/images/g4l_icon.png

icons_launcher:
  image_path: "assets/images/icon.png"
  platforms:
    android:
      # adaptive_background_color: '#FF584F'
      # adaptive_foreground_image: "assets/images/icon.png"
      enable: true
    ios:
      enable: true
   