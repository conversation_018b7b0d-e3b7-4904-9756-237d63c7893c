# Gotcha4Life MFG

Gotcha4Life MFG is a Flutter-based mobile application designed to support the Gotcha4Life initiative, which focuses on mental health and well-being. Our app provides users with resources, tools, and a community to foster connection and support.

## Table of Contents

- [Installation](#installation)
- [Features](#features)
- [Project Structure](#project-structure)
- [Coding Guidelines](#coding-guidelines)
- [Commit Rules](#commit-rules)
- [Branching Strategy](#branching-strategy)
- [Contributing](#contributing)
- [License](#license)

## Installation

To get a local copy up and running, follow these steps:

### Prerequisites

- [Flutter SDK](https://flutter.dev/docs/get-started/install)
- A suitable IDE (VSCode or Android Studio)
- Android or iOS device/emulator

### Setup

1. **Clone the repository**:
   ```bash
   https://github.com/gotcha-mfg/app
   ```
2. **Navigate to the project directory**:
   ```bash
   cd gotcha_mfg_app
   ```
3. **Install dependencies**:
   ```bash
   flutter pub get
   ```
4. **Run the app**:
   ```bash
   flutter run
   ```

## Features

- Auth
  - Auth feature 1
  - Auth feature 2

## Project Structure

```plaintext
lib/
├── core/                 # Core utilities, constants, and shared code
├── config/               # Configuration files
├── features/             # Feature-specific components
│   └── feature_1         # Presentation for feature 1
│       ├── data/         # Repositories, data sources, and models for feature 1
│       ├── domain/       # Business logic and use cases for feature 1
│       └── presentation/ # UI code, screens, and widgets for feature 1
├── main.dart             # Main entry point of the application
└── di.dart               # Dependency injection
```

## Commit Rules

To maintain a clean and understandable Git history, follow these commit rules:

**1. Commit Messages**:

- Use [Conventional Commits](https://www.conventionalcommits.org/en/v1.0.0/) for writing commit messages:
  - `feat`: A new feature.
  - `fix`: A bug fix.
  - `docs`: Documentation changes.
  - `style`: Code style changes (formatting, missing semi-colons, etc.).
  - `refactor`: Code restructuring without changing external behavior.
  - `test`: Adding or refactoring tests.
  - `chore`: Other minor changes (build process, etc.).
  - `WIP`: Work in progress.

 Example:

 ```bash
feat(login): add email login with Firebase

chore: initial commit
```

**2. Commit Frequency**: Commit often and keep changes small. Avoid large "catch-all" commits.

**3. Atomic Commits**: Each commit should focus on one task or issue, making it easier to review and roll back if necessary.

## Branching Strategy

We follow the **Git Flow** branching model:

- **main**: Stable, production-ready code.
- **develop**: The main branch for new feature development.
- **feature/xxx**: Branch for working on specific features.
- **hotfix/xxx**: Quick fixes for issues found in production.
- **release/xxx**: Prepares a new production release.

### Creating a New Feature Branch

**1. Create a new branch**:
   ```bash
   git checkout -b feature/your-feature-name
   ```
**2. Commit changes** following the [commit rules](#commit-rules).

**3. Open a pull request** to the `develop` branch.